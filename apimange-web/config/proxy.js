const { createProxyMiddleware } = require("http-proxy-middleware");

module.exports = function (app) {
  app.use(
    "^/api/bsp",
    createProxyMiddleware({
      // target: 'http://**************:24680',
      target: "http://**************:8082",
      // target: "http://***********:8080",
      changeOrigin: true,
      pathRewrite: {
        "^/api": "",
      },
    })
  );
  app.use(
    "^/backapimgt",
    createProxyMiddleware({
      target: "http://***********:24680",
      // target: "http://***********:8080",
      // target: "http://***********:24680",
      changeOrigin: true,
      pathRewrite: {
        "^/backapimgt": "",
      },
    })
  );
  app.use(
    "^/apipus",
    createProxyMiddleware({
      // target: "http://***************:30777",
      // target: "http://***********:8080",
      target: "https://hxgywebtestout.cd120.info:18443/",
      changeOrigin: true,
      pathRewrite: {
        "^/apipus": "/apipus",
      },
    })
  );
  app.use(
    "^/fanyi",
    createProxyMiddleware({
      target: "https://fanyi-api.baidu.com",
      // target: "http://***********:8080",
      // target: "http://***********:24680",
      changeOrigin: true,
      pathRewrite: {
        "^/fanyi": "",
      },
    })
  );
};
