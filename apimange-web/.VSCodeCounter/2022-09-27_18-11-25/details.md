# Details

Date : 2022-09-27 18:11:25

Directory /Users/<USER>/Desktop/work/apimange-web

Total : 1386 files,  837606 codes, 85494 comments, 19195 blanks, all 942295 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.prettierrc.js](/.prettierrc.js) | JavaScript | 3 | 0 | 1 | 4 |
| [README.md](/README.md) | Markdown | 1 | 0 | 1 | 2 |
| [commitlint.config.js](/commitlint.config.js) | JavaScript | 17 | 0 | 1 | 18 |
| [config/env.js](/config/env.js) | JavaScript | 68 | 40 | 13 | 121 |
| [config/getHttpsConfig.js](/config/getHttpsConfig.js) | JavaScript | 51 | 7 | 9 | 67 |
| [config/jest/babelTransform.js](/config/jest/babelTransform.js) | JavaScript | 25 | 0 | 5 | 30 |
| [config/jest/cssTransform.js](/config/jest/cssTransform.js) | JavaScript | 9 | 3 | 3 | 15 |
| [config/jest/fileTransform.js](/config/jest/fileTransform.js) | JavaScript | 31 | 4 | 6 | 41 |
| [config/modules.js](/config/modules.js) | JavaScript | 80 | 30 | 25 | 135 |
| [config/paths.js](/config/paths.js) | JavaScript | 55 | 10 | 13 | 78 |
| [config/pnpTs.js](/config/pnpTs.js) | JavaScript | 32 | 0 | 4 | 36 |
| [config/proxy.js](/config/proxy.js) | JavaScript | 23 | 4 | 2 | 29 |
| [config/theme.js](/config/theme.js) | JavaScript | 3 | 0 | 1 | 4 |
| [config/webpack.config.js](/config/webpack.config.js) | JavaScript | 566 | 233 | 27 | 826 |
| [config/webpackDevServer.config.js](/config/webpackDevServer.config.js) | JavaScript | 65 | 74 | 7 | 146 |
| [mock/index.js](/mock/index.js) | JavaScript | 19 | 0 | 5 | 24 |
| [mock/login.js](/mock/login.js) | JavaScript | 27 | 1 | 4 | 32 |
| [package.json](/package.json) | JSON | 220 | 0 | 1 | 221 |
| [public/index.html](/public/index.html) | HTML | 13 | 0 | 4 | 17 |
| [public/manifest.json](/public/manifest.json) | JSON | 25 | 0 | 1 | 26 |
| [public/monaco-editor/CHANGELOG.md](/public/monaco-editor/CHANGELOG.md) | Markdown | 954 | 0 | 401 | 1,355 |
| [public/monaco-editor/README.md](/public/monaco-editor/README.md) | Markdown | 55 | 0 | 42 | 97 |
| [public/monaco-editor/dev/bundleInfo.json](/public/monaco-editor/dev/bundleInfo.json) | JSON | 7,259 | 0 | 0 | 7,259 |
| [public/monaco-editor/dev/nls.metadata.json](/public/monaco-editor/dev/nls.metadata.json) | JSON | 3,654 | 0 | 0 | 3,654 |
| [public/monaco-editor/dev/vs/base/worker/workerMain.js](/public/monaco-editor/dev/vs/base/worker/workerMain.js) | JavaScript | 11,039 | 2,334 | 44 | 13,417 |
| [public/monaco-editor/dev/vs/basic-languages/abap/abap.js](/public/monaco-editor/dev/vs/basic-languages/abap/abap.js) | JavaScript | 1,317 | 13 | 2 | 1,332 |
| [public/monaco-editor/dev/vs/basic-languages/apex/apex.js](/public/monaco-editor/dev/vs/basic-languages/apex/apex.js) | JavaScript | 300 | 36 | 1 | 337 |
| [public/monaco-editor/dev/vs/basic-languages/azcli/azcli.js](/public/monaco-editor/dev/vs/basic-languages/azcli/azcli.js) | JavaScript | 83 | 7 | 2 | 92 |
| [public/monaco-editor/dev/vs/basic-languages/bat/bat.js](/public/monaco-editor/dev/vs/basic-languages/bat/bat.js) | JavaScript | 104 | 7 | 2 | 113 |
| [public/monaco-editor/dev/vs/basic-languages/bicep/bicep.js](/public/monaco-editor/dev/vs/basic-languages/bicep/bicep.js) | JavaScript | 97 | 35 | 1 | 133 |
| [public/monaco-editor/dev/vs/basic-languages/cameligo/cameligo.js](/public/monaco-editor/dev/vs/basic-languages/cameligo/cameligo.js) | JavaScript | 177 | 7 | 2 | 186 |
| [public/monaco-editor/dev/vs/basic-languages/clojure/clojure.js](/public/monaco-editor/dev/vs/basic-languages/clojure/clojure.js) | JavaScript | 763 | 7 | 2 | 772 |
| [public/monaco-editor/dev/vs/basic-languages/coffee/coffee.js](/public/monaco-editor/dev/vs/basic-languages/coffee/coffee.js) | JavaScript | 237 | 7 | 2 | 246 |
| [public/monaco-editor/dev/vs/basic-languages/cpp/cpp.js](/public/monaco-editor/dev/vs/basic-languages/cpp/cpp.js) | JavaScript | 363 | 40 | 2 | 405 |
| [public/monaco-editor/dev/vs/basic-languages/csharp/csharp.js](/public/monaco-editor/dev/vs/basic-languages/csharp/csharp.js) | JavaScript | 296 | 40 | 2 | 338 |
| [public/monaco-editor/dev/vs/basic-languages/csp/csp.js](/public/monaco-editor/dev/vs/basic-languages/csp/csp.js) | JavaScript | 66 | 7 | 2 | 75 |
| [public/monaco-editor/dev/vs/basic-languages/css/css.js](/public/monaco-editor/dev/vs/basic-languages/css/css.js) | JavaScript | 193 | 7 | 2 | 202 |
| [public/monaco-editor/dev/vs/basic-languages/dart/dart.js](/public/monaco-editor/dev/vs/basic-languages/dart/dart.js) | JavaScript | 219 | 68 | 1 | 288 |
| [public/monaco-editor/dev/vs/basic-languages/dockerfile/dockerfile.js](/public/monaco-editor/dev/vs/basic-languages/dockerfile/dockerfile.js) | JavaScript | 141 | 7 | 2 | 150 |
| [public/monaco-editor/dev/vs/basic-languages/ecl/ecl.js](/public/monaco-editor/dev/vs/basic-languages/ecl/ecl.js) | JavaScript | 448 | 23 | 1 | 472 |
| [public/monaco-editor/dev/vs/basic-languages/elixir/elixir.js](/public/monaco-editor/dev/vs/basic-languages/elixir/elixir.js) | JavaScript | 476 | 7 | 2 | 485 |
| [public/monaco-editor/dev/vs/basic-languages/flow9/flow9.js](/public/monaco-editor/dev/vs/basic-languages/flow9/flow9.js) | JavaScript | 135 | 23 | 1 | 159 |
| [public/monaco-editor/dev/vs/basic-languages/fsharp/fsharp.js](/public/monaco-editor/dev/vs/basic-languages/fsharp/fsharp.js) | JavaScript | 219 | 7 | 2 | 228 |
| [public/monaco-editor/dev/vs/basic-languages/go/go.js](/public/monaco-editor/dev/vs/basic-languages/go/go.js) | JavaScript | 195 | 30 | 1 | 226 |
| [public/monaco-editor/dev/vs/basic-languages/graphql/graphql.js](/public/monaco-editor/dev/vs/basic-languages/graphql/graphql.js) | JavaScript | 150 | 7 | 2 | 159 |
| [public/monaco-editor/dev/vs/basic-languages/handlebars/handlebars.js](/public/monaco-editor/dev/vs/basic-languages/handlebars/handlebars.js) | JavaScript | 420 | 10 | 5 | 435 |
| [public/monaco-editor/dev/vs/basic-languages/hcl/hcl.js](/public/monaco-editor/dev/vs/basic-languages/hcl/hcl.js) | JavaScript | 159 | 35 | 1 | 195 |
| [public/monaco-editor/dev/vs/basic-languages/html/html.js](/public/monaco-editor/dev/vs/basic-languages/html/html.js) | JavaScript | 296 | 10 | 5 | 311 |
| [public/monaco-editor/dev/vs/basic-languages/ini/ini.js](/public/monaco-editor/dev/vs/basic-languages/ini/ini.js) | JavaScript | 77 | 7 | 2 | 86 |
| [public/monaco-editor/dev/vs/basic-languages/java/java.js](/public/monaco-editor/dev/vs/basic-languages/java/java.js) | JavaScript | 205 | 33 | 1 | 239 |
| [public/monaco-editor/dev/vs/basic-languages/javascript/javascript.js](/public/monaco-editor/dev/vs/basic-languages/javascript/javascript.js) | JavaScript | 285 | 146 | 4 | 435 |
| [public/monaco-editor/dev/vs/basic-languages/julia/julia.js](/public/monaco-editor/dev/vs/basic-languages/julia/julia.js) | JavaScript | 505 | 7 | 2 | 514 |
| [public/monaco-editor/dev/vs/basic-languages/kotlin/kotlin.js](/public/monaco-editor/dev/vs/basic-languages/kotlin/kotlin.js) | JavaScript | 231 | 31 | 1 | 263 |
| [public/monaco-editor/dev/vs/basic-languages/less/less.js](/public/monaco-editor/dev/vs/basic-languages/less/less.js) | JavaScript | 176 | 7 | 2 | 185 |
| [public/monaco-editor/dev/vs/basic-languages/lexon/lexon.js](/public/monaco-editor/dev/vs/basic-languages/lexon/lexon.js) | JavaScript | 160 | 7 | 2 | 169 |
| [public/monaco-editor/dev/vs/basic-languages/liquid/liquid.js](/public/monaco-editor/dev/vs/basic-languages/liquid/liquid.js) | JavaScript | 267 | 10 | 5 | 282 |
| [public/monaco-editor/dev/vs/basic-languages/lua/lua.js](/public/monaco-editor/dev/vs/basic-languages/lua/lua.js) | JavaScript | 166 | 7 | 2 | 175 |
| [public/monaco-editor/dev/vs/basic-languages/m3/m3.js](/public/monaco-editor/dev/vs/basic-languages/m3/m3.js) | JavaScript | 219 | 7 | 2 | 228 |
| [public/monaco-editor/dev/vs/basic-languages/markdown/markdown.js](/public/monaco-editor/dev/vs/basic-languages/markdown/markdown.js) | JavaScript | 211 | 7 | 2 | 220 |
| [public/monaco-editor/dev/vs/basic-languages/mips/mips.js](/public/monaco-editor/dev/vs/basic-languages/mips/mips.js) | JavaScript | 203 | 7 | 2 | 212 |
| [public/monaco-editor/dev/vs/basic-languages/msdax/msdax.js](/public/monaco-editor/dev/vs/basic-languages/msdax/msdax.js) | JavaScript | 386 | 7 | 2 | 395 |
| [public/monaco-editor/dev/vs/basic-languages/mysql/mysql.js](/public/monaco-editor/dev/vs/basic-languages/mysql/mysql.js) | JavaScript | 884 | 7 | 2 | 893 |
| [public/monaco-editor/dev/vs/basic-languages/objective-c/objective-c.js](/public/monaco-editor/dev/vs/basic-languages/objective-c/objective-c.js) | JavaScript | 197 | 7 | 2 | 206 |
| [public/monaco-editor/dev/vs/basic-languages/pascal/pascal.js](/public/monaco-editor/dev/vs/basic-languages/pascal/pascal.js) | JavaScript | 254 | 7 | 2 | 263 |
| [public/monaco-editor/dev/vs/basic-languages/pascaligo/pascaligo.js](/public/monaco-editor/dev/vs/basic-languages/pascaligo/pascaligo.js) | JavaScript | 167 | 7 | 2 | 176 |
| [public/monaco-editor/dev/vs/basic-languages/perl/perl.js](/public/monaco-editor/dev/vs/basic-languages/perl/perl.js) | JavaScript | 608 | 7 | 2 | 617 |
| [public/monaco-editor/dev/vs/basic-languages/pgsql/pgsql.js](/public/monaco-editor/dev/vs/basic-languages/pgsql/pgsql.js) | JavaScript | 852 | 7 | 2 | 861 |
| [public/monaco-editor/dev/vs/basic-languages/php/php.js](/public/monaco-editor/dev/vs/basic-languages/php/php.js) | JavaScript | 480 | 7 | 2 | 489 |
| [public/monaco-editor/dev/vs/basic-languages/pla/pla.js](/public/monaco-editor/dev/vs/basic-languages/pla/pla.js) | JavaScript | 140 | 7 | 2 | 149 |
| [public/monaco-editor/dev/vs/basic-languages/postiats/postiats.js](/public/monaco-editor/dev/vs/basic-languages/postiats/postiats.js) | JavaScript | 548 | 12 | 2 | 562 |
| [public/monaco-editor/dev/vs/basic-languages/powerquery/powerquery.js](/public/monaco-editor/dev/vs/basic-languages/powerquery/powerquery.js) | JavaScript | 899 | 7 | 2 | 908 |
| [public/monaco-editor/dev/vs/basic-languages/powershell/powershell.js](/public/monaco-editor/dev/vs/basic-languages/powershell/powershell.js) | JavaScript | 242 | 7 | 2 | 251 |
| [public/monaco-editor/dev/vs/basic-languages/protobuf/protobuf.js](/public/monaco-editor/dev/vs/basic-languages/protobuf/protobuf.js) | JavaScript | 404 | 34 | 2 | 440 |
| [public/monaco-editor/dev/vs/basic-languages/pug/pug.js](/public/monaco-editor/dev/vs/basic-languages/pug/pug.js) | JavaScript | 402 | 7 | 2 | 411 |
| [public/monaco-editor/dev/vs/basic-languages/python/python.js](/public/monaco-editor/dev/vs/basic-languages/python/python.js) | JavaScript | 284 | 10 | 5 | 299 |
| [public/monaco-editor/dev/vs/basic-languages/qsharp/qsharp.js](/public/monaco-editor/dev/vs/basic-languages/qsharp/qsharp.js) | JavaScript | 290 | 7 | 2 | 299 |
| [public/monaco-editor/dev/vs/basic-languages/r/r.js](/public/monaco-editor/dev/vs/basic-languages/r/r.js) | JavaScript | 254 | 7 | 2 | 263 |
| [public/monaco-editor/dev/vs/basic-languages/razor/razor.js](/public/monaco-editor/dev/vs/basic-languages/razor/razor.js) | JavaScript | 541 | 10 | 5 | 556 |
| [public/monaco-editor/dev/vs/basic-languages/redis/redis.js](/public/monaco-editor/dev/vs/basic-languages/redis/redis.js) | JavaScript | 307 | 7 | 2 | 316 |
| [public/monaco-editor/dev/vs/basic-languages/redshift/redshift.js](/public/monaco-editor/dev/vs/basic-languages/redshift/redshift.js) | JavaScript | 815 | 7 | 2 | 824 |
| [public/monaco-editor/dev/vs/basic-languages/restructuredtext/restructuredtext.js](/public/monaco-editor/dev/vs/basic-languages/restructuredtext/restructuredtext.js) | JavaScript | 171 | 7 | 2 | 180 |
| [public/monaco-editor/dev/vs/basic-languages/ruby/ruby.js](/public/monaco-editor/dev/vs/basic-languages/ruby/ruby.js) | JavaScript | 453 | 7 | 2 | 462 |
| [public/monaco-editor/dev/vs/basic-languages/rust/rust.js](/public/monaco-editor/dev/vs/basic-languages/rust/rust.js) | JavaScript | 312 | 42 | 1 | 355 |
| [public/monaco-editor/dev/vs/basic-languages/sb/sb.js](/public/monaco-editor/dev/vs/basic-languages/sb/sb.js) | JavaScript | 119 | 7 | 2 | 128 |
| [public/monaco-editor/dev/vs/basic-languages/scala/scala.js](/public/monaco-editor/dev/vs/basic-languages/scala/scala.js) | JavaScript | 360 | 9 | 2 | 371 |
| [public/monaco-editor/dev/vs/basic-languages/scheme/scheme.js](/public/monaco-editor/dev/vs/basic-languages/scheme/scheme.js) | JavaScript | 123 | 7 | 2 | 132 |
| [public/monaco-editor/dev/vs/basic-languages/scss/scss.js](/public/monaco-editor/dev/vs/basic-languages/scss/scss.js) | JavaScript | 251 | 7 | 2 | 260 |
| [public/monaco-editor/dev/vs/basic-languages/shell/shell.js](/public/monaco-editor/dev/vs/basic-languages/shell/shell.js) | JavaScript | 230 | 7 | 2 | 239 |
| [public/monaco-editor/dev/vs/basic-languages/solidity/solidity.js](/public/monaco-editor/dev/vs/basic-languages/solidity/solidity.js) | JavaScript | 1,340 | 28 | 1 | 1,369 |
| [public/monaco-editor/dev/vs/basic-languages/sophia/sophia.js](/public/monaco-editor/dev/vs/basic-languages/sophia/sophia.js) | JavaScript | 179 | 28 | 1 | 208 |
| [public/monaco-editor/dev/vs/basic-languages/sparql/sparql.js](/public/monaco-editor/dev/vs/basic-languages/sparql/sparql.js) | JavaScript | 202 | 7 | 2 | 211 |
| [public/monaco-editor/dev/vs/basic-languages/sql/sql.js](/public/monaco-editor/dev/vs/basic-languages/sql/sql.js) | JavaScript | 827 | 7 | 2 | 836 |
| [public/monaco-editor/dev/vs/basic-languages/st/st.js](/public/monaco-editor/dev/vs/basic-languages/st/st.js) | JavaScript | 420 | 10 | 2 | 432 |
| [public/monaco-editor/dev/vs/basic-languages/swift/swift.js](/public/monaco-editor/dev/vs/basic-languages/swift/swift.js) | JavaScript | 261 | 10 | 2 | 273 |
| [public/monaco-editor/dev/vs/basic-languages/systemverilog/systemverilog.js](/public/monaco-editor/dev/vs/basic-languages/systemverilog/systemverilog.js) | JavaScript | 517 | 56 | 1 | 574 |
| [public/monaco-editor/dev/vs/basic-languages/tcl/tcl.js](/public/monaco-editor/dev/vs/basic-languages/tcl/tcl.js) | JavaScript | 241 | 7 | 2 | 250 |
| [public/monaco-editor/dev/vs/basic-languages/twig/twig.js](/public/monaco-editor/dev/vs/basic-languages/twig/twig.js) | JavaScript | 332 | 7 | 2 | 341 |
| [public/monaco-editor/dev/vs/basic-languages/typescript/typescript.js](/public/monaco-editor/dev/vs/basic-languages/typescript/typescript.js) | JavaScript | 285 | 80 | 4 | 369 |
| [public/monaco-editor/dev/vs/basic-languages/vb/vb.js](/public/monaco-editor/dev/vs/basic-languages/vb/vb.js) | JavaScript | 373 | 7 | 2 | 382 |
| [public/monaco-editor/dev/vs/basic-languages/xml/xml.js](/public/monaco-editor/dev/vs/basic-languages/xml/xml.js) | JavaScript | 123 | 10 | 5 | 138 |
| [public/monaco-editor/dev/vs/basic-languages/yaml/yaml.js](/public/monaco-editor/dev/vs/basic-languages/yaml/yaml.js) | JavaScript | 177 | 7 | 2 | 186 |
| [public/monaco-editor/dev/vs/editor/editor.main.css](/public/monaco-editor/dev/vs/editor/editor.main.css) | CSS | 3,183 | 476 | 740 | 4,399 |
| [public/monaco-editor/dev/vs/editor/editor.main.js](/public/monaco-editor/dev/vs/editor/editor.main.js) | JavaScript | 129,481 | 12,497 | 2,992 | 144,970 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.de.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.de.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.es.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.es.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.fr.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.fr.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.it.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.it.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.ja.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.ja.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.js) | JavaScript | 1,549 | 9 | 1 | 1,559 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.ko.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.ko.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.ru.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.ru.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.zh-cn.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.zh-cn.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/editor/editor.main.nls.zh-tw.js](/public/monaco-editor/dev/vs/editor/editor.main.nls.zh-tw.js) | JavaScript | 1,549 | 6 | 1 | 1,556 |
| [public/monaco-editor/dev/vs/language/css/cssMode.js](/public/monaco-editor/dev/vs/language/css/cssMode.js) | JavaScript | 1,954 | 13 | 8 | 1,975 |
| [public/monaco-editor/dev/vs/language/css/cssWorker.js](/public/monaco-editor/dev/vs/language/css/cssWorker.js) | JavaScript | 35,437 | 50 | 45 | 35,532 |
| [public/monaco-editor/dev/vs/language/html/htmlMode.js](/public/monaco-editor/dev/vs/language/html/htmlMode.js) | JavaScript | 1,862 | 13 | 8 | 1,883 |
| [public/monaco-editor/dev/vs/language/html/htmlWorker.js](/public/monaco-editor/dev/vs/language/html/htmlWorker.js) | JavaScript | 16,042 | 40 | 35 | 16,117 |
| [public/monaco-editor/dev/vs/language/json/jsonMode.js](/public/monaco-editor/dev/vs/language/json/jsonMode.js) | JavaScript | 2,327 | 193 | 13 | 2,533 |
| [public/monaco-editor/dev/vs/language/json/jsonWorker.js](/public/monaco-editor/dev/vs/language/json/jsonWorker.js) | JavaScript | 7,231 | 34 | 29 | 7,294 |
| [public/monaco-editor/dev/vs/language/typescript/tsMode.js](/public/monaco-editor/dev/vs/language/typescript/tsMode.js) | JavaScript | 1,317 | 15 | 11 | 1,343 |
| [public/monaco-editor/dev/vs/language/typescript/tsWorker.js](/public/monaco-editor/dev/vs/language/typescript/tsWorker.js) | JavaScript | 142,897 | 15,165 | 2,321 | 160,383 |
| [public/monaco-editor/dev/vs/loader.js](/public/monaco-editor/dev/vs/loader.js) | JavaScript | 1,719 | 238 | 2 | 1,959 |
| [public/monaco-editor/esm/metadata.d.ts](/public/monaco-editor/esm/metadata.d.ts) | TypeScript | 14 | 5 | 10 | 29 |
| [public/monaco-editor/esm/metadata.js](/public/monaco-editor/esm/metadata.js) | JavaScript | 544 | 0 | 2 | 546 |
| [public/monaco-editor/esm/vs/base/browser/browser.js](/public/monaco-editor/esm/vs/base/browser/browser.js) | JavaScript | 55 | 9 | 1 | 65 |
| [public/monaco-editor/esm/vs/base/browser/canIUse.js](/public/monaco-editor/esm/vs/base/browser/canIUse.js) | JavaScript | 22 | 9 | 1 | 32 |
| [public/monaco-editor/esm/vs/base/browser/contextmenu.js](/public/monaco-editor/esm/vs/base/browser/contextmenu.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/base/browser/dnd.js](/public/monaco-editor/esm/vs/base/browser/dnd.js) | JavaScript | 21 | 17 | 1 | 39 |
| [public/monaco-editor/esm/vs/base/browser/dom.js](/public/monaco-editor/esm/vs/base/browser/dom.js) | JavaScript | 888 | 107 | 1 | 996 |
| [public/monaco-editor/esm/vs/base/browser/dompurify/dompurify.js](/public/monaco-editor/esm/vs/base/browser/dompurify/dompurify.js) | JavaScript | 772 | 376 | 237 | 1,385 |
| [public/monaco-editor/esm/vs/base/browser/event.js](/public/monaco-editor/esm/vs/base/browser/event.js) | JavaScript | 21 | 4 | 1 | 26 |
| [public/monaco-editor/esm/vs/base/browser/fastDomNode.js](/public/monaco-editor/esm/vs/base/browser/fastDomNode.js) | JavaScript | 199 | 4 | 1 | 204 |
| [public/monaco-editor/esm/vs/base/browser/formattedTextRenderer.js](/public/monaco-editor/esm/vs/base/browser/formattedTextRenderer.js) | JavaScript | 163 | 5 | 1 | 169 |
| [public/monaco-editor/esm/vs/base/browser/globalMouseMoveMonitor.js](/public/monaco-editor/esm/vs/base/browser/globalMouseMoveMonitor.js) | JavaScript | 87 | 12 | 1 | 100 |
| [public/monaco-editor/esm/vs/base/browser/history.js](/public/monaco-editor/esm/vs/base/browser/history.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/base/browser/iframe.js](/public/monaco-editor/esm/vs/base/browser/iframe.js) | JavaScript | 79 | 17 | 1 | 97 |
| [public/monaco-editor/esm/vs/base/browser/keyboardEvent.js](/public/monaco-editor/esm/vs/base/browser/keyboardEvent.js) | JavaScript | 108 | 10 | 1 | 119 |
| [public/monaco-editor/esm/vs/base/browser/markdownRenderer.js](/public/monaco-editor/esm/vs/base/browser/markdownRenderer.js) | JavaScript | 285 | 41 | 1 | 327 |
| [public/monaco-editor/esm/vs/base/browser/mouseEvent.js](/public/monaco-editor/esm/vs/base/browser/mouseEvent.js) | JavaScript | 109 | 16 | 1 | 126 |
| [public/monaco-editor/esm/vs/base/browser/touch.js](/public/monaco-editor/esm/vs/base/browser/touch.js) | JavaScript | 244 | 12 | 1 | 257 |
| [public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionViewItems.js](/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionViewItems.js) | JavaScript | 271 | 21 | 1 | 293 |
| [public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.css](/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.css) | CSS | 86 | 6 | 19 | 111 |
| [public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.js](/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.js) | JavaScript | 366 | 16 | 1 | 383 |
| [public/monaco-editor/esm/vs/base/browser/ui/aria/aria.css](/public/monaco-editor/esm/vs/base/browser/ui/aria/aria.css) | CSS | 4 | 4 | 1 | 9 |
| [public/monaco-editor/esm/vs/base/browser/ui/aria/aria.js](/public/monaco-editor/esm/vs/base/browser/ui/aria/aria.js) | JavaScript | 75 | 13 | 1 | 89 |
| [public/monaco-editor/esm/vs/base/browser/ui/button/button.css](/public/monaco-editor/esm/vs/base/browser/ui/button/button.css) | CSS | 41 | 4 | 11 | 56 |
| [public/monaco-editor/esm/vs/base/browser/ui/button/button.js](/public/monaco-editor/esm/vs/base/browser/ui/button/button.js) | JavaScript | 145 | 1 | 1 | 147 |
| [public/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.css](/public/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.css) | CSS | 41 | 5 | 8 | 54 |
| [public/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.js](/public/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.js) | JavaScript | 99 | 4 | 1 | 104 |
| [public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon/codicon-modifiers.css](/public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon/codicon-modifiers.css) | CSS | 22 | 6 | 6 | 34 |
| [public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon/codicon.css](/public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon/codicon.css) | CSS | 17 | 5 | 4 | 26 |
| [public/monaco-editor/esm/vs/base/browser/ui/codicons/codiconStyles.js](/public/monaco-editor/esm/vs/base/browser/ui/codicons/codiconStyles.js) | JavaScript | 10 | 4 | 1 | 15 |
| [public/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.css](/public/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.css) | CSS | 12 | 4 | 3 | 19 |
| [public/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.js](/public/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.js) | JavaScript | 255 | 20 | 7 | 282 |
| [public/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.css](/public/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.css) | CSS | 18 | 4 | 3 | 25 |
| [public/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.js](/public/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.js) | JavaScript | 54 | 4 | 1 | 59 |
| [public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.css](/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.css) | CSS | 35 | 4 | 8 | 47 |
| [public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.js](/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.js) | JavaScript | 135 | 4 | 1 | 140 |
| [public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdownActionViewItem.js](/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdownActionViewItem.js) | JavaScript | 84 | 5 | 1 | 90 |
| [public/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.css](/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.css) | CSS | 48 | 9 | 8 | 65 |
| [public/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.js](/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.js) | JavaScript | 298 | 5 | 1 | 304 |
| [public/monaco-editor/esm/vs/base/browser/ui/findinput/findInputCheckboxes.js](/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInputCheckboxes.js) | JavaScript | 42 | 4 | 1 | 47 |
| [public/monaco-editor/esm/vs/base/browser/ui/findinput/replaceInput.js](/public/monaco-editor/esm/vs/base/browser/ui/findinput/replaceInput.js) | JavaScript | 241 | 6 | 1 | 248 |
| [public/monaco-editor/esm/vs/base/browser/ui/highlightedlabel/highlightedLabel.js](/public/monaco-editor/esm/vs/base/browser/ui/highlightedlabel/highlightedLabel.js) | JavaScript | 86 | 26 | 1 | 113 |
| [public/monaco-editor/esm/vs/base/browser/ui/hover/hover.css](/public/monaco-editor/esm/vs/base/browser/ui/hover/hover.css) | CSS | 120 | 7 | 28 | 155 |
| [public/monaco-editor/esm/vs/base/browser/ui/hover/hoverWidget.js](/public/monaco-editor/esm/vs/base/browser/ui/hover/hoverWidget.js) | JavaScript | 55 | 4 | 1 | 60 |
| [public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconHoverDelegate.js](/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconHoverDelegate.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabel.js](/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabel.js) | JavaScript | 214 | 4 | 1 | 219 |
| [public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabelHover.js](/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabelHover.js) | JavaScript | 161 | 11 | 1 | 173 |
| [public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabels.js](/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabels.js) | JavaScript | 24 | 4 | 1 | 29 |
| [public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconlabel.css](/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconlabel.css) | CSS | 76 | 9 | 20 | 105 |
| [public/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.css](/public/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.css) | CSS | 85 | 8 | 19 | 112 |
| [public/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.js](/public/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.js) | JavaScript | 517 | 13 | 1 | 531 |
| [public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.css](/public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.css) | CSS | 27 | 4 | 7 | 38 |
| [public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.js](/public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.js) | JavaScript | 124 | 4 | 1 | 129 |
| [public/monaco-editor/esm/vs/base/browser/ui/list/list.css](/public/monaco-editor/esm/vs/base/browser/ui/list/list.css) | CSS | 128 | 9 | 25 | 162 |
| [public/monaco-editor/esm/vs/base/browser/ui/list/list.js](/public/monaco-editor/esm/vs/base/browser/ui/list/list.js) | JavaScript | 5 | 4 | 1 | 10 |
| [public/monaco-editor/esm/vs/base/browser/ui/list/listPaging.js](/public/monaco-editor/esm/vs/base/browser/ui/list/listPaging.js) | JavaScript | 113 | 4 | 1 | 118 |
| [public/monaco-editor/esm/vs/base/browser/ui/list/listView.js](/public/monaco-editor/esm/vs/base/browser/ui/list/listView.js) | JavaScript | 961 | 40 | 1 | 1,002 |
| [public/monaco-editor/esm/vs/base/browser/ui/list/listWidget.js](/public/monaco-editor/esm/vs/base/browser/ui/list/listWidget.js) | JavaScript | 1,341 | 58 | 1 | 1,400 |
| [public/monaco-editor/esm/vs/base/browser/ui/list/rangeMap.js](/public/monaco-editor/esm/vs/base/browser/ui/list/rangeMap.js) | JavaScript | 108 | 37 | 1 | 146 |
| [public/monaco-editor/esm/vs/base/browser/ui/list/rowCache.js](/public/monaco-editor/esm/vs/base/browser/ui/list/rowCache.js) | JavaScript | 66 | 12 | 1 | 79 |
| [public/monaco-editor/esm/vs/base/browser/ui/list/splice.js](/public/monaco-editor/esm/vs/base/browser/ui/list/splice.js) | JavaScript | 8 | 4 | 1 | 13 |
| [public/monaco-editor/esm/vs/base/browser/ui/menu/menu.js](/public/monaco-editor/esm/vs/base/browser/ui/menu/menu.js) | JavaScript | 1,021 | 57 | 63 | 1,141 |
| [public/monaco-editor/esm/vs/base/browser/ui/mouseCursor/mouseCursor.css](/public/monaco-editor/esm/vs/base/browser/ui/mouseCursor/mouseCursor.css) | CSS | 7 | 5 | 3 | 15 |
| [public/monaco-editor/esm/vs/base/browser/ui/mouseCursor/mouseCursor.js](/public/monaco-editor/esm/vs/base/browser/ui/mouseCursor/mouseCursor.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.css](/public/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.css) | CSS | 30 | 14 | 8 | 52 |
| [public/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.js](/public/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.js) | JavaScript | 87 | 15 | 1 | 103 |
| [public/monaco-editor/esm/vs/base/browser/ui/sash/sash.css](/public/monaco-editor/esm/vs/base/browser/ui/sash/sash.css) | CSS | 108 | 5 | 24 | 137 |
| [public/monaco-editor/esm/vs/base/browser/ui/sash/sash.js](/public/monaco-editor/esm/vs/base/browser/ui/sash/sash.js) | JavaScript | 359 | 71 | 1 | 431 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/abstractScrollbar.js](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/abstractScrollbar.js) | JavaScript | 184 | 20 | 1 | 205 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/horizontalScrollbar.js](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/horizontalScrollbar.js) | JavaScript | 84 | 4 | 1 | 89 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/media/scrollbars.css](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/media/scrollbars.css) | CSS | 41 | 7 | 6 | 54 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElement.js](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElement.js) | JavaScript | 454 | 57 | 1 | 512 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElementOptions.js](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElementOptions.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarArrow.js](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarArrow.js) | JavaScript | 64 | 8 | 1 | 73 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarState.js](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarState.js) | JavaScript | 134 | 27 | 1 | 162 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarVisibilityController.js](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarVisibilityController.js) | JavaScript | 86 | 8 | 1 | 95 |
| [public/monaco-editor/esm/vs/base/browser/ui/scrollbar/verticalScrollbar.js](/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/verticalScrollbar.js) | JavaScript | 85 | 6 | 1 | 92 |
| [public/monaco-editor/esm/vs/base/browser/ui/splitview/splitview.css](/public/monaco-editor/esm/vs/base/browser/ui/splitview/splitview.css) | CSS | 54 | 4 | 13 | 71 |
| [public/monaco-editor/esm/vs/base/browser/ui/splitview/splitview.js](/public/monaco-editor/esm/vs/base/browser/ui/splitview/splitview.js) | JavaScript | 639 | 109 | 1 | 749 |
| [public/monaco-editor/esm/vs/base/browser/ui/table/table.css](/public/monaco-editor/esm/vs/base/browser/ui/table/table.css) | CSS | 44 | 9 | 9 | 62 |
| [public/monaco-editor/esm/vs/base/browser/ui/table/table.js](/public/monaco-editor/esm/vs/base/browser/ui/table/table.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/base/browser/ui/table/tableWidget.js](/public/monaco-editor/esm/vs/base/browser/ui/table/tableWidget.js) | JavaScript | 161 | 4 | 1 | 166 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/abstractTree.js](/public/monaco-editor/esm/vs/base/browser/ui/tree/abstractTree.js) | JavaScript | 1,140 | 26 | 1 | 1,167 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/asyncDataTree.js](/public/monaco-editor/esm/vs/base/browser/ui/tree/asyncDataTree.js) | JavaScript | 757 | 21 | 1 | 779 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/compressedObjectTreeModel.js](/public/monaco-editor/esm/vs/base/browser/ui/tree/compressedObjectTreeModel.js) | JavaScript | 337 | 13 | 1 | 351 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/dataTree.js](/public/monaco-editor/esm/vs/base/browser/ui/tree/dataTree.js) | JavaScript | 13 | 4 | 1 | 18 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/indexTreeModel.js](/public/monaco-editor/esm/vs/base/browser/ui/tree/indexTreeModel.js) | JavaScript | 520 | 16 | 1 | 537 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/media/tree.css](/public/monaco-editor/esm/vs/base/browser/ui/tree/media/tree.css) | CSS | 53 | 5 | 12 | 70 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/objectTree.js](/public/monaco-editor/esm/vs/base/browser/ui/tree/objectTree.js) | JavaScript | 126 | 4 | 1 | 131 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/objectTreeModel.js](/public/monaco-editor/esm/vs/base/browser/ui/tree/objectTreeModel.js) | JavaScript | 161 | 4 | 1 | 166 |
| [public/monaco-editor/esm/vs/base/browser/ui/tree/tree.js](/public/monaco-editor/esm/vs/base/browser/ui/tree/tree.js) | JavaScript | 25 | 4 | 1 | 30 |
| [public/monaco-editor/esm/vs/base/browser/ui/widget.js](/public/monaco-editor/esm/vs/base/browser/ui/widget.js) | JavaScript | 37 | 4 | 1 | 42 |
| [public/monaco-editor/esm/vs/base/common/actions.js](/public/monaco-editor/esm/vs/base/common/actions.js) | JavaScript | 156 | 7 | 1 | 164 |
| [public/monaco-editor/esm/vs/base/common/arrays.js](/public/monaco-editor/esm/vs/base/common/arrays.js) | JavaScript | 249 | 68 | 1 | 318 |
| [public/monaco-editor/esm/vs/base/common/assert.js](/public/monaco-editor/esm/vs/base/common/assert.js) | JavaScript | 5 | 7 | 1 | 13 |
| [public/monaco-editor/esm/vs/base/common/async.js](/public/monaco-editor/esm/vs/base/common/async.js) | JavaScript | 652 | 136 | 1 | 789 |
| [public/monaco-editor/esm/vs/base/common/buffer.js](/public/monaco-editor/esm/vs/base/common/buffer.js) | JavaScript | 55 | 2 | 1 | 58 |
| [public/monaco-editor/esm/vs/base/common/cancellation.js](/public/monaco-editor/esm/vs/base/common/cancellation.js) | JavaScript | 98 | 12 | 1 | 111 |
| [public/monaco-editor/esm/vs/base/common/charCode.js](/public/monaco-editor/esm/vs/base/common/charCode.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/base/common/codicons.js](/public/monaco-editor/esm/vs/base/common/codicons.js) | JavaScript | 158 | 15 | 1 | 174 |
| [public/monaco-editor/esm/vs/base/common/collections.js](/public/monaco-editor/esm/vs/base/common/collections.js) | JavaScript | 43 | 8 | 1 | 52 |
| [public/monaco-editor/esm/vs/base/common/color.js](/public/monaco-editor/esm/vs/base/common/color.js) | JavaScript | 395 | 48 | 1 | 444 |
| [public/monaco-editor/esm/vs/base/common/comparers.js](/public/monaco-editor/esm/vs/base/common/comparers.js) | JavaScript | 53 | 16 | 1 | 70 |
| [public/monaco-editor/esm/vs/base/common/decorators.js](/public/monaco-editor/esm/vs/base/common/decorators.js) | JavaScript | 30 | 0 | 1 | 31 |
| [public/monaco-editor/esm/vs/base/common/diff/diff.js](/public/monaco-editor/esm/vs/base/common/diff/diff.js) | JavaScript | 652 | 247 | 1 | 900 |
| [public/monaco-editor/esm/vs/base/common/diff/diffChange.js](/public/monaco-editor/esm/vs/base/common/diff/diffChange.js) | JavaScript | 14 | 18 | 1 | 33 |
| [public/monaco-editor/esm/vs/base/common/errorMessage.js](/public/monaco-editor/esm/vs/base/common/errorMessage.js) | JavaScript | 53 | 11 | 1 | 65 |
| [public/monaco-editor/esm/vs/base/common/errors.js](/public/monaco-editor/esm/vs/base/common/errors.js) | JavaScript | 84 | 11 | 1 | 96 |
| [public/monaco-editor/esm/vs/base/common/event.js](/public/monaco-editor/esm/vs/base/common/event.js) | JavaScript | 483 | 101 | 1 | 585 |
| [public/monaco-editor/esm/vs/base/common/extpath.js](/public/monaco-editor/esm/vs/base/common/extpath.js) | JavaScript | 118 | 33 | 1 | 152 |
| [public/monaco-editor/esm/vs/base/common/filters.js](/public/monaco-editor/esm/vs/base/common/filters.js) | JavaScript | 617 | 76 | 1 | 694 |
| [public/monaco-editor/esm/vs/base/common/functional.js](/public/monaco-editor/esm/vs/base/common/functional.js) | JavaScript | 13 | 4 | 1 | 18 |
| [public/monaco-editor/esm/vs/base/common/fuzzyScorer.js](/public/monaco-editor/esm/vs/base/common/fuzzyScorer.js) | JavaScript | 116 | 22 | 1 | 139 |
| [public/monaco-editor/esm/vs/base/common/glob.js](/public/monaco-editor/esm/vs/base/common/glob.js) | JavaScript | 453 | 46 | 1 | 500 |
| [public/monaco-editor/esm/vs/base/common/hash.js](/public/monaco-editor/esm/vs/base/common/hash.js) | JavaScript | 239 | 19 | 1 | 259 |
| [public/monaco-editor/esm/vs/base/common/history.js](/public/monaco-editor/esm/vs/base/common/history.js) | JavaScript | 69 | 4 | 1 | 74 |
| [public/monaco-editor/esm/vs/base/common/htmlContent.js](/public/monaco-editor/esm/vs/base/common/htmlContent.js) | JavaScript | 92 | 5 | 1 | 98 |
| [public/monaco-editor/esm/vs/base/common/iconLabels.js](/public/monaco-editor/esm/vs/base/common/iconLabels.js) | JavaScript | 97 | 24 | 1 | 122 |
| [public/monaco-editor/esm/vs/base/common/idGenerator.js](/public/monaco-editor/esm/vs/base/common/idGenerator.js) | JavaScript | 10 | 4 | 1 | 15 |
| [public/monaco-editor/esm/vs/base/common/iterator.js](/public/monaco-editor/esm/vs/base/common/iterator.js) | JavaScript | 134 | 15 | 1 | 150 |
| [public/monaco-editor/esm/vs/base/common/jsonSchema.js](/public/monaco-editor/esm/vs/base/common/jsonSchema.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/base/common/keyCodes.js](/public/monaco-editor/esm/vs/base/common/keyCodes.js) | JavaScript | 349 | 24 | 1 | 374 |
| [public/monaco-editor/esm/vs/base/common/keybindingLabels.js](/public/monaco-editor/esm/vs/base/common/keybindingLabels.js) | JavaScript | 97 | 17 | 1 | 115 |
| [public/monaco-editor/esm/vs/base/common/keybindings.js](/public/monaco-editor/esm/vs/base/common/keybindings.js) | JavaScript | 92 | 13 | 1 | 106 |
| [public/monaco-editor/esm/vs/base/common/labels.js](/public/monaco-editor/esm/vs/base/common/labels.js) | JavaScript | 25 | 5 | 1 | 31 |
| [public/monaco-editor/esm/vs/base/common/lazy.js](/public/monaco-editor/esm/vs/base/common/lazy.js) | JavaScript | 24 | 13 | 1 | 38 |
| [public/monaco-editor/esm/vs/base/common/lifecycle.js](/public/monaco-editor/esm/vs/base/common/lifecycle.js) | JavaScript | 214 | 34 | 1 | 249 |
| [public/monaco-editor/esm/vs/base/common/linkedList.js](/public/monaco-editor/esm/vs/base/common/linkedList.js) | JavaScript | 113 | 11 | 1 | 125 |
| [public/monaco-editor/esm/vs/base/common/map.js](/public/monaco-editor/esm/vs/base/common/map.js) | JavaScript | 984 | 58 | 1 | 1,043 |
| [public/monaco-editor/esm/vs/base/common/marked/marked.js](/public/monaco-editor/esm/vs/base/common/marked/marked.js) | JavaScript | 2,055 | 419 | 520 | 2,994 |
| [public/monaco-editor/esm/vs/base/common/marshalling.js](/public/monaco-editor/esm/vs/base/common/marshalling.js) | JavaScript | 35 | 5 | 1 | 41 |
| [public/monaco-editor/esm/vs/base/common/mime.js](/public/monaco-editor/esm/vs/base/common/mime.js) | JavaScript | 163 | 28 | 1 | 192 |
| [public/monaco-editor/esm/vs/base/common/navigator.js](/public/monaco-editor/esm/vs/base/common/navigator.js) | JavaScript | 30 | 4 | 1 | 35 |
| [public/monaco-editor/esm/vs/base/common/network.js](/public/monaco-editor/esm/vs/base/common/network.js) | JavaScript | 100 | 48 | 1 | 149 |
| [public/monaco-editor/esm/vs/base/common/numbers.js](/public/monaco-editor/esm/vs/base/common/numbers.js) | JavaScript | 17 | 4 | 1 | 22 |
| [public/monaco-editor/esm/vs/base/common/objects.js](/public/monaco-editor/esm/vs/base/common/objects.js) | JavaScript | 150 | 9 | 1 | 160 |
| [public/monaco-editor/esm/vs/base/common/paging.js](/public/monaco-editor/esm/vs/base/common/paging.js) | JavaScript | 1 | 0 | 1 | 2 |
| [public/monaco-editor/esm/vs/base/common/path.js](/public/monaco-editor/esm/vs/base/common/path.js) | JavaScript | 1,120 | 259 | 1 | 1,380 |
| [public/monaco-editor/esm/vs/base/common/platform.js](/public/monaco-editor/esm/vs/base/common/platform.js) | JavaScript | 132 | 26 | 1 | 159 |
| [public/monaco-editor/esm/vs/base/common/process.js](/public/monaco-editor/esm/vs/base/common/process.js) | JavaScript | 30 | 25 | 1 | 56 |
| [public/monaco-editor/esm/vs/base/common/range.js](/public/monaco-editor/esm/vs/base/common/range.js) | JavaScript | 36 | 8 | 1 | 45 |
| [public/monaco-editor/esm/vs/base/common/resources.js](/public/monaco-editor/esm/vs/base/common/resources.js) | JavaScript | 205 | 22 | 1 | 228 |
| [public/monaco-editor/esm/vs/base/common/scrollable.js](/public/monaco-editor/esm/vs/base/common/scrollable.js) | JavaScript | 289 | 27 | 1 | 317 |
| [public/monaco-editor/esm/vs/base/common/search.js](/public/monaco-editor/esm/vs/base/common/search.js) | JavaScript | 44 | 5 | 1 | 50 |
| [public/monaco-editor/esm/vs/base/common/sequence.js](/public/monaco-editor/esm/vs/base/common/sequence.js) | JavaScript | 1 | 0 | 1 | 2 |
| [public/monaco-editor/esm/vs/base/common/severity.js](/public/monaco-editor/esm/vs/base/common/severity.js) | JavaScript | 41 | 8 | 1 | 50 |
| [public/monaco-editor/esm/vs/base/common/stopwatch.js](/public/monaco-editor/esm/vs/base/common/stopwatch.js) | JavaScript | 24 | 4 | 1 | 29 |
| [public/monaco-editor/esm/vs/base/common/strings.js](/public/monaco-editor/esm/vs/base/common/strings.js) | JavaScript | 567 | 187 | 1 | 755 |
| [public/monaco-editor/esm/vs/base/common/styler.js](/public/monaco-editor/esm/vs/base/common/styler.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/base/common/types.js](/public/monaco-editor/esm/vs/base/common/types.js) | JavaScript | 108 | 40 | 1 | 149 |
| [public/monaco-editor/esm/vs/base/common/uint.js](/public/monaco-editor/esm/vs/base/common/uint.js) | JavaScript | 18 | 4 | 1 | 23 |
| [public/monaco-editor/esm/vs/base/common/uri.js](/public/monaco-editor/esm/vs/base/common/uri.js) | JavaScript | 437 | 152 | 1 | 590 |
| [public/monaco-editor/esm/vs/base/common/uuid.js](/public/monaco-editor/esm/vs/base/common/uuid.js) | JavaScript | 45 | 6 | 1 | 52 |
| [public/monaco-editor/esm/vs/base/common/worker/simpleWorker.js](/public/monaco-editor/esm/vs/base/common/worker/simpleWorker.js) | JavaScript | 407 | 34 | 1 | 442 |
| [public/monaco-editor/esm/vs/base/parts/quickinput/browser/media/quickInput.css](/public/monaco-editor/esm/vs/base/parts/quickinput/browser/media/quickInput.css) | CSS | 230 | 10 | 52 | 292 |
| [public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInput.js](/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInput.js) | JavaScript | 1,268 | 32 | 1 | 1,301 |
| [public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputBox.js](/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputBox.js) | JavaScript | 85 | 4 | 1 | 90 |
| [public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputList.js](/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputList.js) | JavaScript | 595 | 26 | 1 | 622 |
| [public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputUtils.js](/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputUtils.js) | JavaScript | 22 | 4 | 1 | 27 |
| [public/monaco-editor/esm/vs/base/parts/quickinput/common/quickInput.js](/public/monaco-editor/esm/vs/base/parts/quickinput/common/quickInput.js) | JavaScript | 14 | 14 | 1 | 29 |
| [public/monaco-editor/esm/vs/base/parts/storage/common/storage.js](/public/monaco-editor/esm/vs/base/parts/storage/common/storage.js) | JavaScript | 167 | 23 | 1 | 191 |
| [public/monaco-editor/esm/vs/base/worker/defaultWorkerFactory.js](/public/monaco-editor/esm/vs/base/worker/defaultWorkerFactory.js) | JavaScript | 76 | 31 | 1 | 108 |
| [public/monaco-editor/esm/vs/basic-languages/_.contribution.js](/public/monaco-editor/esm/vs/basic-languages/_.contribution.js) | JavaScript | 64 | 8 | 4 | 76 |
| [public/monaco-editor/esm/vs/basic-languages/abap/abap.contribution.js](/public/monaco-editor/esm/vs/basic-languages/abap/abap.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/abap/abap.js](/public/monaco-editor/esm/vs/basic-languages/abap/abap.js) | JavaScript | 1,303 | 13 | 2 | 1,318 |
| [public/monaco-editor/esm/vs/basic-languages/apex/apex.contribution.js](/public/monaco-editor/esm/vs/basic-languages/apex/apex.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/apex/apex.js](/public/monaco-editor/esm/vs/basic-languages/apex/apex.js) | JavaScript | 286 | 36 | 1 | 323 |
| [public/monaco-editor/esm/vs/basic-languages/azcli/azcli.contribution.js](/public/monaco-editor/esm/vs/basic-languages/azcli/azcli.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/azcli/azcli.js](/public/monaco-editor/esm/vs/basic-languages/azcli/azcli.js) | JavaScript | 69 | 7 | 2 | 78 |
| [public/monaco-editor/esm/vs/basic-languages/bat/bat.contribution.js](/public/monaco-editor/esm/vs/basic-languages/bat/bat.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/bat/bat.js](/public/monaco-editor/esm/vs/basic-languages/bat/bat.js) | JavaScript | 90 | 7 | 2 | 99 |
| [public/monaco-editor/esm/vs/basic-languages/bicep/bicep.contribution.js](/public/monaco-editor/esm/vs/basic-languages/bicep/bicep.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/bicep/bicep.js](/public/monaco-editor/esm/vs/basic-languages/bicep/bicep.js) | JavaScript | 83 | 35 | 1 | 119 |
| [public/monaco-editor/esm/vs/basic-languages/cameligo/cameligo.contribution.js](/public/monaco-editor/esm/vs/basic-languages/cameligo/cameligo.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/cameligo/cameligo.js](/public/monaco-editor/esm/vs/basic-languages/cameligo/cameligo.js) | JavaScript | 163 | 7 | 2 | 172 |
| [public/monaco-editor/esm/vs/basic-languages/clojure/clojure.contribution.js](/public/monaco-editor/esm/vs/basic-languages/clojure/clojure.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/clojure/clojure.js](/public/monaco-editor/esm/vs/basic-languages/clojure/clojure.js) | JavaScript | 749 | 7 | 2 | 758 |
| [public/monaco-editor/esm/vs/basic-languages/coffee/coffee.contribution.js](/public/monaco-editor/esm/vs/basic-languages/coffee/coffee.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/coffee/coffee.js](/public/monaco-editor/esm/vs/basic-languages/coffee/coffee.js) | JavaScript | 223 | 7 | 2 | 232 |
| [public/monaco-editor/esm/vs/basic-languages/cpp/cpp.contribution.js](/public/monaco-editor/esm/vs/basic-languages/cpp/cpp.contribution.js) | JavaScript | 29 | 7 | 2 | 38 |
| [public/monaco-editor/esm/vs/basic-languages/cpp/cpp.js](/public/monaco-editor/esm/vs/basic-languages/cpp/cpp.js) | JavaScript | 349 | 40 | 2 | 391 |
| [public/monaco-editor/esm/vs/basic-languages/csharp/csharp.contribution.js](/public/monaco-editor/esm/vs/basic-languages/csharp/csharp.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/csharp/csharp.js](/public/monaco-editor/esm/vs/basic-languages/csharp/csharp.js) | JavaScript | 282 | 40 | 2 | 324 |
| [public/monaco-editor/esm/vs/basic-languages/csp/csp.contribution.js](/public/monaco-editor/esm/vs/basic-languages/csp/csp.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/csp/csp.js](/public/monaco-editor/esm/vs/basic-languages/csp/csp.js) | JavaScript | 52 | 7 | 2 | 61 |
| [public/monaco-editor/esm/vs/basic-languages/css/css.contribution.js](/public/monaco-editor/esm/vs/basic-languages/css/css.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/css/css.js](/public/monaco-editor/esm/vs/basic-languages/css/css.js) | JavaScript | 179 | 7 | 2 | 188 |
| [public/monaco-editor/esm/vs/basic-languages/dart/dart.contribution.js](/public/monaco-editor/esm/vs/basic-languages/dart/dart.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/dart/dart.js](/public/monaco-editor/esm/vs/basic-languages/dart/dart.js) | JavaScript | 205 | 68 | 1 | 274 |
| [public/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.contribution.js](/public/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.js](/public/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.js) | JavaScript | 127 | 7 | 2 | 136 |
| [public/monaco-editor/esm/vs/basic-languages/ecl/ecl.contribution.js](/public/monaco-editor/esm/vs/basic-languages/ecl/ecl.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/ecl/ecl.js](/public/monaco-editor/esm/vs/basic-languages/ecl/ecl.js) | JavaScript | 434 | 23 | 1 | 458 |
| [public/monaco-editor/esm/vs/basic-languages/elixir/elixir.contribution.js](/public/monaco-editor/esm/vs/basic-languages/elixir/elixir.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/elixir/elixir.js](/public/monaco-editor/esm/vs/basic-languages/elixir/elixir.js) | JavaScript | 462 | 7 | 2 | 471 |
| [public/monaco-editor/esm/vs/basic-languages/flow9/flow9.contribution.js](/public/monaco-editor/esm/vs/basic-languages/flow9/flow9.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/flow9/flow9.js](/public/monaco-editor/esm/vs/basic-languages/flow9/flow9.js) | JavaScript | 121 | 23 | 1 | 145 |
| [public/monaco-editor/esm/vs/basic-languages/fsharp/fsharp.contribution.js](/public/monaco-editor/esm/vs/basic-languages/fsharp/fsharp.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/fsharp/fsharp.js](/public/monaco-editor/esm/vs/basic-languages/fsharp/fsharp.js) | JavaScript | 205 | 7 | 2 | 214 |
| [public/monaco-editor/esm/vs/basic-languages/go/go.contribution.js](/public/monaco-editor/esm/vs/basic-languages/go/go.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/go/go.js](/public/monaco-editor/esm/vs/basic-languages/go/go.js) | JavaScript | 181 | 30 | 1 | 212 |
| [public/monaco-editor/esm/vs/basic-languages/graphql/graphql.contribution.js](/public/monaco-editor/esm/vs/basic-languages/graphql/graphql.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/graphql/graphql.js](/public/monaco-editor/esm/vs/basic-languages/graphql/graphql.js) | JavaScript | 136 | 7 | 2 | 145 |
| [public/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.contribution.js](/public/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js](/public/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js) | JavaScript | 396 | 8 | 4 | 408 |
| [public/monaco-editor/esm/vs/basic-languages/hcl/hcl.contribution.js](/public/monaco-editor/esm/vs/basic-languages/hcl/hcl.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/hcl/hcl.js](/public/monaco-editor/esm/vs/basic-languages/hcl/hcl.js) | JavaScript | 145 | 35 | 1 | 181 |
| [public/monaco-editor/esm/vs/basic-languages/html/html.contribution.js](/public/monaco-editor/esm/vs/basic-languages/html/html.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/html/html.js](/public/monaco-editor/esm/vs/basic-languages/html/html.js) | JavaScript | 272 | 8 | 4 | 284 |
| [public/monaco-editor/esm/vs/basic-languages/ini/ini.contribution.js](/public/monaco-editor/esm/vs/basic-languages/ini/ini.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/ini/ini.js](/public/monaco-editor/esm/vs/basic-languages/ini/ini.js) | JavaScript | 63 | 7 | 2 | 72 |
| [public/monaco-editor/esm/vs/basic-languages/java/java.contribution.js](/public/monaco-editor/esm/vs/basic-languages/java/java.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/java/java.js](/public/monaco-editor/esm/vs/basic-languages/java/java.js) | JavaScript | 191 | 33 | 1 | 225 |
| [public/monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution.js](/public/monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution.js) | JavaScript | 18 | 7 | 2 | 27 |
| [public/monaco-editor/esm/vs/basic-languages/javascript/javascript.js](/public/monaco-editor/esm/vs/basic-languages/javascript/javascript.js) | JavaScript | 69 | 7 | 2 | 78 |
| [public/monaco-editor/esm/vs/basic-languages/julia/julia.contribution.js](/public/monaco-editor/esm/vs/basic-languages/julia/julia.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/julia/julia.js](/public/monaco-editor/esm/vs/basic-languages/julia/julia.js) | JavaScript | 491 | 7 | 2 | 500 |
| [public/monaco-editor/esm/vs/basic-languages/kotlin/kotlin.contribution.js](/public/monaco-editor/esm/vs/basic-languages/kotlin/kotlin.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/kotlin/kotlin.js](/public/monaco-editor/esm/vs/basic-languages/kotlin/kotlin.js) | JavaScript | 217 | 31 | 1 | 249 |
| [public/monaco-editor/esm/vs/basic-languages/less/less.contribution.js](/public/monaco-editor/esm/vs/basic-languages/less/less.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/less/less.js](/public/monaco-editor/esm/vs/basic-languages/less/less.js) | JavaScript | 162 | 7 | 2 | 171 |
| [public/monaco-editor/esm/vs/basic-languages/lexon/lexon.contribution.js](/public/monaco-editor/esm/vs/basic-languages/lexon/lexon.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/lexon/lexon.js](/public/monaco-editor/esm/vs/basic-languages/lexon/lexon.js) | JavaScript | 146 | 7 | 2 | 155 |
| [public/monaco-editor/esm/vs/basic-languages/liquid/liquid.contribution.js](/public/monaco-editor/esm/vs/basic-languages/liquid/liquid.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/liquid/liquid.js](/public/monaco-editor/esm/vs/basic-languages/liquid/liquid.js) | JavaScript | 243 | 8 | 4 | 255 |
| [public/monaco-editor/esm/vs/basic-languages/lua/lua.contribution.js](/public/monaco-editor/esm/vs/basic-languages/lua/lua.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/lua/lua.js](/public/monaco-editor/esm/vs/basic-languages/lua/lua.js) | JavaScript | 152 | 7 | 2 | 161 |
| [public/monaco-editor/esm/vs/basic-languages/m3/m3.contribution.js](/public/monaco-editor/esm/vs/basic-languages/m3/m3.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/m3/m3.js](/public/monaco-editor/esm/vs/basic-languages/m3/m3.js) | JavaScript | 205 | 7 | 2 | 214 |
| [public/monaco-editor/esm/vs/basic-languages/markdown/markdown.contribution.js](/public/monaco-editor/esm/vs/basic-languages/markdown/markdown.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/markdown/markdown.js](/public/monaco-editor/esm/vs/basic-languages/markdown/markdown.js) | JavaScript | 197 | 7 | 2 | 206 |
| [public/monaco-editor/esm/vs/basic-languages/mips/mips.contribution.js](/public/monaco-editor/esm/vs/basic-languages/mips/mips.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/mips/mips.js](/public/monaco-editor/esm/vs/basic-languages/mips/mips.js) | JavaScript | 189 | 7 | 2 | 198 |
| [public/monaco-editor/esm/vs/basic-languages/monaco.contribution.js](/public/monaco-editor/esm/vs/basic-languages/monaco.contribution.js) | JavaScript | 77 | 7 | 2 | 86 |
| [public/monaco-editor/esm/vs/basic-languages/msdax/msdax.contribution.js](/public/monaco-editor/esm/vs/basic-languages/msdax/msdax.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/msdax/msdax.js](/public/monaco-editor/esm/vs/basic-languages/msdax/msdax.js) | JavaScript | 372 | 7 | 2 | 381 |
| [public/monaco-editor/esm/vs/basic-languages/mysql/mysql.contribution.js](/public/monaco-editor/esm/vs/basic-languages/mysql/mysql.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/mysql/mysql.js](/public/monaco-editor/esm/vs/basic-languages/mysql/mysql.js) | JavaScript | 870 | 7 | 2 | 879 |
| [public/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.contribution.js](/public/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js](/public/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js) | JavaScript | 183 | 7 | 2 | 192 |
| [public/monaco-editor/esm/vs/basic-languages/pascal/pascal.contribution.js](/public/monaco-editor/esm/vs/basic-languages/pascal/pascal.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/pascal/pascal.js](/public/monaco-editor/esm/vs/basic-languages/pascal/pascal.js) | JavaScript | 240 | 7 | 2 | 249 |
| [public/monaco-editor/esm/vs/basic-languages/pascaligo/pascaligo.contribution.js](/public/monaco-editor/esm/vs/basic-languages/pascaligo/pascaligo.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/pascaligo/pascaligo.js](/public/monaco-editor/esm/vs/basic-languages/pascaligo/pascaligo.js) | JavaScript | 153 | 7 | 2 | 162 |
| [public/monaco-editor/esm/vs/basic-languages/perl/perl.contribution.js](/public/monaco-editor/esm/vs/basic-languages/perl/perl.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/perl/perl.js](/public/monaco-editor/esm/vs/basic-languages/perl/perl.js) | JavaScript | 594 | 7 | 2 | 603 |
| [public/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.contribution.js](/public/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.js](/public/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.js) | JavaScript | 838 | 7 | 2 | 847 |
| [public/monaco-editor/esm/vs/basic-languages/php/php.contribution.js](/public/monaco-editor/esm/vs/basic-languages/php/php.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/php/php.js](/public/monaco-editor/esm/vs/basic-languages/php/php.js) | JavaScript | 466 | 7 | 2 | 475 |
| [public/monaco-editor/esm/vs/basic-languages/pla/pla.contribution.js](/public/monaco-editor/esm/vs/basic-languages/pla/pla.contribution.js) | JavaScript | 14 | 7 | 2 | 23 |
| [public/monaco-editor/esm/vs/basic-languages/pla/pla.js](/public/monaco-editor/esm/vs/basic-languages/pla/pla.js) | JavaScript | 126 | 7 | 2 | 135 |
| [public/monaco-editor/esm/vs/basic-languages/postiats/postiats.contribution.js](/public/monaco-editor/esm/vs/basic-languages/postiats/postiats.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/postiats/postiats.js](/public/monaco-editor/esm/vs/basic-languages/postiats/postiats.js) | JavaScript | 534 | 12 | 2 | 548 |
| [public/monaco-editor/esm/vs/basic-languages/powerquery/powerquery.contribution.js](/public/monaco-editor/esm/vs/basic-languages/powerquery/powerquery.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/powerquery/powerquery.js](/public/monaco-editor/esm/vs/basic-languages/powerquery/powerquery.js) | JavaScript | 885 | 7 | 2 | 894 |
| [public/monaco-editor/esm/vs/basic-languages/powershell/powershell.contribution.js](/public/monaco-editor/esm/vs/basic-languages/powershell/powershell.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/powershell/powershell.js](/public/monaco-editor/esm/vs/basic-languages/powershell/powershell.js) | JavaScript | 228 | 7 | 2 | 237 |
| [public/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.contribution.js](/public/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js](/public/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js) | JavaScript | 390 | 34 | 2 | 426 |
| [public/monaco-editor/esm/vs/basic-languages/pug/pug.contribution.js](/public/monaco-editor/esm/vs/basic-languages/pug/pug.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/pug/pug.js](/public/monaco-editor/esm/vs/basic-languages/pug/pug.js) | JavaScript | 388 | 7 | 2 | 397 |
| [public/monaco-editor/esm/vs/basic-languages/python/python.contribution.js](/public/monaco-editor/esm/vs/basic-languages/python/python.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/python/python.js](/public/monaco-editor/esm/vs/basic-languages/python/python.js) | JavaScript | 260 | 8 | 4 | 272 |
| [public/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.contribution.js](/public/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js](/public/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js) | JavaScript | 276 | 7 | 2 | 285 |
| [public/monaco-editor/esm/vs/basic-languages/r/r.contribution.js](/public/monaco-editor/esm/vs/basic-languages/r/r.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/r/r.js](/public/monaco-editor/esm/vs/basic-languages/r/r.js) | JavaScript | 240 | 7 | 2 | 249 |
| [public/monaco-editor/esm/vs/basic-languages/razor/razor.contribution.js](/public/monaco-editor/esm/vs/basic-languages/razor/razor.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/razor/razor.js](/public/monaco-editor/esm/vs/basic-languages/razor/razor.js) | JavaScript | 517 | 8 | 4 | 529 |
| [public/monaco-editor/esm/vs/basic-languages/redis/redis.contribution.js](/public/monaco-editor/esm/vs/basic-languages/redis/redis.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/redis/redis.js](/public/monaco-editor/esm/vs/basic-languages/redis/redis.js) | JavaScript | 293 | 7 | 2 | 302 |
| [public/monaco-editor/esm/vs/basic-languages/redshift/redshift.contribution.js](/public/monaco-editor/esm/vs/basic-languages/redshift/redshift.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/redshift/redshift.js](/public/monaco-editor/esm/vs/basic-languages/redshift/redshift.js) | JavaScript | 801 | 7 | 2 | 810 |
| [public/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.contribution.js](/public/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js](/public/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js) | JavaScript | 157 | 7 | 2 | 166 |
| [public/monaco-editor/esm/vs/basic-languages/ruby/ruby.contribution.js](/public/monaco-editor/esm/vs/basic-languages/ruby/ruby.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/ruby/ruby.js](/public/monaco-editor/esm/vs/basic-languages/ruby/ruby.js) | JavaScript | 439 | 7 | 2 | 448 |
| [public/monaco-editor/esm/vs/basic-languages/rust/rust.contribution.js](/public/monaco-editor/esm/vs/basic-languages/rust/rust.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/rust/rust.js](/public/monaco-editor/esm/vs/basic-languages/rust/rust.js) | JavaScript | 298 | 42 | 1 | 341 |
| [public/monaco-editor/esm/vs/basic-languages/sb/sb.contribution.js](/public/monaco-editor/esm/vs/basic-languages/sb/sb.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/sb/sb.js](/public/monaco-editor/esm/vs/basic-languages/sb/sb.js) | JavaScript | 105 | 7 | 2 | 114 |
| [public/monaco-editor/esm/vs/basic-languages/scala/scala.contribution.js](/public/monaco-editor/esm/vs/basic-languages/scala/scala.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/scala/scala.js](/public/monaco-editor/esm/vs/basic-languages/scala/scala.js) | JavaScript | 346 | 9 | 2 | 357 |
| [public/monaco-editor/esm/vs/basic-languages/scheme/scheme.contribution.js](/public/monaco-editor/esm/vs/basic-languages/scheme/scheme.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/scheme/scheme.js](/public/monaco-editor/esm/vs/basic-languages/scheme/scheme.js) | JavaScript | 109 | 7 | 2 | 118 |
| [public/monaco-editor/esm/vs/basic-languages/scss/scss.contribution.js](/public/monaco-editor/esm/vs/basic-languages/scss/scss.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/scss/scss.js](/public/monaco-editor/esm/vs/basic-languages/scss/scss.js) | JavaScript | 237 | 7 | 2 | 246 |
| [public/monaco-editor/esm/vs/basic-languages/shell/shell.contribution.js](/public/monaco-editor/esm/vs/basic-languages/shell/shell.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/shell/shell.js](/public/monaco-editor/esm/vs/basic-languages/shell/shell.js) | JavaScript | 216 | 7 | 2 | 225 |
| [public/monaco-editor/esm/vs/basic-languages/solidity/solidity.contribution.js](/public/monaco-editor/esm/vs/basic-languages/solidity/solidity.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/solidity/solidity.js](/public/monaco-editor/esm/vs/basic-languages/solidity/solidity.js) | JavaScript | 1,326 | 28 | 1 | 1,355 |
| [public/monaco-editor/esm/vs/basic-languages/sophia/sophia.contribution.js](/public/monaco-editor/esm/vs/basic-languages/sophia/sophia.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/sophia/sophia.js](/public/monaco-editor/esm/vs/basic-languages/sophia/sophia.js) | JavaScript | 165 | 28 | 1 | 194 |
| [public/monaco-editor/esm/vs/basic-languages/sparql/sparql.contribution.js](/public/monaco-editor/esm/vs/basic-languages/sparql/sparql.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/sparql/sparql.js](/public/monaco-editor/esm/vs/basic-languages/sparql/sparql.js) | JavaScript | 188 | 7 | 2 | 197 |
| [public/monaco-editor/esm/vs/basic-languages/sql/sql.contribution.js](/public/monaco-editor/esm/vs/basic-languages/sql/sql.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/sql/sql.js](/public/monaco-editor/esm/vs/basic-languages/sql/sql.js) | JavaScript | 813 | 7 | 2 | 822 |
| [public/monaco-editor/esm/vs/basic-languages/st/st.contribution.js](/public/monaco-editor/esm/vs/basic-languages/st/st.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/st/st.js](/public/monaco-editor/esm/vs/basic-languages/st/st.js) | JavaScript | 406 | 10 | 2 | 418 |
| [public/monaco-editor/esm/vs/basic-languages/swift/swift.contribution.js](/public/monaco-editor/esm/vs/basic-languages/swift/swift.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/swift/swift.js](/public/monaco-editor/esm/vs/basic-languages/swift/swift.js) | JavaScript | 247 | 10 | 2 | 259 |
| [public/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.contribution.js](/public/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.contribution.js) | JavaScript | 29 | 7 | 2 | 38 |
| [public/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js](/public/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js) | JavaScript | 503 | 56 | 1 | 560 |
| [public/monaco-editor/esm/vs/basic-languages/tcl/tcl.contribution.js](/public/monaco-editor/esm/vs/basic-languages/tcl/tcl.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/tcl/tcl.js](/public/monaco-editor/esm/vs/basic-languages/tcl/tcl.js) | JavaScript | 227 | 7 | 2 | 236 |
| [public/monaco-editor/esm/vs/basic-languages/twig/twig.contribution.js](/public/monaco-editor/esm/vs/basic-languages/twig/twig.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/twig/twig.js](/public/monaco-editor/esm/vs/basic-languages/twig/twig.js) | JavaScript | 318 | 7 | 2 | 327 |
| [public/monaco-editor/esm/vs/basic-languages/typescript/typescript.contribution.js](/public/monaco-editor/esm/vs/basic-languages/typescript/typescript.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/typescript/typescript.js](/public/monaco-editor/esm/vs/basic-languages/typescript/typescript.js) | JavaScript | 261 | 78 | 3 | 342 |
| [public/monaco-editor/esm/vs/basic-languages/vb/vb.contribution.js](/public/monaco-editor/esm/vs/basic-languages/vb/vb.contribution.js) | JavaScript | 15 | 7 | 2 | 24 |
| [public/monaco-editor/esm/vs/basic-languages/vb/vb.js](/public/monaco-editor/esm/vs/basic-languages/vb/vb.js) | JavaScript | 359 | 7 | 2 | 368 |
| [public/monaco-editor/esm/vs/basic-languages/xml/xml.contribution.js](/public/monaco-editor/esm/vs/basic-languages/xml/xml.contribution.js) | JavaScript | 31 | 7 | 2 | 40 |
| [public/monaco-editor/esm/vs/basic-languages/xml/xml.js](/public/monaco-editor/esm/vs/basic-languages/xml/xml.js) | JavaScript | 99 | 8 | 4 | 111 |
| [public/monaco-editor/esm/vs/basic-languages/yaml/yaml.contribution.js](/public/monaco-editor/esm/vs/basic-languages/yaml/yaml.contribution.js) | JavaScript | 16 | 7 | 2 | 25 |
| [public/monaco-editor/esm/vs/basic-languages/yaml/yaml.js](/public/monaco-editor/esm/vs/basic-languages/yaml/yaml.js) | JavaScript | 163 | 7 | 2 | 172 |
| [public/monaco-editor/esm/vs/editor/browser/config/charWidthReader.js](/public/monaco-editor/esm/vs/editor/browser/config/charWidthReader.js) | JavaScript | 107 | 10 | 1 | 118 |
| [public/monaco-editor/esm/vs/editor/browser/config/configuration.js](/public/monaco-editor/esm/vs/editor/browser/config/configuration.js) | JavaScript | 246 | 15 | 1 | 262 |
| [public/monaco-editor/esm/vs/editor/browser/config/elementSizeObserver.js](/public/monaco-editor/esm/vs/editor/browser/config/elementSizeObserver.js) | JavaScript | 77 | 5 | 1 | 83 |
| [public/monaco-editor/esm/vs/editor/browser/controller/coreCommands.js](/public/monaco-editor/esm/vs/editor/browser/controller/coreCommands.js) | JavaScript | 1,503 | 37 | 1 | 1,541 |
| [public/monaco-editor/esm/vs/editor/browser/controller/mouseHandler.js](/public/monaco-editor/esm/vs/editor/browser/controller/mouseHandler.js) | JavaScript | 439 | 26 | 1 | 466 |
| [public/monaco-editor/esm/vs/editor/browser/controller/mouseTarget.js](/public/monaco-editor/esm/vs/editor/browser/controller/mouseTarget.js) | JavaScript | 746 | 73 | 1 | 820 |
| [public/monaco-editor/esm/vs/editor/browser/controller/pointerHandler.js](/public/monaco-editor/esm/vs/editor/browser/controller/pointerHandler.js) | JavaScript | 110 | 10 | 1 | 121 |
| [public/monaco-editor/esm/vs/editor/browser/controller/textAreaHandler.css](/public/monaco-editor/esm/vs/editor/browser/controller/textAreaHandler.css) | CSS | 16 | 17 | 2 | 35 |
| [public/monaco-editor/esm/vs/editor/browser/controller/textAreaHandler.js](/public/monaco-editor/esm/vs/editor/browser/controller/textAreaHandler.js) | JavaScript | 498 | 46 | 1 | 545 |
| [public/monaco-editor/esm/vs/editor/browser/controller/textAreaInput.js](/public/monaco-editor/esm/vs/editor/browser/controller/textAreaInput.js) | JavaScript | 510 | 106 | 1 | 617 |
| [public/monaco-editor/esm/vs/editor/browser/controller/textAreaState.js](/public/monaco-editor/esm/vs/editor/browser/controller/textAreaState.js) | JavaScript | 242 | 29 | 1 | 272 |
| [public/monaco-editor/esm/vs/editor/browser/core/editorState.js](/public/monaco-editor/esm/vs/editor/browser/core/editorState.js) | JavaScript | 134 | 12 | 1 | 147 |
| [public/monaco-editor/esm/vs/editor/browser/core/keybindingCancellation.js](/public/monaco-editor/esm/vs/editor/browser/core/keybindingCancellation.js) | JavaScript | 72 | 6 | 1 | 79 |
| [public/monaco-editor/esm/vs/editor/browser/core/markdownRenderer.js](/public/monaco-editor/esm/vs/editor/browser/core/markdownRenderer.js) | JavaScript | 97 | 12 | 1 | 110 |
| [public/monaco-editor/esm/vs/editor/browser/editorBrowser.js](/public/monaco-editor/esm/vs/editor/browser/editorBrowser.js) | JavaScript | 26 | 13 | 1 | 40 |
| [public/monaco-editor/esm/vs/editor/browser/editorDom.js](/public/monaco-editor/esm/vs/editor/browser/editorDom.js) | JavaScript | 222 | 26 | 1 | 249 |
| [public/monaco-editor/esm/vs/editor/browser/editorExtensions.js](/public/monaco-editor/esm/vs/editor/browser/editorExtensions.js) | JavaScript | 416 | 25 | 1 | 442 |
| [public/monaco-editor/esm/vs/editor/browser/services/abstractCodeEditorService.js](/public/monaco-editor/esm/vs/editor/browser/services/abstractCodeEditorService.js) | JavaScript | 73 | 5 | 1 | 79 |
| [public/monaco-editor/esm/vs/editor/browser/services/bulkEditService.js](/public/monaco-editor/esm/vs/editor/browser/services/bulkEditService.js) | JavaScript | 42 | 4 | 1 | 47 |
| [public/monaco-editor/esm/vs/editor/browser/services/codeEditorService.js](/public/monaco-editor/esm/vs/editor/browser/services/codeEditorService.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/editor/browser/services/codeEditorServiceImpl.js](/public/monaco-editor/esm/vs/editor/browser/services/codeEditorServiceImpl.js) | JavaScript | 39 | 0 | 1 | 40 |
| [public/monaco-editor/esm/vs/editor/browser/services/markerDecorations.js](/public/monaco-editor/esm/vs/editor/browser/services/markerDecorations.js) | JavaScript | 23 | 5 | 1 | 29 |
| [public/monaco-editor/esm/vs/editor/browser/services/openerService.js](/public/monaco-editor/esm/vs/editor/browser/services/openerService.js) | JavaScript | 226 | 27 | 1 | 254 |
| [public/monaco-editor/esm/vs/editor/browser/view/domLineBreaksComputer.js](/public/monaco-editor/esm/vs/editor/browser/view/domLineBreaksComputer.js) | JavaScript | 272 | 16 | 1 | 289 |
| [public/monaco-editor/esm/vs/editor/browser/view/dynamicViewOverlay.js](/public/monaco-editor/esm/vs/editor/browser/view/dynamicViewOverlay.js) | JavaScript | 3 | 4 | 1 | 8 |
| [public/monaco-editor/esm/vs/editor/browser/view/viewController.js](/public/monaco-editor/esm/vs/editor/browser/view/viewController.js) | JavaScript | 257 | 7 | 1 | 265 |
| [public/monaco-editor/esm/vs/editor/browser/view/viewImpl.js](/public/monaco-editor/esm/vs/editor/browser/view/viewImpl.js) | JavaScript | 381 | 27 | 1 | 409 |
| [public/monaco-editor/esm/vs/editor/browser/view/viewLayer.js](/public/monaco-editor/esm/vs/editor/browser/view/viewLayer.js) | JavaScript | 416 | 47 | 1 | 464 |
| [public/monaco-editor/esm/vs/editor/browser/view/viewOverlays.js](/public/monaco-editor/esm/vs/editor/browser/view/viewOverlays.js) | JavaScript | 192 | 14 | 1 | 207 |
| [public/monaco-editor/esm/vs/editor/browser/view/viewPart.js](/public/monaco-editor/esm/vs/editor/browser/view/viewPart.js) | JavaScript | 47 | 4 | 1 | 52 |
| [public/monaco-editor/esm/vs/editor/browser/view/viewUserInputEvents.js](/public/monaco-editor/esm/vs/editor/browser/view/viewUserInputEvents.js) | JavaScript | 100 | 4 | 1 | 105 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/contentWidgets/contentWidgets.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/contentWidgets/contentWidgets.js) | JavaScript | 413 | 32 | 1 | 446 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.css) | CSS | 17 | 4 | 4 | 25 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.js) | JavaScript | 173 | 7 | 1 | 181 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/decorations/decorations.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/decorations/decorations.css) | CSS | 3 | 8 | 1 | 12 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/decorations/decorations.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/decorations/decorations.js) | JavaScript | 182 | 13 | 1 | 196 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/editorScrollbar/editorScrollbar.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/editorScrollbar/editorScrollbar.js) | JavaScript | 169 | 12 | 3 | 184 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.css) | CSS | 10 | 8 | 3 | 21 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.js) | JavaScript | 149 | 6 | 1 | 156 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.css) | CSS | 4 | 4 | 2 | 10 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.js) | JavaScript | 220 | 8 | 1 | 229 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.css) | CSS | 18 | 4 | 4 | 26 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.js) | JavaScript | 154 | 8 | 1 | 163 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/lines/rangeUtil.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/rangeUtil.js) | JavaScript | 95 | 16 | 1 | 112 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLine.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLine.js) | JavaScript | 445 | 63 | 1 | 509 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLines.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLines.css) | CSS | 21 | 20 | 8 | 49 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLines.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLines.js) | JavaScript | 577 | 73 | 1 | 651 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.css) | CSS | 9 | 8 | 1 | 18 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.js) | JavaScript | 88 | 6 | 1 | 95 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/margin/margin.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/margin/margin.js) | JavaScript | 55 | 7 | 1 | 63 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.css) | CSS | 6 | 8 | 1 | 15 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.js) | JavaScript | 73 | 6 | 1 | 80 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimap.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimap.css) | CSS | 24 | 6 | 3 | 33 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimap.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimap.js) | JavaScript | 1,331 | 107 | 1 | 1,439 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRenderer.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRenderer.js) | JavaScript | 83 | 4 | 1 | 88 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRendererFactory.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRendererFactory.js) | JavaScript | 97 | 36 | 1 | 134 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharSheet.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharSheet.js) | JavaScript | 18 | 5 | 1 | 24 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapPreBaked.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapPreBaked.js) | JavaScript | 30 | 19 | 1 | 50 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.css) | CSS | 5 | 4 | 0 | 9 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.js) | JavaScript | 97 | 8 | 1 | 106 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler.js) | JavaScript | 341 | 16 | 1 | 358 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler/overviewRuler.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler/overviewRuler.js) | JavaScript | 121 | 6 | 1 | 128 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/rulers/rulers.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/rulers/rulers.css) | CSS | 4 | 4 | 1 | 9 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/rulers/rulers.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/rulers/rulers.js) | JavaScript | 75 | 8 | 1 | 84 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.css) | CSS | 6 | 4 | 1 | 11 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.js) | JavaScript | 68 | 7 | 1 | 76 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/selections/selections.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/selections/selections.css) | CSS | 11 | 8 | 4 | 23 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/selections/selections.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/selections/selections.js) | JavaScript | 314 | 23 | 1 | 338 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursor.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursor.js) | JavaScript | 152 | 10 | 1 | 163 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.css](/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.css) | CSS | 68 | 8 | 12 | 88 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.js) | JavaScript | 301 | 15 | 1 | 317 |
| [public/monaco-editor/esm/vs/editor/browser/viewParts/viewZones/viewZones.js](/public/monaco-editor/esm/vs/editor/browser/viewParts/viewZones/viewZones.js) | JavaScript | 318 | 11 | 1 | 330 |
| [public/monaco-editor/esm/vs/editor/browser/widget/codeEditorWidget.js](/public/monaco-editor/esm/vs/editor/browser/widget/codeEditorWidget.js) | JavaScript | 1,497 | 19 | 1 | 1,517 |
| [public/monaco-editor/esm/vs/editor/browser/widget/diffEditorWidget.js](/public/monaco-editor/esm/vs/editor/browser/widget/diffEditorWidget.js) | JavaScript | 1,881 | 63 | 1 | 1,945 |
| [public/monaco-editor/esm/vs/editor/browser/widget/diffNavigator.js](/public/monaco-editor/esm/vs/editor/browser/widget/diffNavigator.js) | JavaScript | 153 | 16 | 1 | 170 |
| [public/monaco-editor/esm/vs/editor/browser/widget/diffReview.js](/public/monaco-editor/esm/vs/editor/browser/widget/diffReview.js) | JavaScript | 712 | 16 | 1 | 729 |
| [public/monaco-editor/esm/vs/editor/browser/widget/embeddedCodeEditorWidget.js](/public/monaco-editor/esm/vs/editor/browser/widget/embeddedCodeEditorWidget.js) | JavaScript | 48 | 5 | 1 | 54 |
| [public/monaco-editor/esm/vs/editor/browser/widget/inlineDiffMargin.js](/public/monaco-editor/esm/vs/editor/browser/widget/inlineDiffMargin.js) | JavaScript | 178 | 8 | 1 | 187 |
| [public/monaco-editor/esm/vs/editor/browser/widget/media/diffEditor.css](/public/monaco-editor/esm/vs/editor/browser/widget/media/diffEditor.css) | CSS | 43 | 8 | 12 | 63 |
| [public/monaco-editor/esm/vs/editor/browser/widget/media/diffReview.css](/public/monaco-editor/esm/vs/editor/browser/widget/media/diffReview.css) | CSS | 46 | 4 | 12 | 62 |
| [public/monaco-editor/esm/vs/editor/browser/widget/media/editor.css](/public/monaco-editor/esm/vs/editor/browser/widget/media/editor.css) | CSS | 19 | 14 | 10 | 43 |
| [public/monaco-editor/esm/vs/editor/common/commands/replaceCommand.js](/public/monaco-editor/esm/vs/editor/common/commands/replaceCommand.js) | JavaScript | 78 | 4 | 1 | 83 |
| [public/monaco-editor/esm/vs/editor/common/commands/shiftCommand.js](/public/monaco-editor/esm/vs/editor/common/commands/shiftCommand.js) | JavaScript | 200 | 23 | 1 | 224 |
| [public/monaco-editor/esm/vs/editor/common/commands/surroundSelectionCommand.js](/public/monaco-editor/esm/vs/editor/common/commands/surroundSelectionCommand.js) | JavaScript | 19 | 4 | 1 | 24 |
| [public/monaco-editor/esm/vs/editor/common/commands/trimTrailingWhitespaceCommand.js](/public/monaco-editor/esm/vs/editor/common/commands/trimTrailingWhitespaceCommand.js) | JavaScript | 67 | 14 | 1 | 82 |
| [public/monaco-editor/esm/vs/editor/common/config/commonEditorConfig.js](/public/monaco-editor/esm/vs/editor/common/config/commonEditorConfig.js) | JavaScript | 550 | 29 | 1 | 580 |
| [public/monaco-editor/esm/vs/editor/common/config/editorOptions.js](/public/monaco-editor/esm/vs/editor/common/config/editorOptions.js) | JavaScript | 2,362 | 166 | 1 | 2,529 |
| [public/monaco-editor/esm/vs/editor/common/config/editorZoom.js](/public/monaco-editor/esm/vs/editor/common/config/editorZoom.js) | JavaScript | 19 | 4 | 1 | 24 |
| [public/monaco-editor/esm/vs/editor/common/config/fontInfo.js](/public/monaco-editor/esm/vs/editor/common/config/fontInfo.js) | JavaScript | 102 | 37 | 1 | 140 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursor.js](/public/monaco-editor/esm/vs/editor/common/controller/cursor.js) | JavaScript | 776 | 63 | 1 | 840 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorAtomicMoveOperations.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorAtomicMoveOperations.js) | JavaScript | 103 | 39 | 1 | 143 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorCollection.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorCollection.js) | JavaScript | 231 | 11 | 1 | 243 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorColumnSelection.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorColumnSelection.js) | JavaScript | 83 | 7 | 1 | 91 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorColumns.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorColumns.js) | JavaScript | 111 | 20 | 1 | 132 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorCommon.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorCommon.js) | JavaScript | 216 | 9 | 1 | 226 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorDeleteOperations.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorDeleteOperations.js) | JavaScript | 199 | 15 | 1 | 215 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorEvents.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorEvents.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorMoveCommands.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorMoveCommands.js) | JavaScript | 601 | 40 | 1 | 642 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorMoveOperations.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorMoveOperations.js) | JavaScript | 246 | 25 | 1 | 272 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorTypeOperations.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorTypeOperations.js) | JavaScript | 827 | 78 | 1 | 906 |
| [public/monaco-editor/esm/vs/editor/common/controller/cursorWordOperations.js](/public/monaco-editor/esm/vs/editor/common/controller/cursorWordOperations.js) | JavaScript | 643 | 35 | 1 | 679 |
| [public/monaco-editor/esm/vs/editor/common/controller/oneCursor.js](/public/monaco-editor/esm/vs/editor/common/controller/oneCursor.js) | JavaScript | 97 | 13 | 1 | 111 |
| [public/monaco-editor/esm/vs/editor/common/controller/wordCharacterClassifier.js](/public/monaco-editor/esm/vs/editor/common/controller/wordCharacterClassifier.js) | JavaScript | 21 | 4 | 1 | 26 |
| [public/monaco-editor/esm/vs/editor/common/core/characterClassifier.js](/public/monaco-editor/esm/vs/editor/common/core/characterClassifier.js) | JavaScript | 44 | 7 | 1 | 52 |
| [public/monaco-editor/esm/vs/editor/common/core/editOperation.js](/public/monaco-editor/esm/vs/editor/common/core/editOperation.js) | JavaScript | 29 | 4 | 1 | 34 |
| [public/monaco-editor/esm/vs/editor/common/core/lineTokens.js](/public/monaco-editor/esm/vs/editor/common/core/lineTokens.js) | JavaScript | 204 | 15 | 1 | 220 |
| [public/monaco-editor/esm/vs/editor/common/core/position.js](/public/monaco-editor/esm/vs/editor/common/core/position.js) | JavaScript | 77 | 57 | 1 | 135 |
| [public/monaco-editor/esm/vs/editor/common/core/range.js](/public/monaco-editor/esm/vs/editor/common/core/range.js) | JavaScript | 266 | 110 | 1 | 377 |
| [public/monaco-editor/esm/vs/editor/common/core/rgba.js](/public/monaco-editor/esm/vs/editor/common/core/rgba.js) | JavaScript | 25 | 8 | 1 | 34 |
| [public/monaco-editor/esm/vs/editor/common/core/selection.js](/public/monaco-editor/esm/vs/editor/common/core/selection.js) | JavaScript | 91 | 51 | 1 | 143 |
| [public/monaco-editor/esm/vs/editor/common/core/stringBuilder.js](/public/monaco-editor/esm/vs/editor/common/core/stringBuilder.js) | JavaScript | 136 | 10 | 1 | 147 |
| [public/monaco-editor/esm/vs/editor/common/core/token.js](/public/monaco-editor/esm/vs/editor/common/core/token.js) | JavaScript | 25 | 4 | 1 | 30 |
| [public/monaco-editor/esm/vs/editor/common/diff/diffComputer.js](/public/monaco-editor/esm/vs/editor/common/diff/diffComputer.js) | JavaScript | 378 | 20 | 1 | 399 |
| [public/monaco-editor/esm/vs/editor/common/editorAction.js](/public/monaco-editor/esm/vs/editor/common/editorAction.js) | JavaScript | 19 | 4 | 1 | 24 |
| [public/monaco-editor/esm/vs/editor/common/editorCommon.js](/public/monaco-editor/esm/vs/editor/common/editorCommon.js) | JavaScript | 4 | 3 | 1 | 8 |
| [public/monaco-editor/esm/vs/editor/common/editorContextKeys.js](/public/monaco-editor/esm/vs/editor/common/editorContextKeys.js) | JavaScript | 44 | 20 | 1 | 65 |
| [public/monaco-editor/esm/vs/editor/common/model.js](/public/monaco-editor/esm/vs/editor/common/model.js) | JavaScript | 84 | 37 | 1 | 122 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairs.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairs.js) | JavaScript | 25 | 14 | 1 | 40 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsImpl.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsImpl.js) | JavaScript | 610 | 44 | 1 | 655 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/ast.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/ast.js) | JavaScript | 423 | 44 | 1 | 468 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/beforeEditPositionMapper.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/beforeEditPositionMapper.js) | JavaScript | 75 | 17 | 1 | 93 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/bracketPairsTree.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/bracketPairsTree.js) | JavaScript | 163 | 13 | 1 | 177 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/brackets.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/brackets.js) | JavaScript | 94 | 8 | 1 | 103 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/concat23Trees.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/concat23Trees.js) | JavaScript | 147 | 42 | 1 | 190 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/length.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/length.js) | JavaScript | 87 | 33 | 1 | 121 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/nodeReader.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/nodeReader.js) | JavaScript | 95 | 29 | 1 | 125 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/parser.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/parser.js) | JavaScript | 92 | 13 | 1 | 106 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet.js) | JavaScript | 89 | 15 | 1 | 105 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/tokenizer.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/tokenizer.js) | JavaScript | 254 | 41 | 1 | 296 |
| [public/monaco-editor/esm/vs/editor/common/model/bracketPairs/colorizedBracketPairsDecorationProvider.js](/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/colorizedBracketPairsDecorationProvider.js) | JavaScript | 83 | 6 | 1 | 90 |
| [public/monaco-editor/esm/vs/editor/common/model/decorationProvider.js](/public/monaco-editor/esm/vs/editor/common/model/decorationProvider.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/editor/common/model/editStack.js](/public/monaco-editor/esm/vs/editor/common/model/editStack.js) | JavaScript | 352 | 7 | 1 | 360 |
| [public/monaco-editor/esm/vs/editor/common/model/indentationGuesser.js](/public/monaco-editor/esm/vs/editor/common/model/indentationGuesser.js) | JavaScript | 138 | 38 | 1 | 177 |
| [public/monaco-editor/esm/vs/editor/common/model/intervalTree.js](/public/monaco-editor/esm/vs/editor/common/model/intervalTree.js) | JavaScript | 860 | 112 | 1 | 973 |
| [public/monaco-editor/esm/vs/editor/common/model/mirrorTextModel.js](/public/monaco-editor/esm/vs/editor/common/model/mirrorTextModel.js) | JavaScript | 94 | 20 | 1 | 115 |
| [public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase.js](/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase.js) | JavaScript | 1,366 | 86 | 1 | 1,453 |
| [public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer.js](/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer.js) | JavaScript | 408 | 37 | 1 | 446 |
| [public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder.js](/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder.js) | JavaScript | 125 | 13 | 1 | 139 |
| [public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase.js](/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase.js) | JavaScript | 344 | 16 | 1 | 361 |
| [public/monaco-editor/esm/vs/editor/common/model/textChange.js](/public/monaco-editor/esm/vs/editor/common/model/textChange.js) | JavaScript | 237 | 6 | 1 | 244 |
| [public/monaco-editor/esm/vs/editor/common/model/textModel.js](/public/monaco-editor/esm/vs/editor/common/model/textModel.js) | JavaScript | 2,301 | 145 | 1 | 2,447 |
| [public/monaco-editor/esm/vs/editor/common/model/textModelEvents.js](/public/monaco-editor/esm/vs/editor/common/model/textModelEvents.js) | JavaScript | 135 | 39 | 1 | 175 |
| [public/monaco-editor/esm/vs/editor/common/model/textModelSearch.js](/public/monaco-editor/esm/vs/editor/common/model/textModelSearch.js) | JavaScript | 413 | 43 | 1 | 457 |
| [public/monaco-editor/esm/vs/editor/common/model/textModelTokens.js](/public/monaco-editor/esm/vs/editor/common/model/textModelTokens.js) | JavaScript | 381 | 15 | 1 | 397 |
| [public/monaco-editor/esm/vs/editor/common/model/tokensStore.js](/public/monaco-editor/esm/vs/editor/common/model/tokensStore.js) | JavaScript | 873 | 151 | 1 | 1,025 |
| [public/monaco-editor/esm/vs/editor/common/model/wordHelper.js](/public/monaco-editor/esm/vs/editor/common/model/wordHelper.js) | JavaScript | 97 | 19 | 1 | 117 |
| [public/monaco-editor/esm/vs/editor/common/modes.js](/public/monaco-editor/esm/vs/editor/common/modes.js) | JavaScript | 270 | 145 | 1 | 416 |
| [public/monaco-editor/esm/vs/editor/common/modes/languageConfiguration.js](/public/monaco-editor/esm/vs/editor/common/modes/languageConfiguration.js) | JavaScript | 93 | 32 | 1 | 126 |
| [public/monaco-editor/esm/vs/editor/common/modes/languageConfigurationRegistry.js](/public/monaco-editor/esm/vs/editor/common/modes/languageConfigurationRegistry.js) | JavaScript | 748 | 63 | 1 | 812 |
| [public/monaco-editor/esm/vs/editor/common/modes/languageFeatureRegistry.js](/public/monaco-editor/esm/vs/editor/common/modes/languageFeatureRegistry.js) | JavaScript | 182 | 13 | 1 | 196 |
| [public/monaco-editor/esm/vs/editor/common/modes/languageSelector.js](/public/monaco-editor/esm/vs/editor/common/modes/languageSelector.js) | JavaScript | 79 | 14 | 1 | 94 |
| [public/monaco-editor/esm/vs/editor/common/modes/linkComputer.js](/public/monaco-editor/esm/vs/editor/common/modes/linkComputer.js) | JavaScript | 240 | 25 | 1 | 266 |
| [public/monaco-editor/esm/vs/editor/common/modes/modesRegistry.js](/public/monaco-editor/esm/vs/editor/common/modes/modesRegistry.js) | JavaScript | 72 | 6 | 1 | 79 |
| [public/monaco-editor/esm/vs/editor/common/modes/nullMode.js](/public/monaco-editor/esm/vs/editor/common/modes/nullMode.js) | JavaScript | 24 | 4 | 1 | 29 |
| [public/monaco-editor/esm/vs/editor/common/modes/supports.js](/public/monaco-editor/esm/vs/editor/common/modes/supports.js) | JavaScript | 45 | 4 | 1 | 50 |
| [public/monaco-editor/esm/vs/editor/common/modes/supports/characterPair.js](/public/monaco-editor/esm/vs/editor/common/modes/supports/characterPair.js) | JavaScript | 47 | 9 | 1 | 57 |
| [public/monaco-editor/esm/vs/editor/common/modes/supports/electricCharacter.js](/public/monaco-editor/esm/vs/editor/common/modes/supports/electricCharacter.js) | JavaScript | 47 | 5 | 1 | 53 |
| [public/monaco-editor/esm/vs/editor/common/modes/supports/indentRules.js](/public/monaco-editor/esm/vs/editor/common/modes/supports/indentRules.js) | JavaScript | 53 | 8 | 1 | 62 |
| [public/monaco-editor/esm/vs/editor/common/modes/supports/inplaceReplaceSupport.js](/public/monaco-editor/esm/vs/editor/common/modes/supports/inplaceReplaceSupport.js) | JavaScript | 79 | 6 | 1 | 86 |
| [public/monaco-editor/esm/vs/editor/common/modes/supports/onEnter.js](/public/monaco-editor/esm/vs/editor/common/modes/supports/onEnter.js) | JavaScript | 99 | 7 | 1 | 107 |
| [public/monaco-editor/esm/vs/editor/common/modes/supports/richEditBrackets.js](/public/monaco-editor/esm/vs/editor/common/modes/supports/richEditBrackets.js) | JavaScript | 262 | 100 | 1 | 363 |
| [public/monaco-editor/esm/vs/editor/common/modes/supports/tokenization.js](/public/monaco-editor/esm/vs/editor/common/modes/supports/tokenization.js) | JavaScript | 263 | 15 | 1 | 279 |
| [public/monaco-editor/esm/vs/editor/common/modes/textToHtmlTokenizer.js](/public/monaco-editor/esm/vs/editor/common/modes/textToHtmlTokenizer.js) | JavaScript | 114 | 5 | 1 | 120 |
| [public/monaco-editor/esm/vs/editor/common/modes/tokenizationRegistry.js](/public/monaco-editor/esm/vs/editor/common/modes/tokenizationRegistry.js) | JavaScript | 75 | 4 | 1 | 80 |
| [public/monaco-editor/esm/vs/editor/common/modes/unicodeTextModelHighlighter.js](/public/monaco-editor/esm/vs/editor/common/modes/unicodeTextModelHighlighter.js) | JavaScript | 145 | 7 | 1 | 153 |
| [public/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js](/public/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js) | JavaScript | 446 | 45 | 1 | 492 |
| [public/monaco-editor/esm/vs/editor/common/services/editorWorkerService.js](/public/monaco-editor/esm/vs/editor/common/services/editorWorkerService.js) | JavaScript | 3 | 4 | 1 | 8 |
| [public/monaco-editor/esm/vs/editor/common/services/editorWorkerServiceImpl.js](/public/monaco-editor/esm/vs/editor/common/services/editorWorkerServiceImpl.js) | JavaScript | 416 | 22 | 1 | 439 |
| [public/monaco-editor/esm/vs/editor/common/services/getIconClasses.js](/public/monaco-editor/esm/vs/editor/common/services/getIconClasses.js) | JavaScript | 63 | 17 | 1 | 81 |
| [public/monaco-editor/esm/vs/editor/common/services/getSemanticTokens.js](/public/monaco-editor/esm/vs/editor/common/services/getSemanticTokens.js) | JavaScript | 198 | 19 | 1 | 218 |
| [public/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js](/public/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js) | JavaScript | 237 | 12 | 1 | 250 |
| [public/monaco-editor/esm/vs/editor/common/services/markerDecorationsServiceImpl.js](/public/monaco-editor/esm/vs/editor/common/services/markerDecorationsServiceImpl.js) | JavaScript | 187 | 8 | 1 | 196 |
| [public/monaco-editor/esm/vs/editor/common/services/markersDecorationService.js](/public/monaco-editor/esm/vs/editor/common/services/markersDecorationService.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/editor/common/services/modeService.js](/public/monaco-editor/esm/vs/editor/common/services/modeService.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/editor/common/services/modeServiceImpl.js](/public/monaco-editor/esm/vs/editor/common/services/modeServiceImpl.js) | JavaScript | 93 | 8 | 1 | 102 |
| [public/monaco-editor/esm/vs/editor/common/services/modelService.js](/public/monaco-editor/esm/vs/editor/common/services/modelService.js) | JavaScript | 5 | 4 | 1 | 10 |
| [public/monaco-editor/esm/vs/editor/common/services/modelServiceImpl.js](/public/monaco-editor/esm/vs/editor/common/services/modelServiceImpl.js) | JavaScript | 713 | 31 | 1 | 745 |
| [public/monaco-editor/esm/vs/editor/common/services/resolverService.js](/public/monaco-editor/esm/vs/editor/common/services/resolverService.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js](/public/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js) | JavaScript | 73 | 6 | 1 | 80 |
| [public/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js](/public/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js) | JavaScript | 247 | 12 | 1 | 260 |
| [public/monaco-editor/esm/vs/editor/common/services/textResourceConfigurationService.js](/public/monaco-editor/esm/vs/editor/common/services/textResourceConfigurationService.js) | JavaScript | 3 | 0 | 1 | 4 |
| [public/monaco-editor/esm/vs/editor/common/services/webWorker.js](/public/monaco-editor/esm/vs/editor/common/services/webWorker.js) | JavaScript | 56 | 9 | 1 | 66 |
| [public/monaco-editor/esm/vs/editor/common/standalone/standaloneBase.js](/public/monaco-editor/esm/vs/editor/common/standalone/standaloneBase.js) | JavaScript | 36 | 4 | 1 | 41 |
| [public/monaco-editor/esm/vs/editor/common/standalone/standaloneEnums.js](/public/monaco-editor/esm/vs/editor/common/standalone/standaloneEnums.js) | JavaScript | 530 | 328 | 1 | 859 |
| [public/monaco-editor/esm/vs/editor/common/standaloneStrings.js](/public/monaco-editor/esm/vs/editor/common/standaloneStrings.js) | JavaScript | 62 | 4 | 1 | 67 |
| [public/monaco-editor/esm/vs/editor/common/view/editorColorRegistry.js](/public/monaco-editor/esm/vs/editor/common/view/editorColorRegistry.js) | JavaScript | 90 | 8 | 1 | 99 |
| [public/monaco-editor/esm/vs/editor/common/view/overviewZoneManager.js](/public/monaco-editor/esm/vs/editor/common/view/overviewZoneManager.js) | JavaScript | 159 | 7 | 1 | 167 |
| [public/monaco-editor/esm/vs/editor/common/view/renderingContext.js](/public/monaco-editor/esm/vs/editor/common/view/renderingContext.js) | JavaScript | 89 | 4 | 1 | 94 |
| [public/monaco-editor/esm/vs/editor/common/view/viewContext.js](/public/monaco-editor/esm/vs/editor/common/view/viewContext.js) | JavaScript | 28 | 4 | 1 | 33 |
| [public/monaco-editor/esm/vs/editor/common/view/viewEvents.js](/public/monaco-editor/esm/vs/editor/common/view/viewEvents.js) | JavaScript | 126 | 8 | 1 | 135 |
| [public/monaco-editor/esm/vs/editor/common/viewLayout/lineDecorations.js](/public/monaco-editor/esm/vs/editor/common/viewLayout/lineDecorations.js) | JavaScript | 189 | 15 | 1 | 205 |
| [public/monaco-editor/esm/vs/editor/common/viewLayout/linesLayout.js](/public/monaco-editor/esm/vs/editor/common/viewLayout/linesLayout.js) | JavaScript | 600 | 154 | 1 | 755 |
| [public/monaco-editor/esm/vs/editor/common/viewLayout/viewLayout.js](/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLayout.js) | JavaScript | 315 | 17 | 1 | 333 |
| [public/monaco-editor/esm/vs/editor/common/viewLayout/viewLineRenderer.js](/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLineRenderer.js) | JavaScript | 799 | 76 | 1 | 876 |
| [public/monaco-editor/esm/vs/editor/common/viewLayout/viewLinesViewportData.js](/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLinesViewportData.js) | JavaScript | 19 | 7 | 1 | 27 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/minimapTokensColorTracker.js](/public/monaco-editor/esm/vs/editor/common/viewModel/minimapTokensColorTracker.js) | JavaScript | 49 | 6 | 1 | 56 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjection.js](/public/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjection.js) | JavaScript | 297 | 20 | 1 | 318 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjectionData.js](/public/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjectionData.js) | JavaScript | 219 | 49 | 1 | 269 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/monospaceLineBreaksComputer.js](/public/monaco-editor/esm/vs/editor/common/viewModel/monospaceLineBreaksComputer.js) | JavaScript | 394 | 46 | 1 | 441 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/prefixSumComputer.js](/public/monaco-editor/esm/vs/editor/common/viewModel/prefixSumComputer.js) | JavaScript | 196 | 27 | 1 | 224 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/viewEventHandler.js](/public/monaco-editor/esm/vs/editor/common/viewModel/viewEventHandler.js) | JavaScript | 177 | 6 | 1 | 184 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/viewModel.js](/public/monaco-editor/esm/vs/editor/common/viewModel/viewModel.js) | JavaScript | 100 | 10 | 1 | 111 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/viewModelDecorations.js](/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelDecorations.js) | JavaScript | 140 | 11 | 1 | 152 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/viewModelEventDispatcher.js](/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelEventDispatcher.js) | JavaScript | 265 | 8 | 1 | 274 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/viewModelImpl.js](/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelImpl.js) | JavaScript | 887 | 36 | 1 | 924 |
| [public/monaco-editor/esm/vs/editor/common/viewModel/viewModelLines.js](/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelLines.js) | JavaScript | 855 | 72 | 1 | 928 |
| [public/monaco-editor/esm/vs/editor/contrib/anchorSelect/anchorSelect.css](/public/monaco-editor/esm/vs/editor/contrib/anchorSelect/anchorSelect.css) | CSS | 4 | 4 | 2 | 10 |
| [public/monaco-editor/esm/vs/editor/contrib/anchorSelect/anchorSelect.js](/public/monaco-editor/esm/vs/editor/contrib/anchorSelect/anchorSelect.js) | JavaScript | 173 | 4 | 1 | 178 |
| [public/monaco-editor/esm/vs/editor/contrib/bracketMatching/bracketMatching.css](/public/monaco-editor/esm/vs/editor/contrib/bracketMatching/bracketMatching.css) | CSS | 3 | 4 | 2 | 9 |
| [public/monaco-editor/esm/vs/editor/contrib/bracketMatching/bracketMatching.js](/public/monaco-editor/esm/vs/editor/contrib/bracketMatching/bracketMatching.js) | JavaScript | 308 | 16 | 1 | 325 |
| [public/monaco-editor/esm/vs/editor/contrib/caretOperations/caretOperations.js](/public/monaco-editor/esm/vs/editor/contrib/caretOperations/caretOperations.js) | JavaScript | 45 | 4 | 1 | 50 |
| [public/monaco-editor/esm/vs/editor/contrib/caretOperations/moveCaretCommand.js](/public/monaco-editor/esm/vs/editor/contrib/caretOperations/moveCaretCommand.js) | JavaScript | 42 | 4 | 1 | 47 |
| [public/monaco-editor/esm/vs/editor/contrib/caretOperations/transpose.js](/public/monaco-editor/esm/vs/editor/contrib/caretOperations/transpose.js) | JavaScript | 58 | 7 | 1 | 66 |
| [public/monaco-editor/esm/vs/editor/contrib/clipboard/clipboard.js](/public/monaco-editor/esm/vs/editor/contrib/clipboard/clipboard.js) | JavaScript | 219 | 21 | 1 | 241 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/codeAction.js](/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeAction.js) | JavaScript | 214 | 8 | 1 | 223 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionCommands.js](/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionCommands.js) | JavaScript | 349 | 4 | 1 | 354 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionContributions.js](/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionContributions.js) | JavaScript | 10 | 4 | 1 | 15 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionMenu.js](/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionMenu.js) | JavaScript | 180 | 10 | 1 | 191 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionModel.js](/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionModel.js) | JavaScript | 223 | 10 | 1 | 234 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionUi.js](/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionUi.js) | JavaScript | 155 | 8 | 1 | 164 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/lightBulbWidget.css](/public/monaco-editor/esm/vs/editor/contrib/codeAction/lightBulbWidget.css) | CSS | 10 | 4 | 3 | 17 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/lightBulbWidget.js](/public/monaco-editor/esm/vs/editor/contrib/codeAction/lightBulbWidget.js) | JavaScript | 202 | 18 | 1 | 221 |
| [public/monaco-editor/esm/vs/editor/contrib/codeAction/types.js](/public/monaco-editor/esm/vs/editor/contrib/codeAction/types.js) | JavaScript | 103 | 9 | 1 | 113 |
| [public/monaco-editor/esm/vs/editor/contrib/codelens/codeLensCache.js](/public/monaco-editor/esm/vs/editor/contrib/codelens/codeLensCache.js) | JavaScript | 100 | 11 | 1 | 112 |
| [public/monaco-editor/esm/vs/editor/contrib/codelens/codelens.js](/public/monaco-editor/esm/vs/editor/contrib/codelens/codelens.js) | JavaScript | 104 | 7 | 1 | 112 |
| [public/monaco-editor/esm/vs/editor/contrib/codelens/codelensController.js](/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensController.js) | JavaScript | 434 | 23 | 1 | 458 |
| [public/monaco-editor/esm/vs/editor/contrib/codelens/codelensWidget.css](/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensWidget.css) | CSS | 40 | 4 | 10 | 54 |
| [public/monaco-editor/esm/vs/editor/contrib/codelens/codelensWidget.js](/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensWidget.js) | JavaScript | 248 | 11 | 1 | 260 |
| [public/monaco-editor/esm/vs/editor/contrib/colorPicker/color.js](/public/monaco-editor/esm/vs/editor/contrib/colorPicker/color.js) | JavaScript | 66 | 4 | 1 | 71 |
| [public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorContributions.js](/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorContributions.js) | JavaScript | 36 | 5 | 1 | 42 |
| [public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorDetector.js](/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorDetector.js) | JavaScript | 196 | 5 | 1 | 202 |
| [public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPicker.css](/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPicker.css) | CSS | 114 | 7 | 24 | 145 |
| [public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPickerModel.js](/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPickerModel.js) | JavaScript | 53 | 4 | 1 | 58 |
| [public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPickerWidget.js](/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPickerWidget.js) | JavaScript | 259 | 6 | 1 | 266 |
| [public/monaco-editor/esm/vs/editor/contrib/comment/blockCommentCommand.js](/public/monaco-editor/esm/vs/editor/contrib/comment/blockCommentCommand.js) | JavaScript | 128 | 18 | 1 | 147 |
| [public/monaco-editor/esm/vs/editor/contrib/comment/comment.js](/public/monaco-editor/esm/vs/editor/contrib/comment/comment.js) | JavaScript | 135 | 8 | 1 | 144 |
| [public/monaco-editor/esm/vs/editor/contrib/comment/lineCommentCommand.js](/public/monaco-editor/esm/vs/editor/contrib/comment/lineCommentCommand.js) | JavaScript | 279 | 44 | 1 | 324 |
| [public/monaco-editor/esm/vs/editor/contrib/contextmenu/contextmenu.js](/public/monaco-editor/esm/vs/editor/contrib/contextmenu/contextmenu.js) | JavaScript | 224 | 20 | 1 | 245 |
| [public/monaco-editor/esm/vs/editor/contrib/cursorUndo/cursorUndo.js](/public/monaco-editor/esm/vs/editor/contrib/cursorUndo/cursorUndo.js) | JavaScript | 127 | 5 | 1 | 133 |
| [public/monaco-editor/esm/vs/editor/contrib/dnd/dnd.css](/public/monaco-editor/esm/vs/editor/contrib/dnd/dnd.css) | CSS | 22 | 4 | 2 | 28 |
| [public/monaco-editor/esm/vs/editor/contrib/dnd/dnd.js](/public/monaco-editor/esm/vs/editor/contrib/dnd/dnd.js) | JavaScript | 190 | 6 | 1 | 197 |
| [public/monaco-editor/esm/vs/editor/contrib/dnd/dragAndDropCommand.js](/public/monaco-editor/esm/vs/editor/contrib/dnd/dragAndDropCommand.js) | JavaScript | 53 | 9 | 1 | 63 |
| [public/monaco-editor/esm/vs/editor/contrib/documentSymbols/documentSymbols.js](/public/monaco-editor/esm/vs/editor/contrib/documentSymbols/documentSymbols.js) | JavaScript | 41 | 4 | 1 | 46 |
| [public/monaco-editor/esm/vs/editor/contrib/documentSymbols/outlineModel.js](/public/monaco-editor/esm/vs/editor/contrib/documentSymbols/outlineModel.js) | JavaScript | 235 | 13 | 1 | 249 |
| [public/monaco-editor/esm/vs/editor/contrib/find/findController.js](/public/monaco-editor/esm/vs/editor/contrib/find/findController.js) | JavaScript | 869 | 23 | 1 | 893 |
| [public/monaco-editor/esm/vs/editor/contrib/find/findDecorations.js](/public/monaco-editor/esm/vs/editor/contrib/find/findDecorations.js) | JavaScript | 275 | 14 | 1 | 290 |
| [public/monaco-editor/esm/vs/editor/contrib/find/findModel.js](/public/monaco-editor/esm/vs/editor/contrib/find/findModel.js) | JavaScript | 448 | 30 | 1 | 479 |
| [public/monaco-editor/esm/vs/editor/contrib/find/findOptionsWidget.js](/public/monaco-editor/esm/vs/editor/contrib/find/findOptionsWidget.js) | JavaScript | 166 | 5 | 1 | 172 |
| [public/monaco-editor/esm/vs/editor/contrib/find/findState.js](/public/monaco-editor/esm/vs/editor/contrib/find/findState.js) | JavaScript | 215 | 5 | 1 | 221 |
| [public/monaco-editor/esm/vs/editor/contrib/find/findWidget.css](/public/monaco-editor/esm/vs/editor/contrib/find/findWidget.css) | CSS | 176 | 12 | 37 | 225 |
| [public/monaco-editor/esm/vs/editor/contrib/find/findWidget.js](/public/monaco-editor/esm/vs/editor/contrib/find/findWidget.js) | JavaScript | 1,132 | 57 | 1 | 1,190 |
| [public/monaco-editor/esm/vs/editor/contrib/find/replaceAllCommand.js](/public/monaco-editor/esm/vs/editor/contrib/find/replaceAllCommand.js) | JavaScript | 43 | 8 | 1 | 52 |
| [public/monaco-editor/esm/vs/editor/contrib/find/replacePattern.js](/public/monaco-editor/esm/vs/editor/contrib/find/replacePattern.js) | JavaScript | 235 | 51 | 1 | 287 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/folding.css](/public/monaco-editor/esm/vs/editor/contrib/folding/folding.css) | CSS | 24 | 4 | 4 | 32 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/folding.js](/public/monaco-editor/esm/vs/editor/contrib/folding/folding.js) | JavaScript | 948 | 22 | 1 | 971 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/foldingDecorations.js](/public/monaco-editor/esm/vs/editor/contrib/folding/foldingDecorations.js) | JavaScript | 65 | 4 | 1 | 70 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/foldingModel.js](/public/monaco-editor/esm/vs/editor/contrib/folding/foldingModel.js) | JavaScript | 426 | 86 | 1 | 513 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/foldingRanges.js](/public/monaco-editor/esm/vs/editor/contrib/folding/foldingRanges.js) | JavaScript | 163 | 4 | 1 | 168 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/hiddenRangeModel.js](/public/monaco-editor/esm/vs/editor/contrib/folding/hiddenRangeModel.js) | JavaScript | 134 | 9 | 1 | 144 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/indentRangeProvider.js](/public/monaco-editor/esm/vs/editor/contrib/folding/indentRangeProvider.js) | JavaScript | 142 | 17 | 1 | 160 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/intializingRangeProvider.js](/public/monaco-editor/esm/vs/editor/contrib/folding/intializingRangeProvider.js) | JavaScript | 48 | 4 | 1 | 53 |
| [public/monaco-editor/esm/vs/editor/contrib/folding/syntaxRangeProvider.js](/public/monaco-editor/esm/vs/editor/contrib/folding/syntaxRangeProvider.js) | JavaScript | 162 | 4 | 1 | 167 |
| [public/monaco-editor/esm/vs/editor/contrib/fontZoom/fontZoom.js](/public/monaco-editor/esm/vs/editor/contrib/fontZoom/fontZoom.js) | JavaScript | 45 | 4 | 1 | 50 |
| [public/monaco-editor/esm/vs/editor/contrib/format/format.js](/public/monaco-editor/esm/vs/editor/contrib/format/format.js) | JavaScript | 380 | 15 | 1 | 396 |
| [public/monaco-editor/esm/vs/editor/contrib/format/formatActions.js](/public/monaco-editor/esm/vs/editor/contrib/format/formatActions.js) | JavaScript | 244 | 21 | 1 | 266 |
| [public/monaco-editor/esm/vs/editor/contrib/format/formattingEdit.js](/public/monaco-editor/esm/vs/editor/contrib/format/formattingEdit.js) | JavaScript | 46 | 5 | 1 | 52 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoError/gotoError.js](/public/monaco-editor/esm/vs/editor/contrib/gotoError/gotoError.js) | JavaScript | 273 | 9 | 1 | 283 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoError/gotoErrorWidget.js](/public/monaco-editor/esm/vs/editor/contrib/gotoError/gotoErrorWidget.js) | JavaScript | 331 | 10 | 1 | 342 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoError/markerNavigationService.js](/public/monaco-editor/esm/vs/editor/contrib/gotoError/markerNavigationService.js) | JavaScript | 184 | 6 | 1 | 191 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoError/media/gotoErrorWidget.css](/public/monaco-editor/esm/vs/editor/contrib/gotoError/media/gotoErrorWidget.css) | CSS | 58 | 6 | 16 | 80 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/goToCommands.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/goToCommands.js) | JavaScript | 680 | 28 | 1 | 709 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/goToSymbol.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/goToSymbol.js) | JavaScript | 82 | 6 | 1 | 89 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/clickLinkGesture.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/clickLinkGesture.js) | JavaScript | 130 | 14 | 1 | 145 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.css](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.css) | CSS | 4 | 4 | 1 | 9 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.js) | JavaScript | 280 | 21 | 1 | 302 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesController.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesController.js) | JavaScript | 369 | 22 | 1 | 392 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesTree.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesTree.js) | JavaScript | 184 | 18 | 1 | 203 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesWidget.css](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesWidget.css) | CSS | 62 | 6 | 17 | 85 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesWidget.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesWidget.js) | JavaScript | 469 | 25 | 1 | 495 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/referencesModel.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/referencesModel.js) | JavaScript | 243 | 8 | 1 | 252 |
| [public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/symbolNavigation.js](/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/symbolNavigation.js) | JavaScript | 170 | 8 | 1 | 179 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/colorHoverParticipant.js](/public/monaco-editor/esm/vs/editor/contrib/hover/colorHoverParticipant.js) | JavaScript | 146 | 8 | 1 | 155 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/getHover.js](/public/monaco-editor/esm/vs/editor/contrib/hover/getHover.js) | JavaScript | 49 | 4 | 1 | 54 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/hover.js](/public/monaco-editor/esm/vs/editor/contrib/hover/hover.js) | JavaScript | 286 | 12 | 1 | 299 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/hoverOperation.js](/public/monaco-editor/esm/vs/editor/contrib/hover/hoverOperation.js) | JavaScript | 158 | 4 | 1 | 163 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/hoverTypes.js](/public/monaco-editor/esm/vs/editor/contrib/hover/hoverTypes.js) | JavaScript | 27 | 4 | 1 | 32 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/markdownHoverParticipant.js](/public/monaco-editor/esm/vs/editor/contrib/hover/markdownHoverParticipant.js) | JavaScript | 127 | 5 | 1 | 133 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/markerHoverParticipant.js](/public/monaco-editor/esm/vs/editor/contrib/hover/markerHoverParticipant.js) | JavaScript | 224 | 8 | 1 | 233 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/modesContentHover.js](/public/monaco-editor/esm/vs/editor/contrib/hover/modesContentHover.js) | JavaScript | 467 | 24 | 1 | 492 |
| [public/monaco-editor/esm/vs/editor/contrib/hover/modesGlyphHover.js](/public/monaco-editor/esm/vs/editor/contrib/hover/modesGlyphHover.js) | JavaScript | 163 | 7 | 1 | 171 |
| [public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace/inPlaceReplace.js](/public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace/inPlaceReplace.js) | JavaScript | 148 | 13 | 1 | 162 |
| [public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace/inPlaceReplaceCommand.js](/public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace/inPlaceReplaceCommand.js) | JavaScript | 19 | 5 | 1 | 25 |
| [public/monaco-editor/esm/vs/editor/contrib/indentation/indentUtils.js](/public/monaco-editor/esm/vs/editor/contrib/indentation/indentUtils.js) | JavaScript | 27 | 4 | 1 | 32 |
| [public/monaco-editor/esm/vs/editor/contrib/indentation/indentation.js](/public/monaco-editor/esm/vs/editor/contrib/indentation/indentation.js) | JavaScript | 551 | 25 | 1 | 577 |
| [public/monaco-editor/esm/vs/editor/contrib/inlayHints/inlayHintsController.js](/public/monaco-editor/esm/vs/editor/contrib/inlayHints/inlayHintsController.js) | JavaScript | 305 | 10 | 1 | 316 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/consts.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/consts.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostText.css](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostText.css) | CSS | 23 | 4 | 7 | 34 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostText.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostText.js) | JavaScript | 78 | 7 | 1 | 86 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextController.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextController.js) | JavaScript | 269 | 10 | 1 | 280 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextModel.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextModel.js) | JavaScript | 152 | 7 | 1 | 160 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextWidget.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextWidget.js) | JavaScript | 320 | 7 | 1 | 328 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText.js) | JavaScript | 136 | 32 | 1 | 169 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant.js) | JavaScript | 156 | 7 | 1 | 164 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionsModel.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionsModel.js) | JavaScript | 482 | 28 | 1 | 511 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/suggestWidgetInlineCompletionProvider.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/suggestWidgetInlineCompletionProvider.js) | JavaScript | 194 | 15 | 1 | 210 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/suggestWidgetPreviewModel.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/suggestWidgetPreviewModel.js) | JavaScript | 126 | 7 | 1 | 134 |
| [public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/utils.js](/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/utils.js) | JavaScript | 21 | 4 | 1 | 26 |
| [public/monaco-editor/esm/vs/editor/contrib/lineSelection/lineSelection.js](/public/monaco-editor/esm/vs/editor/contrib/lineSelection/lineSelection.js) | JavaScript | 30 | 4 | 1 | 35 |
| [public/monaco-editor/esm/vs/editor/contrib/linesOperations/copyLinesCommand.js](/public/monaco-editor/esm/vs/editor/contrib/linesOperations/copyLinesCommand.js) | JavaScript | 65 | 5 | 1 | 71 |
| [public/monaco-editor/esm/vs/editor/contrib/linesOperations/linesOperations.js](/public/monaco-editor/esm/vs/editor/contrib/linesOperations/linesOperations.js) | JavaScript | 967 | 26 | 1 | 994 |
| [public/monaco-editor/esm/vs/editor/contrib/linesOperations/moveLinesCommand.js](/public/monaco-editor/esm/vs/editor/contrib/linesOperations/moveLinesCommand.js) | JavaScript | 298 | 38 | 1 | 337 |
| [public/monaco-editor/esm/vs/editor/contrib/linesOperations/sortLinesCommand.js](/public/monaco-editor/esm/vs/editor/contrib/linesOperations/sortLinesCommand.js) | JavaScript | 73 | 10 | 1 | 84 |
| [public/monaco-editor/esm/vs/editor/contrib/linkedEditing/linkedEditing.js](/public/monaco-editor/esm/vs/editor/contrib/linkedEditing/linkedEditing.js) | JavaScript | 353 | 12 | 1 | 366 |
| [public/monaco-editor/esm/vs/editor/contrib/links/getLinks.js](/public/monaco-editor/esm/vs/editor/contrib/links/getLinks.js) | JavaScript | 146 | 13 | 1 | 160 |
| [public/monaco-editor/esm/vs/editor/contrib/links/links.css](/public/monaco-editor/esm/vs/editor/contrib/links/links.css) | CSS | 8 | 4 | 2 | 14 |
| [public/monaco-editor/esm/vs/editor/contrib/links/links.js](/public/monaco-editor/esm/vs/editor/contrib/links/links.js) | JavaScript | 368 | 12 | 1 | 381 |
| [public/monaco-editor/esm/vs/editor/contrib/message/messageController.css](/public/monaco-editor/esm/vs/editor/contrib/message/messageController.css) | CSS | 55 | 4 | 12 | 71 |
| [public/monaco-editor/esm/vs/editor/contrib/message/messageController.js](/public/monaco-editor/esm/vs/editor/contrib/message/messageController.js) | JavaScript | 138 | 11 | 1 | 150 |
| [public/monaco-editor/esm/vs/editor/contrib/multicursor/multicursor.js](/public/monaco-editor/esm/vs/editor/contrib/multicursor/multicursor.js) | JavaScript | 900 | 39 | 1 | 940 |
| [public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHints.css](/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHints.css) | CSS | 86 | 4 | 21 | 111 |
| [public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHints.js](/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHints.js) | JavaScript | 106 | 4 | 1 | 111 |
| [public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHintsModel.js](/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHintsModel.js) | JavaScript | 250 | 11 | 1 | 262 |
| [public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHintsWidget.js](/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHintsWidget.js) | JavaScript | 354 | 8 | 1 | 363 |
| [public/monaco-editor/esm/vs/editor/contrib/parameterHints/provideSignatureHelp.js](/public/monaco-editor/esm/vs/editor/contrib/parameterHints/provideSignatureHelp.js) | JavaScript | 61 | 4 | 1 | 66 |
| [public/monaco-editor/esm/vs/editor/contrib/peekView/media/peekViewWidget.css](/public/monaco-editor/esm/vs/editor/contrib/peekView/media/peekViewWidget.css) | CSS | 55 | 4 | 15 | 74 |
| [public/monaco-editor/esm/vs/editor/contrib/peekView/peekView.js](/public/monaco-editor/esm/vs/editor/contrib/peekView/peekView.js) | JavaScript | 228 | 6 | 1 | 235 |
| [public/monaco-editor/esm/vs/editor/contrib/quickAccess/commandsQuickAccess.js](/public/monaco-editor/esm/vs/editor/contrib/quickAccess/commandsQuickAccess.js) | JavaScript | 22 | 4 | 1 | 27 |
| [public/monaco-editor/esm/vs/editor/contrib/quickAccess/editorNavigationQuickAccess.js](/public/monaco-editor/esm/vs/editor/contrib/quickAccess/editorNavigationQuickAccess.js) | JavaScript | 112 | 35 | 1 | 148 |
| [public/monaco-editor/esm/vs/editor/contrib/quickAccess/gotoLineQuickAccess.js](/public/monaco-editor/esm/vs/editor/contrib/quickAccess/gotoLineQuickAccess.js) | JavaScript | 112 | 15 | 1 | 128 |
| [public/monaco-editor/esm/vs/editor/contrib/quickAccess/gotoSymbolQuickAccess.js](/public/monaco-editor/esm/vs/editor/contrib/quickAccess/gotoSymbolQuickAccess.js) | JavaScript | 339 | 51 | 1 | 391 |
| [public/monaco-editor/esm/vs/editor/contrib/rename/rename.js](/public/monaco-editor/esm/vs/editor/contrib/rename/rename.js) | JavaScript | 341 | 12 | 1 | 354 |
| [public/monaco-editor/esm/vs/editor/contrib/rename/renameInputField.css](/public/monaco-editor/esm/vs/editor/contrib/rename/renameInputField.css) | CSS | 18 | 4 | 6 | 28 |
| [public/monaco-editor/esm/vs/editor/contrib/rename/renameInputField.js](/public/monaco-editor/esm/vs/editor/contrib/rename/renameInputField.js) | JavaScript | 173 | 7 | 1 | 181 |
| [public/monaco-editor/esm/vs/editor/contrib/smartSelect/bracketSelections.js](/public/monaco-editor/esm/vs/editor/contrib/smartSelect/bracketSelections.js) | JavaScript | 140 | 15 | 1 | 156 |
| [public/monaco-editor/esm/vs/editor/contrib/smartSelect/smartSelect.js](/public/monaco-editor/esm/vs/editor/contrib/smartSelect/smartSelect.js) | JavaScript | 254 | 21 | 1 | 276 |
| [public/monaco-editor/esm/vs/editor/contrib/smartSelect/wordSelections.js](/public/monaco-editor/esm/vs/editor/contrib/smartSelect/wordSelections.js) | JavaScript | 64 | 10 | 1 | 75 |
| [public/monaco-editor/esm/vs/editor/contrib/snippet/snippetController2.js](/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetController2.js) | JavaScript | 210 | 26 | 1 | 237 |
| [public/monaco-editor/esm/vs/editor/contrib/snippet/snippetParser.js](/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetParser.js) | JavaScript | 816 | 52 | 1 | 869 |
| [public/monaco-editor/esm/vs/editor/contrib/snippet/snippetSession.css](/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetSession.css) | CSS | 13 | 4 | 3 | 20 |
| [public/monaco-editor/esm/vs/editor/contrib/snippet/snippetSession.js](/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetSession.js) | JavaScript | 425 | 86 | 1 | 512 |
| [public/monaco-editor/esm/vs/editor/contrib/snippet/snippetVariables.js](/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetVariables.js) | JavaScript | 302 | 18 | 1 | 321 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/completionModel.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/completionModel.js) | JavaScript | 197 | 37 | 1 | 235 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/media/suggest.css](/public/monaco-editor/esm/vs/editor/contrib/suggest/media/suggest.css) | CSS | 337 | 20 | 97 | 454 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/resizable.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/resizable.js) | JavaScript | 143 | 4 | 1 | 148 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggest.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggest.js) | JavaScript | 294 | 27 | 1 | 322 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestAlternatives.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestAlternatives.js) | JavaScript | 85 | 7 | 1 | 93 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestCommitCharacters.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestCommitCharacters.js) | JavaScript | 41 | 7 | 1 | 49 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestController.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestController.js) | JavaScript | 773 | 48 | 1 | 822 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestMemory.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestMemory.js) | JavaScript | 236 | 14 | 1 | 251 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestModel.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestModel.js) | JavaScript | 572 | 69 | 1 | 642 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestOvertypingCapturer.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestOvertypingCapturer.js) | JavaScript | 53 | 6 | 1 | 60 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidget.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidget.js) | JavaScript | 777 | 33 | 1 | 811 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetDetails.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetDetails.js) | JavaScript | 367 | 11 | 1 | 379 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetRenderer.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetRenderer.js) | JavaScript | 204 | 8 | 1 | 213 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetStatus.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetStatus.js) | JavaScript | 82 | 4 | 1 | 87 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/wordContextKey.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/wordContextKey.js) | JavaScript | 60 | 5 | 1 | 66 |
| [public/monaco-editor/esm/vs/editor/contrib/suggest/wordDistance.js](/public/monaco-editor/esm/vs/editor/contrib/suggest/wordDistance.js) | JavaScript | 67 | 5 | 1 | 73 |
| [public/monaco-editor/esm/vs/editor/contrib/symbolIcons/symbolIcons.js](/public/monaco-editor/esm/vs/editor/contrib/symbolIcons/symbolIcons.js) | JavaScript | 304 | 4 | 1 | 309 |
| [public/monaco-editor/esm/vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.js](/public/monaco-editor/esm/vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.js) | JavaScript | 33 | 4 | 1 | 38 |
| [public/monaco-editor/esm/vs/editor/contrib/tokenization/tokenization.js](/public/monaco-editor/esm/vs/editor/contrib/tokenization/tokenization.js) | JavaScript | 25 | 4 | 1 | 30 |
| [public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/bannerController.css](/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/bannerController.css) | CSS | 66 | 4 | 16 | 86 |
| [public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/bannerController.js](/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/bannerController.js) | JavaScript | 106 | 11 | 1 | 118 |
| [public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.css](/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.css) | CSS | 4 | 4 | 2 | 10 |
| [public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.js](/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.js) | JavaScript | 575 | 13 | 1 | 589 |
| [public/monaco-editor/esm/vs/editor/contrib/unusualLineTerminators/unusualLineTerminators.js](/public/monaco-editor/esm/vs/editor/contrib/unusualLineTerminators/unusualLineTerminators.js) | JavaScript | 99 | 9 | 1 | 109 |
| [public/monaco-editor/esm/vs/editor/contrib/viewportSemanticTokens/viewportSemanticTokens.js](/public/monaco-editor/esm/vs/editor/contrib/viewportSemanticTokens/viewportSemanticTokens.js) | JavaScript | 112 | 4 | 1 | 117 |
| [public/monaco-editor/esm/vs/editor/contrib/wordHighlighter/wordHighlighter.js](/public/monaco-editor/esm/vs/editor/contrib/wordHighlighter/wordHighlighter.js) | JavaScript | 519 | 38 | 1 | 558 |
| [public/monaco-editor/esm/vs/editor/contrib/wordOperations/wordOperations.js](/public/monaco-editor/esm/vs/editor/contrib/wordOperations/wordOperations.js) | JavaScript | 435 | 7 | 1 | 443 |
| [public/monaco-editor/esm/vs/editor/contrib/wordPartOperations/wordPartOperations.js](/public/monaco-editor/esm/vs/editor/contrib/wordPartOperations/wordPartOperations.js) | JavaScript | 136 | 6 | 1 | 143 |
| [public/monaco-editor/esm/vs/editor/contrib/zoneWidget/zoneWidget.css](/public/monaco-editor/esm/vs/editor/contrib/zoneWidget/zoneWidget.css) | CSS | 11 | 4 | 3 | 18 |
| [public/monaco-editor/esm/vs/editor/contrib/zoneWidget/zoneWidget.js](/public/monaco-editor/esm/vs/editor/contrib/zoneWidget/zoneWidget.js) | JavaScript | 378 | 17 | 1 | 396 |
| [public/monaco-editor/esm/vs/editor/edcore.main.js](/public/monaco-editor/esm/vs/editor/edcore.main.js) | JavaScript | 11 | 4 | 1 | 16 |
| [public/monaco-editor/esm/vs/editor/editor.all.js](/public/monaco-editor/esm/vs/editor/editor.all.js) | JavaScript | 49 | 6 | 1 | 56 |
| [public/monaco-editor/esm/vs/editor/editor.api.d.ts](/public/monaco-editor/esm/vs/editor/editor.api.d.ts) | TypeScript | 2,976 | 4,449 | 359 | 7,784 |
| [public/monaco-editor/esm/vs/editor/editor.api.js](/public/monaco-editor/esm/vs/editor/editor.api.js) | JavaScript | 49 | 7 | 1 | 57 |
| [public/monaco-editor/esm/vs/editor/editor.main.js](/public/monaco-editor/esm/vs/editor/editor.main.js) | JavaScript | 6 | 0 | 1 | 7 |
| [public/monaco-editor/esm/vs/editor/editor.worker.js](/public/monaco-editor/esm/vs/editor/editor.worker.js) | JavaScript | 20 | 5 | 1 | 26 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.css](/public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.css) | CSS | 5 | 4 | 1 | 10 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.js](/public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.js) | JavaScript | 302 | 6 | 1 | 309 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/colorizer.js](/public/monaco-editor/esm/vs/editor/standalone/browser/colorizer.js) | JavaScript | 150 | 7 | 1 | 158 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.css](/public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.css) | CSS | 18 | 4 | 2 | 24 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.js](/public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.js) | JavaScript | 66 | 5 | 1 | 72 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens/inspectTokens.css](/public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens/inspectTokens.css) | CSS | 29 | 4 | 8 | 41 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens/inspectTokens.js](/public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens/inspectTokens.js) | JavaScript | 249 | 5 | 1 | 255 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess.js](/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess.js) | JavaScript | 81 | 4 | 1 | 86 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess.js](/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess.js) | JavaScript | 58 | 4 | 1 | 63 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess.js](/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess.js) | JavaScript | 66 | 4 | 1 | 71 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess.js](/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess.js) | JavaScript | 9 | 4 | 1 | 14 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInput.css](/public/monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInput.css) | CSS | 39 | 4 | 9 | 52 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInputServiceImpl.js](/public/monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInputServiceImpl.js) | JavaScript | 114 | 7 | 1 | 122 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch.js](/public/monaco-editor/esm/vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch.js) | JavaScript | 32 | 4 | 1 | 37 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/simpleServices.js](/public/monaco-editor/esm/vs/editor/standalone/browser/simpleServices.js) | JavaScript | 461 | 10 | 1 | 472 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/standalone-tokens.css](/public/monaco-editor/esm/vs/editor/standalone/browser/standalone-tokens.css) | CSS | 190 | 36 | 24 | 250 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/standaloneCodeEditor.js](/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneCodeEditor.js) | JavaScript | 304 | 26 | 1 | 331 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/standaloneCodeServiceImpl.js](/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneCodeServiceImpl.js) | JavaScript | 86 | 5 | 1 | 92 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/standaloneEditor.js](/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneEditor.js) | JavaScript | 215 | 95 | 1 | 311 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/standaloneLanguages.js](/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneLanguages.js) | JavaScript | 334 | 125 | 1 | 460 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/standaloneServices.js](/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneServices.js) | JavaScript | 180 | 9 | 1 | 190 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/standaloneThemeServiceImpl.js](/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneThemeServiceImpl.js) | JavaScript | 306 | 8 | 1 | 315 |
| [public/monaco-editor/esm/vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast.js](/public/monaco-editor/esm/vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast.js) | JavaScript | 26 | 5 | 1 | 32 |
| [public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchCommon.js](/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchCommon.js) | JavaScript | 87 | 37 | 1 | 125 |
| [public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchCompile.js](/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchCompile.js) | JavaScript | 445 | 70 | 1 | 516 |
| [public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchLexer.js](/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchLexer.js) | JavaScript | 680 | 55 | 1 | 736 |
| [public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchTypes.js](/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchTypes.js) | JavaScript | 1 | 4 | 1 | 6 |
| [public/monaco-editor/esm/vs/editor/standalone/common/standaloneThemeService.js](/public/monaco-editor/esm/vs/editor/standalone/common/standaloneThemeService.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/editor/standalone/common/themes.js](/public/monaco-editor/esm/vs/editor/standalone/common/themes.js) | JavaScript | 169 | 10 | 1 | 180 |
| [public/monaco-editor/esm/vs/language/css/css.worker.js](/public/monaco-editor/esm/vs/language/css/css.worker.js) | JavaScript | 35,424 | 51 | 46 | 35,521 |
| [public/monaco-editor/esm/vs/language/css/cssMode.js](/public/monaco-editor/esm/vs/language/css/cssMode.js) | JavaScript | 1,930 | 11 | 7 | 1,948 |
| [public/monaco-editor/esm/vs/language/css/monaco.contribution.js](/public/monaco-editor/esm/vs/language/css/monaco.contribution.js) | JavaScript | 116 | 8 | 4 | 128 |
| [public/monaco-editor/esm/vs/language/html/html.worker.js](/public/monaco-editor/esm/vs/language/html/html.worker.js) | JavaScript | 16,029 | 41 | 36 | 16,106 |
| [public/monaco-editor/esm/vs/language/html/htmlMode.js](/public/monaco-editor/esm/vs/language/html/htmlMode.js) | JavaScript | 1,838 | 11 | 7 | 1,856 |
| [public/monaco-editor/esm/vs/language/html/monaco.contribution.js](/public/monaco-editor/esm/vs/language/html/monaco.contribution.js) | JavaScript | 132 | 8 | 4 | 144 |
| [public/monaco-editor/esm/vs/language/json/json.worker.js](/public/monaco-editor/esm/vs/language/json/json.worker.js) | JavaScript | 7,218 | 35 | 30 | 7,283 |
| [public/monaco-editor/esm/vs/language/json/jsonMode.js](/public/monaco-editor/esm/vs/language/json/jsonMode.js) | JavaScript | 2,303 | 191 | 12 | 2,506 |
| [public/monaco-editor/esm/vs/language/json/monaco.contribution.js](/public/monaco-editor/esm/vs/language/json/monaco.contribution.js) | JavaScript | 91 | 8 | 4 | 103 |
| [public/monaco-editor/esm/vs/language/typescript/monaco.contribution.js](/public/monaco-editor/esm/vs/language/typescript/monaco.contribution.js) | JavaScript | 239 | 9 | 5 | 253 |
| [public/monaco-editor/esm/vs/language/typescript/ts.worker.js](/public/monaco-editor/esm/vs/language/typescript/ts.worker.js) | JavaScript | 142,897 | 15,166 | 2,323 | 160,386 |
| [public/monaco-editor/esm/vs/language/typescript/tsMode.js](/public/monaco-editor/esm/vs/language/typescript/tsMode.js) | JavaScript | 1,286 | 13 | 10 | 1,309 |
| [public/monaco-editor/esm/vs/nls.js](/public/monaco-editor/esm/vs/nls.js) | JavaScript | 16 | 4 | 1 | 21 |
| [public/monaco-editor/esm/vs/platform/accessibility/browser/accessibilityService.js](/public/monaco-editor/esm/vs/platform/accessibility/browser/accessibilityService.js) | JavaScript | 48 | 0 | 1 | 49 |
| [public/monaco-editor/esm/vs/platform/accessibility/common/accessibility.js](/public/monaco-editor/esm/vs/platform/accessibility/common/accessibility.js) | JavaScript | 4 | 4 | 1 | 9 |
| [public/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.css](/public/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.css) | CSS | 49 | 4 | 12 | 65 |
| [public/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.js](/public/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.js) | JavaScript | 397 | 17 | 1 | 415 |
| [public/monaco-editor/esm/vs/platform/actions/common/actions.js](/public/monaco-editor/esm/vs/platform/actions/common/actions.js) | JavaScript | 204 | 12 | 1 | 217 |
| [public/monaco-editor/esm/vs/platform/actions/common/menuService.js](/public/monaco-editor/esm/vs/platform/actions/common/menuService.js) | JavaScript | 167 | 30 | 1 | 198 |
| [public/monaco-editor/esm/vs/platform/browser/contextScopedHistoryWidget.js](/public/monaco-editor/esm/vs/platform/browser/contextScopedHistoryWidget.js) | JavaScript | 85 | 4 | 1 | 90 |
| [public/monaco-editor/esm/vs/platform/browser/historyWidgetKeybindingHint.js](/public/monaco-editor/esm/vs/platform/browser/historyWidgetKeybindingHint.js) | JavaScript | 4 | 4 | 1 | 9 |
| [public/monaco-editor/esm/vs/platform/clipboard/browser/clipboardService.js](/public/monaco-editor/esm/vs/platform/clipboard/browser/clipboardService.js) | JavaScript | 68 | 13 | 1 | 82 |
| [public/monaco-editor/esm/vs/platform/clipboard/common/clipboardService.js](/public/monaco-editor/esm/vs/platform/clipboard/common/clipboardService.js) | JavaScript | 2 | 0 | 1 | 3 |
| [public/monaco-editor/esm/vs/platform/commands/common/commands.js](/public/monaco-editor/esm/vs/platform/commands/common/commands.js) | JavaScript | 81 | 7 | 1 | 89 |
| [public/monaco-editor/esm/vs/platform/configuration/common/configuration.js](/public/monaco-editor/esm/vs/platform/configuration/common/configuration.js) | JavaScript | 75 | 8 | 1 | 84 |
| [public/monaco-editor/esm/vs/platform/configuration/common/configurationModels.js](/public/monaco-editor/esm/vs/platform/configuration/common/configurationModels.js) | JavaScript | 366 | 9 | 1 | 376 |
| [public/monaco-editor/esm/vs/platform/configuration/common/configurationRegistry.js](/public/monaco-editor/esm/vs/platform/configuration/common/configurationRegistry.js) | JavaScript | 279 | 9 | 1 | 289 |
| [public/monaco-editor/esm/vs/platform/contextkey/browser/contextKeyService.js](/public/monaco-editor/esm/vs/platform/contextkey/browser/contextKeyService.js) | JavaScript | 380 | 21 | 1 | 402 |
| [public/monaco-editor/esm/vs/platform/contextkey/common/contextkey.js](/public/monaco-editor/esm/vs/platform/contextkey/common/contextkey.js) | JavaScript | 1,170 | 38 | 1 | 1,209 |
| [public/monaco-editor/esm/vs/platform/contextkey/common/contextkeys.js](/public/monaco-editor/esm/vs/platform/contextkey/common/contextkeys.js) | JavaScript | 11 | 4 | 1 | 16 |
| [public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.css](/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.css) | CSS | 3 | 4 | 3 | 10 |
| [public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.js](/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.js) | JavaScript | 116 | 8 | 1 | 125 |
| [public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuService.js](/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuService.js) | JavaScript | 47 | 5 | 1 | 53 |
| [public/monaco-editor/esm/vs/platform/contextview/browser/contextView.js](/public/monaco-editor/esm/vs/platform/contextview/browser/contextView.js) | JavaScript | 3 | 4 | 1 | 8 |
| [public/monaco-editor/esm/vs/platform/contextview/browser/contextViewService.js](/public/monaco-editor/esm/vs/platform/contextview/browser/contextViewService.js) | JavaScript | 61 | 5 | 1 | 67 |
| [public/monaco-editor/esm/vs/platform/dialogs/common/dialogs.js](/public/monaco-editor/esm/vs/platform/dialogs/common/dialogs.js) | JavaScript | 2 | 0 | 1 | 3 |
| [public/monaco-editor/esm/vs/platform/editor/common/editor.js](/public/monaco-editor/esm/vs/platform/editor/common/editor.js) | JavaScript | 5 | 8 | 1 | 14 |
| [public/monaco-editor/esm/vs/platform/environment/common/environment.js](/public/monaco-editor/esm/vs/platform/environment/common/environment.js) | JavaScript | 2 | 0 | 1 | 3 |
| [public/monaco-editor/esm/vs/platform/extensions/common/extensions.js](/public/monaco-editor/esm/vs/platform/extensions/common/extensions.js) | JavaScript | 12 | 19 | 1 | 32 |
| [public/monaco-editor/esm/vs/platform/files/common/files.js](/public/monaco-editor/esm/vs/platform/files/common/files.js) | JavaScript | 6 | 3 | 1 | 10 |
| [public/monaco-editor/esm/vs/platform/instantiation/common/descriptors.js](/public/monaco-editor/esm/vs/platform/instantiation/common/descriptors.js) | JavaScript | 7 | 4 | 1 | 12 |
| [public/monaco-editor/esm/vs/platform/instantiation/common/extensions.js](/public/monaco-editor/esm/vs/platform/instantiation/common/extensions.js) | JavaScript | 11 | 4 | 1 | 16 |
| [public/monaco-editor/esm/vs/platform/instantiation/common/graph.js](/public/monaco-editor/esm/vs/platform/instantiation/common/graph.js) | JavaScript | 79 | 9 | 1 | 89 |
| [public/monaco-editor/esm/vs/platform/instantiation/common/instantiation.js](/public/monaco-editor/esm/vs/platform/instantiation/common/instantiation.js) | JavaScript | 34 | 8 | 1 | 43 |
| [public/monaco-editor/esm/vs/platform/instantiation/common/instantiationService.js](/public/monaco-editor/esm/vs/platform/instantiation/common/instantiationService.js) | JavaScript | 263 | 21 | 1 | 285 |
| [public/monaco-editor/esm/vs/platform/instantiation/common/serviceCollection.js](/public/monaco-editor/esm/vs/platform/instantiation/common/serviceCollection.js) | JavaScript | 19 | 4 | 1 | 24 |
| [public/monaco-editor/esm/vs/platform/jsonschemas/common/jsonContributionRegistry.js](/public/monaco-editor/esm/vs/platform/jsonschemas/common/jsonContributionRegistry.js) | JavaScript | 26 | 4 | 1 | 31 |
| [public/monaco-editor/esm/vs/platform/keybinding/common/abstractKeybindingService.js](/public/monaco-editor/esm/vs/platform/keybinding/common/abstractKeybindingService.js) | JavaScript | 210 | 15 | 1 | 226 |
| [public/monaco-editor/esm/vs/platform/keybinding/common/baseResolvedKeybinding.js](/public/monaco-editor/esm/vs/platform/keybinding/common/baseResolvedKeybinding.js) | JavaScript | 43 | 7 | 1 | 51 |
| [public/monaco-editor/esm/vs/platform/keybinding/common/keybinding.js](/public/monaco-editor/esm/vs/platform/keybinding/common/keybinding.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/platform/keybinding/common/keybindingResolver.js](/public/monaco-editor/esm/vs/platform/keybinding/common/keybindingResolver.js) | JavaScript | 223 | 26 | 1 | 250 |
| [public/monaco-editor/esm/vs/platform/keybinding/common/keybindingsRegistry.js](/public/monaco-editor/esm/vs/platform/keybinding/common/keybindingsRegistry.js) | JavaScript | 121 | 8 | 1 | 130 |
| [public/monaco-editor/esm/vs/platform/keybinding/common/resolvedKeybindingItem.js](/public/monaco-editor/esm/vs/platform/keybinding/common/resolvedKeybindingItem.js) | JavaScript | 28 | 6 | 1 | 35 |
| [public/monaco-editor/esm/vs/platform/keybinding/common/usLayoutResolvedKeybinding.js](/public/monaco-editor/esm/vs/platform/keybinding/common/usLayoutResolvedKeybinding.js) | JavaScript | 155 | 10 | 1 | 166 |
| [public/monaco-editor/esm/vs/platform/label/common/label.js](/public/monaco-editor/esm/vs/platform/label/common/label.js) | JavaScript | 2 | 0 | 1 | 3 |
| [public/monaco-editor/esm/vs/platform/layout/browser/layoutService.js](/public/monaco-editor/esm/vs/platform/layout/browser/layoutService.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/platform/list/browser/listService.js](/public/monaco-editor/esm/vs/platform/list/browser/listService.js) | JavaScript | 877 | 10 | 1 | 888 |
| [public/monaco-editor/esm/vs/platform/log/common/log.js](/public/monaco-editor/esm/vs/platform/log/common/log.js) | JavaScript | 81 | 1 | 1 | 83 |
| [public/monaco-editor/esm/vs/platform/markers/common/markerService.js](/public/monaco-editor/esm/vs/platform/markers/common/markerService.js) | JavaScript | 243 | 12 | 1 | 256 |
| [public/monaco-editor/esm/vs/platform/markers/common/markers.js](/public/monaco-editor/esm/vs/platform/markers/common/markers.js) | JavaScript | 110 | 6 | 1 | 117 |
| [public/monaco-editor/esm/vs/platform/notification/common/notification.js](/public/monaco-editor/esm/vs/platform/notification/common/notification.js) | JavaScript | 6 | 0 | 1 | 7 |
| [public/monaco-editor/esm/vs/platform/opener/browser/link.js](/public/monaco-editor/esm/vs/platform/opener/browser/link.js) | JavaScript | 90 | 4 | 1 | 95 |
| [public/monaco-editor/esm/vs/platform/opener/common/opener.js](/public/monaco-editor/esm/vs/platform/opener/common/opener.js) | JavaScript | 39 | 4 | 1 | 44 |
| [public/monaco-editor/esm/vs/platform/progress/common/progress.js](/public/monaco-editor/esm/vs/platform/progress/common/progress.js) | JavaScript | 17 | 0 | 1 | 18 |
| [public/monaco-editor/esm/vs/platform/quickinput/browser/commandsQuickAccess.js](/public/monaco-editor/esm/vs/platform/quickinput/browser/commandsQuickAccess.js) | JavaScript | 219 | 18 | 1 | 238 |
| [public/monaco-editor/esm/vs/platform/quickinput/browser/helpQuickAccess.js](/public/monaco-editor/esm/vs/platform/quickinput/browser/helpQuickAccess.js) | JavaScript | 72 | 10 | 1 | 83 |
| [public/monaco-editor/esm/vs/platform/quickinput/browser/pickerQuickAccess.js](/public/monaco-editor/esm/vs/platform/quickinput/browser/pickerQuickAccess.js) | JavaScript | 208 | 42 | 1 | 251 |
| [public/monaco-editor/esm/vs/platform/quickinput/browser/quickAccess.js](/public/monaco-editor/esm/vs/platform/quickinput/browser/quickAccess.js) | JavaScript | 156 | 35 | 1 | 192 |
| [public/monaco-editor/esm/vs/platform/quickinput/browser/quickInput.js](/public/monaco-editor/esm/vs/platform/quickinput/browser/quickInput.js) | JavaScript | 160 | 7 | 1 | 168 |
| [public/monaco-editor/esm/vs/platform/quickinput/common/quickAccess.js](/public/monaco-editor/esm/vs/platform/quickinput/common/quickAccess.js) | JavaScript | 40 | 13 | 1 | 54 |
| [public/monaco-editor/esm/vs/platform/quickinput/common/quickInput.js](/public/monaco-editor/esm/vs/platform/quickinput/common/quickInput.js) | JavaScript | 3 | 4 | 1 | 8 |
| [public/monaco-editor/esm/vs/platform/registry/common/platform.js](/public/monaco-editor/esm/vs/platform/registry/common/platform.js) | JavaScript | 17 | 4 | 1 | 22 |
| [public/monaco-editor/esm/vs/platform/severityIcon/common/severityIcon.js](/public/monaco-editor/esm/vs/platform/severityIcon/common/severityIcon.js) | JavaScript | 63 | 4 | 1 | 68 |
| [public/monaco-editor/esm/vs/platform/storage/common/storage.js](/public/monaco-editor/esm/vs/platform/storage/common/storage.js) | JavaScript | 134 | 22 | 1 | 157 |
| [public/monaco-editor/esm/vs/platform/telemetry/common/gdprTypings.js](/public/monaco-editor/esm/vs/platform/telemetry/common/gdprTypings.js) | JavaScript | 1 | 0 | 1 | 2 |
| [public/monaco-editor/esm/vs/platform/telemetry/common/telemetry.js](/public/monaco-editor/esm/vs/platform/telemetry/common/telemetry.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/platform/theme/browser/iconsStyleSheet.js](/public/monaco-editor/esm/vs/platform/theme/browser/iconsStyleSheet.js) | JavaScript | 47 | 4 | 1 | 52 |
| [public/monaco-editor/esm/vs/platform/theme/common/colorRegistry.js](/public/monaco-editor/esm/vs/platform/theme/common/colorRegistry.js) | JavaScript | 353 | 87 | 1 | 441 |
| [public/monaco-editor/esm/vs/platform/theme/common/iconRegistry.js](/public/monaco-editor/esm/vs/platform/theme/common/iconRegistry.js) | JavaScript | 128 | 8 | 1 | 137 |
| [public/monaco-editor/esm/vs/platform/theme/common/styler.js](/public/monaco-editor/esm/vs/platform/theme/common/styler.js) | JavaScript | 78 | 4 | 1 | 83 |
| [public/monaco-editor/esm/vs/platform/theme/common/theme.js](/public/monaco-editor/esm/vs/platform/theme/common/theme.js) | JavaScript | 6 | 7 | 1 | 14 |
| [public/monaco-editor/esm/vs/platform/theme/common/themeService.js](/public/monaco-editor/esm/vs/platform/theme/common/themeService.js) | JavaScript | 113 | 10 | 1 | 124 |
| [public/monaco-editor/esm/vs/platform/undoRedo/common/undoRedo.js](/public/monaco-editor/esm/vs/platform/undoRedo/common/undoRedo.js) | JavaScript | 36 | 4 | 1 | 41 |
| [public/monaco-editor/esm/vs/platform/undoRedo/common/undoRedoService.js](/public/monaco-editor/esm/vs/platform/undoRedo/common/undoRedoService.js) | JavaScript | 1,048 | 49 | 1 | 1,098 |
| [public/monaco-editor/esm/vs/platform/workspace/common/workspace.js](/public/monaco-editor/esm/vs/platform/workspace/common/workspace.js) | JavaScript | 62 | 0 | 1 | 63 |
| [public/monaco-editor/esm/vs/platform/workspace/common/workspaceTrust.js](/public/monaco-editor/esm/vs/platform/workspace/common/workspaceTrust.js) | JavaScript | 2 | 4 | 1 | 7 |
| [public/monaco-editor/esm/vs/platform/workspaces/common/workspaces.js](/public/monaco-editor/esm/vs/platform/workspaces/common/workspaces.js) | JavaScript | 23 | 4 | 1 | 28 |
| [public/monaco-editor/min/vs/base/worker/workerMain.js](/public/monaco-editor/min/vs/base/worker/workerMain.js) | JavaScript | 12 | 8 | 2 | 22 |
| [public/monaco-editor/min/vs/basic-languages/abap/abap.js](/public/monaco-editor/min/vs/basic-languages/abap/abap.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/apex/apex.js](/public/monaco-editor/min/vs/basic-languages/apex/apex.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/azcli/azcli.js](/public/monaco-editor/min/vs/basic-languages/azcli/azcli.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/bat/bat.js](/public/monaco-editor/min/vs/basic-languages/bat/bat.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/bicep/bicep.js](/public/monaco-editor/min/vs/basic-languages/bicep/bicep.js) | JavaScript | 3 | 9 | 0 | 12 |
| [public/monaco-editor/min/vs/basic-languages/cameligo/cameligo.js](/public/monaco-editor/min/vs/basic-languages/cameligo/cameligo.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/clojure/clojure.js](/public/monaco-editor/min/vs/basic-languages/clojure/clojure.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/coffee/coffee.js](/public/monaco-editor/min/vs/basic-languages/coffee/coffee.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/cpp/cpp.js](/public/monaco-editor/min/vs/basic-languages/cpp/cpp.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/csharp/csharp.js](/public/monaco-editor/min/vs/basic-languages/csharp/csharp.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/csp/csp.js](/public/monaco-editor/min/vs/basic-languages/csp/csp.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/css/css.js](/public/monaco-editor/min/vs/basic-languages/css/css.js) | JavaScript | 6 | 6 | 1 | 13 |
| [public/monaco-editor/min/vs/basic-languages/dart/dart.js](/public/monaco-editor/min/vs/basic-languages/dart/dart.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/dockerfile/dockerfile.js](/public/monaco-editor/min/vs/basic-languages/dockerfile/dockerfile.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/ecl/ecl.js](/public/monaco-editor/min/vs/basic-languages/ecl/ecl.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/elixir/elixir.js](/public/monaco-editor/min/vs/basic-languages/elixir/elixir.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/flow9/flow9.js](/public/monaco-editor/min/vs/basic-languages/flow9/flow9.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/fsharp/fsharp.js](/public/monaco-editor/min/vs/basic-languages/fsharp/fsharp.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/go/go.js](/public/monaco-editor/min/vs/basic-languages/go/go.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/graphql/graphql.js](/public/monaco-editor/min/vs/basic-languages/graphql/graphql.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/handlebars/handlebars.js](/public/monaco-editor/min/vs/basic-languages/handlebars/handlebars.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/hcl/hcl.js](/public/monaco-editor/min/vs/basic-languages/hcl/hcl.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/html/html.js](/public/monaco-editor/min/vs/basic-languages/html/html.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/ini/ini.js](/public/monaco-editor/min/vs/basic-languages/ini/ini.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/java/java.js](/public/monaco-editor/min/vs/basic-languages/java/java.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/javascript/javascript.js](/public/monaco-editor/min/vs/basic-languages/javascript/javascript.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/julia/julia.js](/public/monaco-editor/min/vs/basic-languages/julia/julia.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/kotlin/kotlin.js](/public/monaco-editor/min/vs/basic-languages/kotlin/kotlin.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/less/less.js](/public/monaco-editor/min/vs/basic-languages/less/less.js) | JavaScript | 5 | 6 | 1 | 12 |
| [public/monaco-editor/min/vs/basic-languages/lexon/lexon.js](/public/monaco-editor/min/vs/basic-languages/lexon/lexon.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/liquid/liquid.js](/public/monaco-editor/min/vs/basic-languages/liquid/liquid.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/lua/lua.js](/public/monaco-editor/min/vs/basic-languages/lua/lua.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/m3/m3.js](/public/monaco-editor/min/vs/basic-languages/m3/m3.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/markdown/markdown.js](/public/monaco-editor/min/vs/basic-languages/markdown/markdown.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/mips/mips.js](/public/monaco-editor/min/vs/basic-languages/mips/mips.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/msdax/msdax.js](/public/monaco-editor/min/vs/basic-languages/msdax/msdax.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/mysql/mysql.js](/public/monaco-editor/min/vs/basic-languages/mysql/mysql.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/objective-c/objective-c.js](/public/monaco-editor/min/vs/basic-languages/objective-c/objective-c.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/pascal/pascal.js](/public/monaco-editor/min/vs/basic-languages/pascal/pascal.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/pascaligo/pascaligo.js](/public/monaco-editor/min/vs/basic-languages/pascaligo/pascaligo.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/perl/perl.js](/public/monaco-editor/min/vs/basic-languages/perl/perl.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/pgsql/pgsql.js](/public/monaco-editor/min/vs/basic-languages/pgsql/pgsql.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/php/php.js](/public/monaco-editor/min/vs/basic-languages/php/php.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/pla/pla.js](/public/monaco-editor/min/vs/basic-languages/pla/pla.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/postiats/postiats.js](/public/monaco-editor/min/vs/basic-languages/postiats/postiats.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/powerquery/powerquery.js](/public/monaco-editor/min/vs/basic-languages/powerquery/powerquery.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/powershell/powershell.js](/public/monaco-editor/min/vs/basic-languages/powershell/powershell.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/protobuf/protobuf.js](/public/monaco-editor/min/vs/basic-languages/protobuf/protobuf.js) | JavaScript | 5 | 6 | 1 | 12 |
| [public/monaco-editor/min/vs/basic-languages/pug/pug.js](/public/monaco-editor/min/vs/basic-languages/pug/pug.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/python/python.js](/public/monaco-editor/min/vs/basic-languages/python/python.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/qsharp/qsharp.js](/public/monaco-editor/min/vs/basic-languages/qsharp/qsharp.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/r/r.js](/public/monaco-editor/min/vs/basic-languages/r/r.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/razor/razor.js](/public/monaco-editor/min/vs/basic-languages/razor/razor.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/redis/redis.js](/public/monaco-editor/min/vs/basic-languages/redis/redis.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/redshift/redshift.js](/public/monaco-editor/min/vs/basic-languages/redshift/redshift.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/restructuredtext/restructuredtext.js](/public/monaco-editor/min/vs/basic-languages/restructuredtext/restructuredtext.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/ruby/ruby.js](/public/monaco-editor/min/vs/basic-languages/ruby/ruby.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/rust/rust.js](/public/monaco-editor/min/vs/basic-languages/rust/rust.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/sb/sb.js](/public/monaco-editor/min/vs/basic-languages/sb/sb.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/scala/scala.js](/public/monaco-editor/min/vs/basic-languages/scala/scala.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/scheme/scheme.js](/public/monaco-editor/min/vs/basic-languages/scheme/scheme.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/scss/scss.js](/public/monaco-editor/min/vs/basic-languages/scss/scss.js) | JavaScript | 6 | 6 | 1 | 13 |
| [public/monaco-editor/min/vs/basic-languages/shell/shell.js](/public/monaco-editor/min/vs/basic-languages/shell/shell.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/solidity/solidity.js](/public/monaco-editor/min/vs/basic-languages/solidity/solidity.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/sophia/sophia.js](/public/monaco-editor/min/vs/basic-languages/sophia/sophia.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/sparql/sparql.js](/public/monaco-editor/min/vs/basic-languages/sparql/sparql.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/sql/sql.js](/public/monaco-editor/min/vs/basic-languages/sql/sql.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/st/st.js](/public/monaco-editor/min/vs/basic-languages/st/st.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/swift/swift.js](/public/monaco-editor/min/vs/basic-languages/swift/swift.js) | JavaScript | 4 | 9 | 1 | 14 |
| [public/monaco-editor/min/vs/basic-languages/systemverilog/systemverilog.js](/public/monaco-editor/min/vs/basic-languages/systemverilog/systemverilog.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/tcl/tcl.js](/public/monaco-editor/min/vs/basic-languages/tcl/tcl.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/twig/twig.js](/public/monaco-editor/min/vs/basic-languages/twig/twig.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/typescript/typescript.js](/public/monaco-editor/min/vs/basic-languages/typescript/typescript.js) | JavaScript | 2 | 9 | 0 | 11 |
| [public/monaco-editor/min/vs/basic-languages/vb/vb.js](/public/monaco-editor/min/vs/basic-languages/vb/vb.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/xml/xml.js](/public/monaco-editor/min/vs/basic-languages/xml/xml.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/basic-languages/yaml/yaml.js](/public/monaco-editor/min/vs/basic-languages/yaml/yaml.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/min/vs/editor/editor.main.css](/public/monaco-editor/min/vs/editor/editor.main.css) | CSS | 1 | 5 | 0 | 6 |
| [public/monaco-editor/min/vs/editor/editor.main.js](/public/monaco-editor/min/vs/editor/editor.main.js) | JavaScript | 663 | 57 | 87 | 807 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.de.js](/public/monaco-editor/min/vs/editor/editor.main.nls.de.js) | JavaScript | 22 | 6 | 1 | 29 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.es.js](/public/monaco-editor/min/vs/editor/editor.main.nls.es.js) | JavaScript | 22 | 6 | 1 | 29 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.fr.js](/public/monaco-editor/min/vs/editor/editor.main.nls.fr.js) | JavaScript | 20 | 6 | 1 | 27 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.it.js](/public/monaco-editor/min/vs/editor/editor.main.nls.it.js) | JavaScript | 20 | 6 | 1 | 27 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.ja.js](/public/monaco-editor/min/vs/editor/editor.main.nls.ja.js) | JavaScript | 22 | 6 | 1 | 29 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.js](/public/monaco-editor/min/vs/editor/editor.main.nls.js) | JavaScript | 20 | 6 | 1 | 27 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.ko.js](/public/monaco-editor/min/vs/editor/editor.main.nls.ko.js) | JavaScript | 20 | 6 | 1 | 27 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.ru.js](/public/monaco-editor/min/vs/editor/editor.main.nls.ru.js) | JavaScript | 22 | 6 | 1 | 29 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.zh-cn.js](/public/monaco-editor/min/vs/editor/editor.main.nls.zh-cn.js) | JavaScript | 22 | 6 | 1 | 29 |
| [public/monaco-editor/min/vs/editor/editor.main.nls.zh-tw.js](/public/monaco-editor/min/vs/editor/editor.main.nls.zh-tw.js) | JavaScript | 20 | 6 | 1 | 27 |
| [public/monaco-editor/min/vs/language/css/cssMode.js](/public/monaco-editor/min/vs/language/css/cssMode.js) | JavaScript | 7 | 6 | 1 | 14 |
| [public/monaco-editor/min/vs/language/css/cssWorker.js](/public/monaco-editor/min/vs/language/css/cssWorker.js) | JavaScript | 38 | 6 | 20 | 64 |
| [public/monaco-editor/min/vs/language/html/htmlMode.js](/public/monaco-editor/min/vs/language/html/htmlMode.js) | JavaScript | 7 | 6 | 1 | 14 |
| [public/monaco-editor/min/vs/language/html/htmlWorker.js](/public/monaco-editor/min/vs/language/html/htmlWorker.js) | JavaScript | 257 | 6 | 191 | 454 |
| [public/monaco-editor/min/vs/language/json/jsonMode.js](/public/monaco-editor/min/vs/language/json/jsonMode.js) | JavaScript | 9 | 6 | 1 | 16 |
| [public/monaco-editor/min/vs/language/json/jsonWorker.js](/public/monaco-editor/min/vs/language/json/jsonWorker.js) | JavaScript | 27 | 6 | 4 | 37 |
| [public/monaco-editor/min/vs/language/typescript/tsMode.js](/public/monaco-editor/min/vs/language/typescript/tsMode.js) | JavaScript | 11 | 6 | 4 | 21 |
| [public/monaco-editor/min/vs/language/typescript/tsWorker.js](/public/monaco-editor/min/vs/language/typescript/tsWorker.js) | JavaScript | 20,714 | 11,099 | 3,510 | 35,323 |
| [public/monaco-editor/min/vs/loader.js](/public/monaco-editor/min/vs/loader.js) | JavaScript | 4 | 6 | 1 | 11 |
| [public/monaco-editor/monaco.d.ts](/public/monaco-editor/monaco.d.ts) | TypeScript | 2,976 | 4,449 | 363 | 7,788 |
| [public/monaco-editor/package.json](/public/monaco-editor/package.json) | JSON | 59 | 0 | 0 | 59 |
| [scripts/build.js](/scripts/build.js) | JavaScript | 169 | 20 | 24 | 213 |
| [scripts/start.js](/scripts/start.js) | JavaScript | 133 | 17 | 17 | 167 |
| [scripts/test.js](/scripts/test.js) | JavaScript | 36 | 7 | 11 | 54 |
| [src/App.less](/src/App.less) | Less | 12 | 0 | 2 | 14 |
| [src/App.tsx](/src/App.tsx) | TypeScript React | 7 | 0 | 3 | 10 |
| [src/components/AlertTips/index.tsx](/src/components/AlertTips/index.tsx) | TypeScript React | 35 | 0 | 5 | 40 |
| [src/components/AlertTips/styles.less](/src/components/AlertTips/styles.less) | Less | 11 | 0 | 2 | 13 |
| [src/components/AuthProvider/index.tsx](/src/components/AuthProvider/index.tsx) | TypeScript React | 42 | 0 | 10 | 52 |
| [src/components/AuthResource/index.tsx](/src/components/AuthResource/index.tsx) | TypeScript React | 23 | 0 | 3 | 26 |
| [src/components/ChooseGateWay/index.tsx](/src/components/ChooseGateWay/index.tsx) | TypeScript React | 13 | 0 | 3 | 16 |
| [src/components/CustomProTable/index.tsx](/src/components/CustomProTable/index.tsx) | TypeScript React | 32 | 30 | 3 | 65 |
| [src/components/CustomTable/index.tsx](/src/components/CustomTable/index.tsx) | TypeScript React | 6 | 0 | 3 | 9 |
| [src/components/EchartsRender/index.tsx](/src/components/EchartsRender/index.tsx) | TypeScript React | 54 | 3 | 9 | 66 |
| [src/components/Editor/index.tsx](/src/components/Editor/index.tsx) | TypeScript React | 14 | 1 | 4 | 19 |
| [src/components/HistoryTabs/index.tsx](/src/components/HistoryTabs/index.tsx) | TypeScript React | 125 | 0 | 13 | 138 |
| [src/components/HistoryTabs/styles.module.less](/src/components/HistoryTabs/styles.module.less) | Less | 20 | 1 | 1 | 22 |
| [src/components/Layout/BreadcrumbNav/index.tsx](/src/components/Layout/BreadcrumbNav/index.tsx) | TypeScript React | 58 | 0 | 7 | 65 |
| [src/components/Layout/BreadcrumbNav/styles.module.less](/src/components/Layout/BreadcrumbNav/styles.module.less) | Less | 11 | 0 | 2 | 13 |
| [src/components/Layout/UpdataPasswordNode/index.tsx](/src/components/Layout/UpdataPasswordNode/index.tsx) | TypeScript React | 116 | 0 | 13 | 129 |
| [src/components/Layout/index.less](/src/components/Layout/index.less) | Less | 89 | 10 | 18 | 117 |
| [src/components/Layout/index.tsx](/src/components/Layout/index.tsx) | TypeScript React | 226 | 11 | 14 | 251 |
| [src/components/Layout/styles.module.less](/src/components/Layout/styles.module.less) | Less | 11 | 3 | 3 | 17 |
| [src/components/LeftMenu/index.tsx](/src/components/LeftMenu/index.tsx) | TypeScript React | 93 | 0 | 7 | 100 |
| [src/components/ModalCustomTable/index.tsx](/src/components/ModalCustomTable/index.tsx) | TypeScript React | 133 | 13 | 13 | 159 |
| [src/components/ModifyPassword/index.tsx](/src/components/ModifyPassword/index.tsx) | TypeScript React | 85 | 0 | 9 | 94 |
| [src/components/NoticeList/index.tsx](/src/components/NoticeList/index.tsx) | TypeScript React | 127 | 0 | 9 | 136 |
| [src/components/PageTitle/index.tsx](/src/components/PageTitle/index.tsx) | TypeScript React | 11 | 0 | 3 | 14 |
| [src/components/PageView/index.tsx](/src/components/PageView/index.tsx) | TypeScript React | 16 | 0 | 3 | 19 |
| [src/components/PageView/styles.module.less](/src/components/PageView/styles.module.less) | Less | 7 | 0 | 1 | 8 |
| [src/components/RenderTags/index.less](/src/components/RenderTags/index.less) | Less | 12 | 0 | 1 | 13 |
| [src/components/RenderTags/index.tsx](/src/components/RenderTags/index.tsx) | TypeScript React | 122 | 0 | 14 | 136 |
| [src/components/RequireAuth/index.tsx](/src/components/RequireAuth/index.tsx) | TypeScript React | 18 | 0 | 3 | 21 |
| [src/components/RequireData/index.tsx](/src/components/RequireData/index.tsx) | TypeScript React | 3 | 0 | 1 | 4 |
| [src/components/SearchCard/index.tsx](/src/components/SearchCard/index.tsx) | TypeScript React | 17 | 0 | 3 | 20 |
| [src/components/SelectApiTable/index.less](/src/components/SelectApiTable/index.less) | Less | 6 | 0 | 1 | 7 |
| [src/components/SelectApiTable/index.tsx](/src/components/SelectApiTable/index.tsx) | TypeScript React | 57 | 0 | 7 | 64 |
| [src/components/TigBtn/index.tsx](/src/components/TigBtn/index.tsx) | TypeScript React | 21 | 0 | 4 | 25 |
| [src/components/TigBtn/styles.module.less](/src/components/TigBtn/styles.module.less) | Less | 15 | 0 | 3 | 18 |
| [src/components/Title/index.tsx](/src/components/Title/index.tsx) | TypeScript React | 14 | 0 | 3 | 17 |
| [src/components/Title/styles.module.less](/src/components/Title/styles.module.less) | Less | 19 | 0 | 2 | 21 |
| [src/components/index.tsx](/src/components/index.tsx) | TypeScript React | 43 | 0 | 2 | 45 |
| [src/hooks/index.tsx](/src/hooks/index.tsx) | TypeScript React | 11 | 0 | 2 | 13 |
| [src/hooks/useAsyncEffect.tsx](/src/hooks/useAsyncEffect.tsx) | TypeScript React | 28 | 1 | 3 | 32 |
| [src/hooks/useCancleRequest.tsx](/src/hooks/useCancleRequest.tsx) | TypeScript React | 14 | 0 | 2 | 16 |
| [src/hooks/useHistory.tsx](/src/hooks/useHistory.tsx) | TypeScript React | 12 | 1 | 2 | 15 |
| [src/hooks/useHistoryCache.tsx](/src/hooks/useHistoryCache.tsx) | TypeScript React | 22 | 22 | 5 | 49 |
| [src/hooks/useLocationChange.tsx](/src/hooks/useLocationChange.tsx) | TypeScript React | 20 | 0 | 5 | 25 |
| [src/hooks/useModal.tsx](/src/hooks/useModal.tsx) | TypeScript React | 10 | 0 | 3 | 13 |
| [src/hooks/useMount.tsx](/src/hooks/useMount.tsx) | TypeScript React | 8 | 0 | 2 | 10 |
| [src/hooks/useNotice.tsx](/src/hooks/useNotice.tsx) | TypeScript React | 80 | 0 | 6 | 86 |
| [src/hooks/useNoticeCount.tsx](/src/hooks/useNoticeCount.tsx) | TypeScript React | 16 | 0 | 2 | 18 |
| [src/hooks/useQuery.ts](/src/hooks/useQuery.ts) | TypeScript | 19 | 0 | 5 | 24 |
| [src/hooks/useTable.tsx](/src/hooks/useTable.tsx) | TypeScript React | 81 | 4 | 11 | 96 |
| [src/hooks/useUnmount.tsx](/src/hooks/useUnmount.tsx) | TypeScript React | 10 | 0 | 2 | 12 |
| [src/index.css](/src/index.css) | CSS | 86 | 36 | 20 | 142 |
| [src/index.tsx](/src/index.tsx) | TypeScript React | 25 | 0 | 3 | 28 |
| [src/logo.svg](/src/logo.svg) | XML | 1 | 0 | 1 | 2 |
| [src/page/404/index.tsx](/src/page/404/index.tsx) | TypeScript React | 12 | 0 | 2 | 14 |
| [src/page/AuditManagement/ApiAudit/columns.ts](/src/page/AuditManagement/ApiAudit/columns.ts) | TypeScript | 0 | 0 | 1 | 1 |
| [src/page/AuditManagement/ApiAudit/index.tsx](/src/page/AuditManagement/ApiAudit/index.tsx) | TypeScript React | 117 | 10 | 12 | 139 |
| [src/page/AuditManagement/Subscribe/SubscriptionColumns.tsx](/src/page/AuditManagement/Subscribe/SubscriptionColumns.tsx) | TypeScript React | 55 | 0 | 2 | 57 |
| [src/page/AuditManagement/Subscribe/index.tsx](/src/page/AuditManagement/Subscribe/index.tsx) | TypeScript React | 117 | 10 | 12 | 139 |
| [src/page/AuditManagement/index.tsx](/src/page/AuditManagement/index.tsx) | TypeScript React | 5 | 0 | 2 | 7 |
| [src/page/AuditManagement/route.tsx](/src/page/AuditManagement/route.tsx) | TypeScript React | 30 | 0 | 4 | 34 |
| [src/page/BigScreen/components/DelayProbability/index.tsx](/src/page/BigScreen/components/DelayProbability/index.tsx) | TypeScript React | 62 | 0 | 5 | 67 |
| [src/page/BigScreen/components/Empty/index.tsx](/src/page/BigScreen/components/Empty/index.tsx) | TypeScript React | 12 | 0 | 3 | 15 |
| [src/page/BigScreen/components/Empty/styles.module.less](/src/page/BigScreen/components/Empty/styles.module.less) | Less | 12 | 0 | 2 | 14 |
| [src/page/BigScreen/components/ErrorCount/index.tsx](/src/page/BigScreen/components/ErrorCount/index.tsx) | TypeScript React | 55 | 0 | 6 | 61 |
| [src/page/BigScreen/components/InletFlow/index.tsx](/src/page/BigScreen/components/InletFlow/index.tsx) | TypeScript React | 63 | 0 | 6 | 69 |
| [src/page/BigScreen/components/OutFlow/index.tsx](/src/page/BigScreen/components/OutFlow/index.tsx) | TypeScript React | 63 | 0 | 7 | 70 |
| [src/page/BigScreen/components/PageConainer/index.tsx](/src/page/BigScreen/components/PageConainer/index.tsx) | TypeScript React | 23 | 0 | 5 | 28 |
| [src/page/BigScreen/components/PageConainer/styles.module.less](/src/page/BigScreen/components/PageConainer/styles.module.less) | Less | 16 | 20 | 4 | 40 |
| [src/page/BigScreen/components/RequestCount/index.tsx](/src/page/BigScreen/components/RequestCount/index.tsx) | TypeScript React | 61 | 1 | 5 | 67 |
| [src/page/BigScreen/components/RequestCount/styles.module.less](/src/page/BigScreen/components/RequestCount/styles.module.less) | Less | 2 | 1 | 1 | 4 |
| [src/page/BigScreen/components/Respond/index.tsx](/src/page/BigScreen/components/Respond/index.tsx) | TypeScript React | 143 | 3 | 11 | 157 |
| [src/page/BigScreen/components/Respond/styles.module.less](/src/page/BigScreen/components/Respond/styles.module.less) | Less | 35 | 0 | 5 | 40 |
| [src/page/BigScreen/components/ServiceUseRatio/index.tsx](/src/page/BigScreen/components/ServiceUseRatio/index.tsx) | TypeScript React | 112 | 1 | 6 | 119 |
| [src/page/BigScreen/components/ServiceUseRatio/styles.module.less](/src/page/BigScreen/components/ServiceUseRatio/styles.module.less) | Less | 22 | 0 | 2 | 24 |
| [src/page/BigScreen/components/ServiceUseRatioA/index.tsx](/src/page/BigScreen/components/ServiceUseRatioA/index.tsx) | TypeScript React | 182 | 1 | 6 | 189 |
| [src/page/BigScreen/components/ServiceUseRatioA/styles.module.less](/src/page/BigScreen/components/ServiceUseRatioA/styles.module.less) | Less | 68 | 0 | 11 | 79 |
| [src/page/BigScreen/constant.ts](/src/page/BigScreen/constant.ts) | TypeScript | 17 | 12 | 4 | 33 |
| [src/page/BigScreen/index.tsx](/src/page/BigScreen/index.tsx) | TypeScript React | 116 | 16 | 10 | 142 |
| [src/page/BigScreen/option.ts](/src/page/BigScreen/option.ts) | TypeScript | 324 | 6 | 13 | 343 |
| [src/page/BigScreen/styles.module.less](/src/page/BigScreen/styles.module.less) | Less | 91 | 1 | 9 | 101 |
| [src/page/BigScreen/useDataZoom.ts](/src/page/BigScreen/useDataZoom.ts) | TypeScript | 52 | 0 | 7 | 59 |
| [src/page/GatewayList/PackageManagement/columns.tsx](/src/page/GatewayList/PackageManagement/columns.tsx) | TypeScript React | 127 | 8 | 2 | 137 |
| [src/page/GatewayList/PackageManagement/index.tsx](/src/page/GatewayList/PackageManagement/index.tsx) | TypeScript React | 275 | 38 | 19 | 332 |
| [src/page/GatewayList/PackageManagement/styles.module.less](/src/page/GatewayList/PackageManagement/styles.module.less) | Less | 17 | 0 | 4 | 21 |
| [src/page/GatewayList/components/AddInterface/index.less](/src/page/GatewayList/components/AddInterface/index.less) | Less | 6 | 0 | 1 | 7 |
| [src/page/GatewayList/components/AddInterface/index.tsx](/src/page/GatewayList/components/AddInterface/index.tsx) | TypeScript React | 139 | 4 | 12 | 155 |
| [src/page/GatewayList/components/EditGateWay/index.tsx](/src/page/GatewayList/components/EditGateWay/index.tsx) | TypeScript React | 30 | 4 | 3 | 37 |
| [src/page/GatewayList/components/NewGateWay/index.tsx](/src/page/GatewayList/components/NewGateWay/index.tsx) | TypeScript React | 73 | 0 | 6 | 79 |
| [src/page/GatewayList/components/NewPackage/index.tsx](/src/page/GatewayList/components/NewPackage/index.tsx) | TypeScript React | 267 | 4 | 19 | 290 |
| [src/page/GatewayList/components/TelescopicCapacity/index.less](/src/page/GatewayList/components/TelescopicCapacity/index.less) | Less | 9 | 0 | 1 | 10 |
| [src/page/GatewayList/components/TelescopicCapacity/index.tsx](/src/page/GatewayList/components/TelescopicCapacity/index.tsx) | TypeScript React | 211 | 4 | 9 | 224 |
| [src/page/GatewayList/components/index.tsx](/src/page/GatewayList/components/index.tsx) | TypeScript React | 12 | 0 | 2 | 14 |
| [src/page/GatewayList/index.tsx](/src/page/GatewayList/index.tsx) | TypeScript React | 132 | 14 | 9 | 155 |
| [src/page/GatewayList/route.tsx](/src/page/GatewayList/route.tsx) | TypeScript React | 20 | 0 | 4 | 24 |
| [src/page/InterfaceIntegration/BackendService/index.tsx](/src/page/InterfaceIntegration/BackendService/index.tsx) | TypeScript React | 250 | 22 | 12 | 284 |
| [src/page/InterfaceIntegration/BackgroundServiceApi/index.tsx](/src/page/InterfaceIntegration/BackgroundServiceApi/index.tsx) | TypeScript React | 309 | 20 | 18 | 347 |
| [src/page/InterfaceIntegration/DebugApi/index.tsx](/src/page/InterfaceIntegration/DebugApi/index.tsx) | TypeScript React | 453 | 27 | 30 | 510 |
| [src/page/InterfaceIntegration/index.tsx](/src/page/InterfaceIntegration/index.tsx) | TypeScript React | 5 | 0 | 2 | 7 |
| [src/page/InterfaceIntegration/route.tsx](/src/page/InterfaceIntegration/route.tsx) | TypeScript React | 40 | 0 | 4 | 44 |
| [src/page/Login/index.tsx](/src/page/Login/index.tsx) | TypeScript React | 95 | 0 | 8 | 103 |
| [src/page/Login/styles.module.less](/src/page/Login/styles.module.less) | Less | 61 | 1 | 9 | 71 |
| [src/page/ProcessOrchestration/DataSource/index.tsx](/src/page/ProcessOrchestration/DataSource/index.tsx) | TypeScript React | 101 | 8 | 7 | 116 |
| [src/page/ProcessOrchestration/Edit/index.less](/src/page/ProcessOrchestration/Edit/index.less) | Less | 24 | 0 | 3 | 27 |
| [src/page/ProcessOrchestration/Edit/index.tsx](/src/page/ProcessOrchestration/Edit/index.tsx) | TypeScript React | 149 | 7 | 14 | 170 |
| [src/page/ProcessOrchestration/List/index.tsx](/src/page/ProcessOrchestration/List/index.tsx) | TypeScript React | 114 | 10 | 9 | 133 |
| [src/page/ProcessOrchestration/MessageConversion/index.tsx](/src/page/ProcessOrchestration/MessageConversion/index.tsx) | TypeScript React | 200 | 24 | 12 | 236 |
| [src/page/ProcessOrchestration/MsgBodyManag/index.tsx](/src/page/ProcessOrchestration/MsgBodyManag/index.tsx) | TypeScript React | 192 | 19 | 13 | 224 |
| [src/page/ProcessOrchestration/MsgCovertDetail/index.tsx](/src/page/ProcessOrchestration/MsgCovertDetail/index.tsx) | TypeScript React | 81 | 2 | 5 | 88 |
| [src/page/ProcessOrchestration/MsgDetail/index.tsx](/src/page/ProcessOrchestration/MsgDetail/index.tsx) | TypeScript React | 117 | 8 | 7 | 132 |
| [src/page/ProcessOrchestration/PathManagement/index.tsx](/src/page/ProcessOrchestration/PathManagement/index.tsx) | TypeScript React | 93 | 15 | 8 | 116 |
| [src/page/ProcessOrchestration/Project/index.tsx](/src/page/ProcessOrchestration/Project/index.tsx) | TypeScript React | 117 | 11 | 8 | 136 |
| [src/page/ProcessOrchestration/Service/index.tsx](/src/page/ProcessOrchestration/Service/index.tsx) | TypeScript React | 140 | 30 | 13 | 183 |
| [src/page/ProcessOrchestration/Shape/branch.tsx](/src/page/ProcessOrchestration/Shape/branch.tsx) | TypeScript React | 44 | 0 | 3 | 47 |
| [src/page/ProcessOrchestration/Shape/commonGroupShape.tsx](/src/page/ProcessOrchestration/Shape/commonGroupShape.tsx) | TypeScript React | 93 | 0 | 5 | 98 |
| [src/page/ProcessOrchestration/Shape/commonShape.tsx](/src/page/ProcessOrchestration/Shape/commonShape.tsx) | TypeScript React | 62 | 0 | 3 | 65 |
| [src/page/ProcessOrchestration/Shape/connector.tsx](/src/page/ProcessOrchestration/Shape/connector.tsx) | TypeScript React | 42 | 29 | 11 | 82 |
| [src/page/ProcessOrchestration/Shape/end.tsx](/src/page/ProcessOrchestration/Shape/end.tsx) | TypeScript React | 41 | 0 | 3 | 44 |
| [src/page/ProcessOrchestration/Shape/if.tsx](/src/page/ProcessOrchestration/Shape/if.tsx) | TypeScript React | 44 | 0 | 3 | 47 |
| [src/page/ProcessOrchestration/Shape/index.tsx](/src/page/ProcessOrchestration/Shape/index.tsx) | TypeScript React | 12 | 0 | 1 | 13 |
| [src/page/ProcessOrchestration/Shape/join.tsx](/src/page/ProcessOrchestration/Shape/join.tsx) | TypeScript React | 29 | 2 | 3 | 34 |
| [src/page/ProcessOrchestration/Shape/label.tsx](/src/page/ProcessOrchestration/Shape/label.tsx) | TypeScript React | 44 | 0 | 3 | 47 |
| [src/page/ProcessOrchestration/Shape/start.tsx](/src/page/ProcessOrchestration/Shape/start.tsx) | TypeScript React | 40 | 0 | 3 | 43 |
| [src/page/ProcessOrchestration/Shape/subTitle.tsx](/src/page/ProcessOrchestration/Shape/subTitle.tsx) | TypeScript React | 47 | 0 | 3 | 50 |
| [src/page/ProcessOrchestration/Shape/switch.tsx](/src/page/ProcessOrchestration/Shape/switch.tsx) | TypeScript React | 44 | 0 | 3 | 47 |
| [src/page/ProcessOrchestration/Shape/title.tsx](/src/page/ProcessOrchestration/Shape/title.tsx) | TypeScript React | 71 | 0 | 3 | 74 |
| [src/page/ProcessOrchestration/components/AddConfigService/index.tsx](/src/page/ProcessOrchestration/components/AddConfigService/index.tsx) | TypeScript React | 210 | 3 | 14 | 227 |
| [src/page/ProcessOrchestration/components/AddDataSource/index.tsx](/src/page/ProcessOrchestration/components/AddDataSource/index.tsx) | TypeScript React | 134 | 0 | 11 | 145 |
| [src/page/ProcessOrchestration/components/AddService/index.tsx](/src/page/ProcessOrchestration/components/AddService/index.tsx) | TypeScript React | 76 | 0 | 7 | 83 |
| [src/page/ProcessOrchestration/components/AssignLinkDiagram/index.less](/src/page/ProcessOrchestration/components/AssignLinkDiagram/index.less) | Less | 113 | 0 | 19 | 132 |
| [src/page/ProcessOrchestration/components/AssignLinkDiagram/index.tsx](/src/page/ProcessOrchestration/components/AssignLinkDiagram/index.tsx) | TypeScript React | 34 | 21 | 6 | 61 |
| [src/page/ProcessOrchestration/components/AssignLinkDiagram/useAssignLinkSvg.tsx](/src/page/ProcessOrchestration/components/AssignLinkDiagram/useAssignLinkSvg.tsx) | TypeScript React | 741 | 40 | 38 | 819 |
| [src/page/ProcessOrchestration/components/AssignLinkDiagram/useAssignLinkTable.tsx](/src/page/ProcessOrchestration/components/AssignLinkDiagram/useAssignLinkTable.tsx) | TypeScript React | 72 | 12 | 6 | 90 |
| [src/page/ProcessOrchestration/components/AssignLinkDiagram/useSelectedForm.tsx](/src/page/ProcessOrchestration/components/AssignLinkDiagram/useSelectedForm.tsx) | TypeScript React | 273 | 46 | 15 | 334 |
| [src/page/ProcessOrchestration/components/AssignLinkDiagram/utils.tsx](/src/page/ProcessOrchestration/components/AssignLinkDiagram/utils.tsx) | TypeScript React | 40 | 0 | 3 | 43 |
| [src/page/ProcessOrchestration/components/Config/Activity.tsx](/src/page/ProcessOrchestration/components/Config/Activity.tsx) | TypeScript React | 134 | 0 | 5 | 139 |
| [src/page/ProcessOrchestration/components/Config/Context.tsx](/src/page/ProcessOrchestration/components/Config/Context.tsx) | TypeScript React | 272 | 8 | 14 | 294 |
| [src/page/ProcessOrchestration/components/Config/General.tsx](/src/page/ProcessOrchestration/components/Config/General.tsx) | TypeScript React | 63 | 3 | 6 | 72 |
| [src/page/ProcessOrchestration/components/Config/Modal.tsx](/src/page/ProcessOrchestration/components/Config/Modal.tsx) | TypeScript React | 41 | 3 | 4 | 48 |
| [src/page/ProcessOrchestration/components/Config/Preferences.tsx](/src/page/ProcessOrchestration/components/Config/Preferences.tsx) | TypeScript React | 52 | 8 | 6 | 66 |
| [src/page/ProcessOrchestration/components/Config/Type/Assign.tsx](/src/page/ProcessOrchestration/components/Config/Type/Assign.tsx) | TypeScript React | 78 | 0 | 6 | 84 |
| [src/page/ProcessOrchestration/components/Config/Type/Branch.tsx](/src/page/ProcessOrchestration/components/Config/Type/Branch.tsx) | TypeScript React | 57 | 16 | 6 | 79 |
| [src/page/ProcessOrchestration/components/Config/Type/Break.tsx](/src/page/ProcessOrchestration/components/Config/Type/Break.tsx) | TypeScript React | 40 | 0 | 4 | 44 |
| [src/page/ProcessOrchestration/components/Config/Type/CacheDBCall.tsx](/src/page/ProcessOrchestration/components/Config/Type/CacheDBCall.tsx) | TypeScript React | 23 | 0 | 4 | 27 |
| [src/page/ProcessOrchestration/components/Config/Type/Catch.tsx](/src/page/ProcessOrchestration/components/Config/Type/Catch.tsx) | TypeScript React | 66 | 0 | 5 | 71 |
| [src/page/ProcessOrchestration/components/Config/Type/CatchAll.tsx](/src/page/ProcessOrchestration/components/Config/Type/CatchAll.tsx) | TypeScript React | 41 | 0 | 4 | 45 |
| [src/page/ProcessOrchestration/components/Config/Type/Code.tsx](/src/page/ProcessOrchestration/components/Config/Type/Code.tsx) | TypeScript React | 51 | 0 | 6 | 57 |
| [src/page/ProcessOrchestration/components/Config/Type/Compensate.tsx](/src/page/ProcessOrchestration/components/Config/Type/Compensate.tsx) | TypeScript React | 65 | 0 | 5 | 70 |
| [src/page/ProcessOrchestration/components/Config/Type/Compensationhandler.tsx](/src/page/ProcessOrchestration/components/Config/Type/Compensationhandler.tsx) | TypeScript React | 41 | 0 | 4 | 45 |
| [src/page/ProcessOrchestration/components/Config/Type/Continue.tsx](/src/page/ProcessOrchestration/components/Config/Type/Continue.tsx) | TypeScript React | 39 | 0 | 4 | 43 |
| [src/page/ProcessOrchestration/components/Config/Type/Delay.tsx](/src/page/ProcessOrchestration/components/Config/Type/Delay.tsx) | TypeScript React | 64 | 1 | 9 | 74 |
| [src/page/ProcessOrchestration/components/Config/Type/Edge.tsx](/src/page/ProcessOrchestration/components/Config/Type/Edge.tsx) | TypeScript React | 73 | 1 | 5 | 79 |
| [src/page/ProcessOrchestration/components/Config/Type/Empty.tsx](/src/page/ProcessOrchestration/components/Config/Type/Empty.tsx) | TypeScript React | 40 | 0 | 4 | 44 |
| [src/page/ProcessOrchestration/components/Config/Type/End.tsx](/src/page/ProcessOrchestration/components/Config/Type/End.tsx) | TypeScript React | 39 | 0 | 4 | 43 |
| [src/page/ProcessOrchestration/components/Config/Type/ForEach.tsx](/src/page/ProcessOrchestration/components/Config/Type/ForEach.tsx) | TypeScript React | 69 | 0 | 6 | 75 |
| [src/page/ProcessOrchestration/components/Config/Type/GenCall.tsx](/src/page/ProcessOrchestration/components/Config/Type/GenCall.tsx) | TypeScript React | 448 | 15 | 32 | 495 |
| [src/page/ProcessOrchestration/components/Config/Type/GenCallTypeEnum.tsx](/src/page/ProcessOrchestration/components/Config/Type/GenCallTypeEnum.tsx) | TypeScript React | 16 | 3 | 6 | 25 |
| [src/page/ProcessOrchestration/components/Config/Type/HttpCall.tsx](/src/page/ProcessOrchestration/components/Config/Type/HttpCall.tsx) | TypeScript React | 419 | 11 | 28 | 458 |
| [src/page/ProcessOrchestration/components/Config/Type/If.tsx](/src/page/ProcessOrchestration/components/Config/Type/If.tsx) | TypeScript React | 57 | 0 | 6 | 63 |
| [src/page/ProcessOrchestration/components/Config/Type/JdbcCall.tsx](/src/page/ProcessOrchestration/components/Config/Type/JdbcCall.tsx) | TypeScript React | 236 | 9 | 20 | 265 |
| [src/page/ProcessOrchestration/components/Config/Type/Join.tsx](/src/page/ProcessOrchestration/components/Config/Type/Join.tsx) | TypeScript React | 40 | 0 | 4 | 44 |
| [src/page/ProcessOrchestration/components/Config/Type/Label.tsx](/src/page/ProcessOrchestration/components/Config/Type/Label.tsx) | TypeScript React | 39 | 0 | 4 | 43 |
| [src/page/ProcessOrchestration/components/Config/Type/Milestone.tsx](/src/page/ProcessOrchestration/components/Config/Type/Milestone.tsx) | TypeScript React | 64 | 0 | 5 | 69 |
| [src/page/ProcessOrchestration/components/Config/Type/Reply.tsx](/src/page/ProcessOrchestration/components/Config/Type/Reply.tsx) | TypeScript React | 40 | 0 | 4 | 44 |
| [src/page/ProcessOrchestration/components/Config/Type/Rule.tsx](/src/page/ProcessOrchestration/components/Config/Type/Rule.tsx) | TypeScript React | 77 | 0 | 7 | 84 |
| [src/page/ProcessOrchestration/components/Config/Type/Sequence.tsx](/src/page/ProcessOrchestration/components/Config/Type/Sequence.tsx) | TypeScript React | 41 | 0 | 4 | 45 |
| [src/page/ProcessOrchestration/components/Config/Type/Sql.tsx](/src/page/ProcessOrchestration/components/Config/Type/Sql.tsx) | TypeScript React | 51 | 0 | 5 | 56 |
| [src/page/ProcessOrchestration/components/Config/Type/Start.tsx](/src/page/ProcessOrchestration/components/Config/Type/Start.tsx) | TypeScript React | 39 | 0 | 3 | 42 |
| [src/page/ProcessOrchestration/components/Config/Type/Switch.tsx](/src/page/ProcessOrchestration/components/Config/Type/Switch.tsx) | TypeScript React | 40 | 0 | 3 | 43 |
| [src/page/ProcessOrchestration/components/Config/Type/Sync.tsx](/src/page/ProcessOrchestration/components/Config/Type/Sync.tsx) | TypeScript React | 73 | 0 | 7 | 80 |
| [src/page/ProcessOrchestration/components/Config/Type/Throw.tsx](/src/page/ProcessOrchestration/components/Config/Type/Throw.tsx) | TypeScript React | 65 | 0 | 5 | 70 |
| [src/page/ProcessOrchestration/components/Config/Type/Trace.tsx](/src/page/ProcessOrchestration/components/Config/Type/Trace.tsx) | TypeScript React | 58 | 0 | 6 | 64 |
| [src/page/ProcessOrchestration/components/Config/Type/Transform.tsx](/src/page/ProcessOrchestration/components/Config/Type/Transform.tsx) | TypeScript React | 90 | 4 | 6 | 100 |
| [src/page/ProcessOrchestration/components/Config/Type/Until.tsx](/src/page/ProcessOrchestration/components/Config/Type/Until.tsx) | TypeScript React | 66 | 0 | 6 | 72 |
| [src/page/ProcessOrchestration/components/Config/Type/While.tsx](/src/page/ProcessOrchestration/components/Config/Type/While.tsx) | TypeScript React | 66 | 0 | 6 | 72 |
| [src/page/ProcessOrchestration/components/Config/Type/Xpath.tsx](/src/page/ProcessOrchestration/components/Config/Type/Xpath.tsx) | TypeScript React | 72 | 0 | 5 | 77 |
| [src/page/ProcessOrchestration/components/Config/Type/Xslt.tsx](/src/page/ProcessOrchestration/components/Config/Type/Xslt.tsx) | TypeScript React | 129 | 1 | 11 | 141 |
| [src/page/ProcessOrchestration/components/Config/Type/index.tsx](/src/page/ProcessOrchestration/components/Config/Type/index.tsx) | TypeScript React | 35 | 0 | 1 | 36 |
| [src/page/ProcessOrchestration/components/Config/hooks.tsx](/src/page/ProcessOrchestration/components/Config/hooks.tsx) | TypeScript React | 109 | 0 | 14 | 123 |
| [src/page/ProcessOrchestration/components/Config/index.tsx](/src/page/ProcessOrchestration/components/Config/index.tsx) | TypeScript React | 64 | 3 | 5 | 72 |
| [src/page/ProcessOrchestration/components/CopyFlow/index.tsx](/src/page/ProcessOrchestration/components/CopyFlow/index.tsx) | TypeScript React | 55 | 0 | 6 | 61 |
| [src/page/ProcessOrchestration/components/DragShape/index.less](/src/page/ProcessOrchestration/components/DragShape/index.less) | Less | 21 | 0 | 2 | 23 |
| [src/page/ProcessOrchestration/components/DragShape/index.tsx](/src/page/ProcessOrchestration/components/DragShape/index.tsx) | TypeScript React | 32 | 0 | 4 | 36 |
| [src/page/ProcessOrchestration/components/EditAssignKeyValue/index.tsx](/src/page/ProcessOrchestration/components/EditAssignKeyValue/index.tsx) | TypeScript React | 64 | 0 | 9 | 73 |
| [src/page/ProcessOrchestration/components/EditAssignParams/index.tsx](/src/page/ProcessOrchestration/components/EditAssignParams/index.tsx) | TypeScript React | 80 | 0 | 10 | 90 |
| [src/page/ProcessOrchestration/components/EditContext/index.tsx](/src/page/ProcessOrchestration/components/EditContext/index.tsx) | TypeScript React | 117 | 0 | 10 | 127 |
| [src/page/ProcessOrchestration/components/EditMsgBody/index.tsx](/src/page/ProcessOrchestration/components/EditMsgBody/index.tsx) | TypeScript React | 158 | 4 | 15 | 177 |
| [src/page/ProcessOrchestration/components/EditMsgCovert/index.tsx](/src/page/ProcessOrchestration/components/EditMsgCovert/index.tsx) | TypeScript React | 163 | 3 | 17 | 183 |
| [src/page/ProcessOrchestration/components/EditProject/index.tsx](/src/page/ProcessOrchestration/components/EditProject/index.tsx) | TypeScript React | 102 | 3 | 12 | 117 |
| [src/page/ProcessOrchestration/components/JsonCovertSchema/index.tsx](/src/page/ProcessOrchestration/components/JsonCovertSchema/index.tsx) | TypeScript React | 102 | 0 | 11 | 113 |
| [src/page/ProcessOrchestration/components/PathManagement/index.tsx](/src/page/ProcessOrchestration/components/PathManagement/index.tsx) | TypeScript React | 191 | 7 | 13 | 211 |
| [src/page/ProcessOrchestration/components/SelectMsg/index.tsx](/src/page/ProcessOrchestration/components/SelectMsg/index.tsx) | TypeScript React | 86 | 0 | 8 | 94 |
| [src/page/ProcessOrchestration/components/SelectPath/index.tsx](/src/page/ProcessOrchestration/components/SelectPath/index.tsx) | TypeScript React | 32 | 0 | 4 | 36 |
| [src/page/ProcessOrchestration/components/SelectTransform/index.tsx](/src/page/ProcessOrchestration/components/SelectTransform/index.tsx) | TypeScript React | 84 | 1 | 7 | 92 |
| [src/page/ProcessOrchestration/components/Start/index.less](/src/page/ProcessOrchestration/components/Start/index.less) | Less | 21 | 0 | 2 | 23 |
| [src/page/ProcessOrchestration/components/Start/index.tsx](/src/page/ProcessOrchestration/components/Start/index.tsx) | TypeScript React | 29 | 0 | 4 | 33 |
| [src/page/ProcessOrchestration/components/TestService/index.tsx](/src/page/ProcessOrchestration/components/TestService/index.tsx) | TypeScript React | 169 | 125 | 14 | 308 |
| [src/page/ProcessOrchestration/components/Tools/index.less](/src/page/ProcessOrchestration/components/Tools/index.less) | Less | 3 | 0 | 1 | 4 |
| [src/page/ProcessOrchestration/components/Tools/index.tsx](/src/page/ProcessOrchestration/components/Tools/index.tsx) | TypeScript React | 398 | 11 | 28 | 437 |
| [src/page/ProcessOrchestration/components/index.ts](/src/page/ProcessOrchestration/components/index.ts) | TypeScript | 46 | 0 | 2 | 48 |
| [src/page/ProcessOrchestration/hooks/index.ts](/src/page/ProcessOrchestration/hooks/index.ts) | TypeScript | 18 | 0 | 1 | 19 |
| [src/page/ProcessOrchestration/hooks/useActivityModel.ts](/src/page/ProcessOrchestration/hooks/useActivityModel.ts) | TypeScript | 87 | 5 | 7 | 99 |
| [src/page/ProcessOrchestration/hooks/useBlankClick.ts](/src/page/ProcessOrchestration/hooks/useBlankClick.ts) | TypeScript | 27 | 1 | 3 | 31 |
| [src/page/ProcessOrchestration/hooks/useDnd.ts](/src/page/ProcessOrchestration/hooks/useDnd.ts) | TypeScript | 178 | 0 | 9 | 187 |
| [src/page/ProcessOrchestration/hooks/useDown.ts](/src/page/ProcessOrchestration/hooks/useDown.ts) | TypeScript | 71 | 1 | 11 | 83 |
| [src/page/ProcessOrchestration/hooks/useEdge.ts](/src/page/ProcessOrchestration/hooks/useEdge.ts) | TypeScript | 73 | 4 | 5 | 82 |
| [src/page/ProcessOrchestration/hooks/useEdgeClick.ts](/src/page/ProcessOrchestration/hooks/useEdgeClick.ts) | TypeScript | 33 | 1 | 3 | 37 |
| [src/page/ProcessOrchestration/hooks/useEdgeConnected.ts](/src/page/ProcessOrchestration/hooks/useEdgeConnected.ts) | TypeScript | 48 | 0 | 5 | 53 |
| [src/page/ProcessOrchestration/hooks/useGeneral.ts](/src/page/ProcessOrchestration/hooks/useGeneral.ts) | TypeScript | 28 | 1 | 4 | 33 |
| [src/page/ProcessOrchestration/hooks/useGraph.ts](/src/page/ProcessOrchestration/hooks/useGraph.ts) | TypeScript | 0 | 0 | 1 | 1 |
| [src/page/ProcessOrchestration/hooks/useGroupList.ts](/src/page/ProcessOrchestration/hooks/useGroupList.ts) | TypeScript | 78 | 6 | 11 | 95 |
| [src/page/ProcessOrchestration/hooks/useMouseDown.ts](/src/page/ProcessOrchestration/hooks/useMouseDown.ts) | TypeScript | 18 | 1 | 3 | 22 |
| [src/page/ProcessOrchestration/hooks/useMouseMove.ts](/src/page/ProcessOrchestration/hooks/useMouseMove.ts) | TypeScript | 57 | 1 | 6 | 64 |
| [src/page/ProcessOrchestration/hooks/useNode.ts](/src/page/ProcessOrchestration/hooks/useNode.ts) | TypeScript | 127 | 10 | 10 | 147 |
| [src/page/ProcessOrchestration/hooks/useNodeClick.ts](/src/page/ProcessOrchestration/hooks/useNodeClick.ts) | TypeScript | 40 | 1 | 3 | 44 |
| [src/page/ProcessOrchestration/hooks/usePortClick.ts](/src/page/ProcessOrchestration/hooks/usePortClick.ts) | TypeScript | 56 | 0 | 7 | 63 |
| [src/page/ProcessOrchestration/hooks/usePreferences.ts](/src/page/ProcessOrchestration/hooks/usePreferences.ts) | TypeScript | 25 | 1 | 4 | 30 |
| [src/page/ProcessOrchestration/hooks/useProcessInfo.ts](/src/page/ProcessOrchestration/hooks/useProcessInfo.ts) | TypeScript | 57 | 5 | 10 | 72 |
| [src/page/ProcessOrchestration/hooks/useTableAssign.tsx](/src/page/ProcessOrchestration/hooks/useTableAssign.tsx) | TypeScript React | 67 | 0 | 5 | 72 |
| [src/page/ProcessOrchestration/hooks/useTableEdit.tsx](/src/page/ProcessOrchestration/hooks/useTableEdit.tsx) | TypeScript React | 48 | 1 | 8 | 57 |
| [src/page/ProcessOrchestration/index.tsx](/src/page/ProcessOrchestration/index.tsx) | TypeScript React | 5 | 0 | 2 | 7 |
| [src/page/ProcessOrchestration/mock.ts](/src/page/ProcessOrchestration/mock.ts) | TypeScript | 929 | 43 | 34 | 1,006 |
| [src/page/ProcessOrchestration/route.tsx](/src/page/ProcessOrchestration/route.tsx) | TypeScript React | 86 | 0 | 4 | 90 |
| [src/page/ProcessOrchestration/utils/connector.ts](/src/page/ProcessOrchestration/utils/connector.ts) | TypeScript | 283 | 58 | 11 | 352 |
| [src/page/ProcessOrchestration/utils/const.ts](/src/page/ProcessOrchestration/utils/const.ts) | TypeScript | 76 | 3 | 11 | 90 |
| [src/page/ProcessOrchestration/utils/edge.ts](/src/page/ProcessOrchestration/utils/edge.ts) | TypeScript | 146 | 11 | 17 | 174 |
| [src/page/ProcessOrchestration/utils/edgeKeyFeames.less](/src/page/ProcessOrchestration/utils/edgeKeyFeames.less) | Less | 5 | 0 | 1 | 6 |
| [src/page/ProcessOrchestration/utils/event.ts](/src/page/ProcessOrchestration/utils/event.ts) | TypeScript | 34 | 0 | 6 | 40 |
| [src/page/ProcessOrchestration/utils/groupNode.ts](/src/page/ProcessOrchestration/utils/groupNode.ts) | TypeScript | 12 | 0 | 4 | 16 |
| [src/page/ProcessOrchestration/utils/index.ts](/src/page/ProcessOrchestration/utils/index.ts) | TypeScript | 7 | 0 | 1 | 8 |
| [src/page/ProcessOrchestration/utils/node.ts](/src/page/ProcessOrchestration/utils/node.ts) | TypeScript | 405 | 12 | 42 | 459 |
| [src/page/ProcessOrchestration/utils/validator.ts](/src/page/ProcessOrchestration/utils/validator.ts) | TypeScript | 16 | 1 | 2 | 19 |
| [src/page/ReportForm/PublishReport/index.tsx](/src/page/ReportForm/PublishReport/index.tsx) | TypeScript React | 195 | 42 | 13 | 250 |
| [src/page/ReportForm/PublishReport/styles.module.less](/src/page/ReportForm/PublishReport/styles.module.less) | Less | 5 | 0 | 1 | 6 |
| [src/page/ReportForm/ReportDetails/index.tsx](/src/page/ReportForm/ReportDetails/index.tsx) | TypeScript React | 99 | 0 | 5 | 104 |
| [src/page/ReportForm/StatisticsDetails/index.tsx](/src/page/ReportForm/StatisticsDetails/index.tsx) | TypeScript React | 111 | 1 | 4 | 116 |
| [src/page/ReportForm/StatisticsReport/index.tsx](/src/page/ReportForm/StatisticsReport/index.tsx) | TypeScript React | 158 | 10 | 9 | 177 |
| [src/page/ReportForm/SubscribeReport/index.tsx](/src/page/ReportForm/SubscribeReport/index.tsx) | TypeScript React | 195 | 42 | 12 | 249 |
| [src/page/ReportForm/SubscribeReport/styles.module.less](/src/page/ReportForm/SubscribeReport/styles.module.less) | Less | 5 | 0 | 1 | 6 |
| [src/page/ReportForm/components/charts/index.tsx](/src/page/ReportForm/components/charts/index.tsx) | TypeScript React | 33 | 0 | 4 | 37 |
| [src/page/ReportForm/components/charts/options.ts](/src/page/ReportForm/components/charts/options.ts) | TypeScript | 212 | 6 | 5 | 223 |
| [src/page/ReportForm/components/charts/styles.module.less](/src/page/ReportForm/components/charts/styles.module.less) | Less | 4 | 0 | 1 | 5 |
| [src/page/ReportForm/components/queryFilter/index.tsx](/src/page/ReportForm/components/queryFilter/index.tsx) | TypeScript React | 51 | 0 | 6 | 57 |
| [src/page/ReportForm/index.tsx](/src/page/ReportForm/index.tsx) | TypeScript React | 5 | 0 | 2 | 7 |
| [src/page/ReportForm/route.tsx](/src/page/ReportForm/route.tsx) | TypeScript React | 54 | 0 | 4 | 58 |
| [src/page/ServiceRelease/AccessStrategyDetail/index.tsx](/src/page/ServiceRelease/AccessStrategyDetail/index.tsx) | TypeScript React | 136 | 11 | 11 | 158 |
| [src/page/ServiceRelease/AuthStrategyDetail/index.tsx](/src/page/ServiceRelease/AuthStrategyDetail/index.tsx) | TypeScript React | 136 | 11 | 11 | 158 |
| [src/page/ServiceRelease/FlowControl/index.tsx](/src/page/ServiceRelease/FlowControl/index.tsx) | TypeScript React | 218 | 25 | 13 | 256 |
| [src/page/ServiceRelease/FlowControlDetails/index.tsx](/src/page/ServiceRelease/FlowControlDetails/index.tsx) | TypeScript React | 135 | 11 | 12 | 158 |
| [src/page/ServiceRelease/InterfaceGroup/index.tsx](/src/page/ServiceRelease/InterfaceGroup/index.tsx) | TypeScript React | 73 | 1 | 5 | 79 |
| [src/page/ServiceRelease/InterfaceGroupDetails/columns.tsx](/src/page/ServiceRelease/InterfaceGroupDetails/columns.tsx) | TypeScript React | 124 | 3 | 2 | 129 |
| [src/page/ServiceRelease/InterfaceGroupDetails/index.tsx](/src/page/ServiceRelease/InterfaceGroupDetails/index.tsx) | TypeScript React | 158 | 4 | 11 | 173 |
| [src/page/ServiceRelease/InterfaceGroupManagement/index.tsx](/src/page/ServiceRelease/InterfaceGroupManagement/index.tsx) | TypeScript React | 206 | 4 | 10 | 220 |
| [src/page/ServiceRelease/InterfaceManagement/index.tsx](/src/page/ServiceRelease/InterfaceManagement/index.tsx) | TypeScript React | 175 | 7 | 15 | 197 |
| [src/page/ServiceRelease/ProxyCache/index.tsx](/src/page/ServiceRelease/ProxyCache/index.tsx) | TypeScript React | 218 | 28 | 13 | 259 |
| [src/page/ServiceRelease/ProxyCacheDetails/index.tsx](/src/page/ServiceRelease/ProxyCacheDetails/index.tsx) | TypeScript React | 184 | 11 | 12 | 207 |
| [src/page/ServiceRelease/SafelyControl/AccessStrategy.tsx](/src/page/ServiceRelease/SafelyControl/AccessStrategy.tsx) | TypeScript React | 183 | 7 | 16 | 206 |
| [src/page/ServiceRelease/SafelyControl/AuthStrategy.tsx](/src/page/ServiceRelease/SafelyControl/AuthStrategy.tsx) | TypeScript React | 184 | 7 | 17 | 208 |
| [src/page/ServiceRelease/SafelyControl/columns.ts](/src/page/ServiceRelease/SafelyControl/columns.ts) | TypeScript | 22 | 10 | 1 | 33 |
| [src/page/ServiceRelease/SafelyControl/index.tsx](/src/page/ServiceRelease/SafelyControl/index.tsx) | TypeScript React | 285 | 28 | 17 | 330 |
| [src/page/ServiceRelease/SafelyControl/index1.tsx](/src/page/ServiceRelease/SafelyControl/index1.tsx) | TypeScript React | 49 | 0 | 3 | 52 |
| [src/page/ServiceRelease/ServiceMonitoring/index.tsx](/src/page/ServiceRelease/ServiceMonitoring/index.tsx) | TypeScript React | 179 | 3 | 7 | 189 |
| [src/page/ServiceRelease/VersionManagement/index.tsx](/src/page/ServiceRelease/VersionManagement/index.tsx) | TypeScript React | 168 | 17 | 12 | 197 |
| [src/page/ServiceRelease/components/AddAccessStrategy/index.tsx](/src/page/ServiceRelease/components/AddAccessStrategy/index.tsx) | TypeScript React | 220 | 2 | 17 | 239 |
| [src/page/ServiceRelease/components/AddApi/index.less](/src/page/ServiceRelease/components/AddApi/index.less) | Less | 9 | 0 | 1 | 10 |
| [src/page/ServiceRelease/components/AddApi/index.tsx](/src/page/ServiceRelease/components/AddApi/index.tsx) | TypeScript React | 264 | 25 | 18 | 307 |
| [src/page/ServiceRelease/components/AddAuthStrategy/index.tsx](/src/page/ServiceRelease/components/AddAuthStrategy/index.tsx) | TypeScript React | 238 | 4 | 20 | 262 |
| [src/page/ServiceRelease/components/AddFlowControl/index.tsx](/src/page/ServiceRelease/components/AddFlowControl/index.tsx) | TypeScript React | 359 | 0 | 18 | 377 |
| [src/page/ServiceRelease/components/AddInputParameter/index.tsx](/src/page/ServiceRelease/components/AddInputParameter/index.tsx) | TypeScript React | 115 | 0 | 11 | 126 |
| [src/page/ServiceRelease/components/AddResInformation/index.tsx](/src/page/ServiceRelease/components/AddResInformation/index.tsx) | TypeScript React | 63 | 0 | 9 | 72 |
| [src/page/ServiceRelease/components/ApiSubCert/index.tsx](/src/page/ServiceRelease/components/ApiSubCert/index.tsx) | TypeScript React | 4 | 0 | 2 | 6 |
| [src/page/ServiceRelease/components/Audit/index.tsx](/src/page/ServiceRelease/components/Audit/index.tsx) | TypeScript React | 86 | 0 | 7 | 93 |
| [src/page/ServiceRelease/components/EditBackendService/index.tsx](/src/page/ServiceRelease/components/EditBackendService/index.tsx) | TypeScript React | 136 | 8 | 12 | 156 |
| [src/page/ServiceRelease/components/EditInterfaceGroup/index.tsx](/src/page/ServiceRelease/components/EditInterfaceGroup/index.tsx) | TypeScript React | 105 | 0 | 11 | 116 |
| [src/page/ServiceRelease/components/EditProxyCacheStrategy/index.tsx](/src/page/ServiceRelease/components/EditProxyCacheStrategy/index.tsx) | TypeScript React | 194 | 3 | 13 | 210 |
| [src/page/ServiceRelease/components/EditVersion/index.tsx](/src/page/ServiceRelease/components/EditVersion/index.tsx) | TypeScript React | 357 | 15 | 22 | 394 |
| [src/page/ServiceRelease/components/ImportExcel/index.tsx](/src/page/ServiceRelease/components/ImportExcel/index.tsx) | TypeScript React | 103 | 12 | 9 | 124 |
| [src/page/ServiceRelease/components/ImportSwagger/index.tsx](/src/page/ServiceRelease/components/ImportSwagger/index.tsx) | TypeScript React | 96 | 12 | 9 | 117 |
| [src/page/ServiceRelease/components/InterfaceHeader/index.tsx](/src/page/ServiceRelease/components/InterfaceHeader/index.tsx) | TypeScript React | 116 | 18 | 12 | 146 |
| [src/page/ServiceRelease/components/InterfaceSetting/index.tsx](/src/page/ServiceRelease/components/InterfaceSetting/index.tsx) | TypeScript React | 211 | 7 | 3 | 221 |
| [src/page/ServiceRelease/components/PublishGateWay/index.less](/src/page/ServiceRelease/components/PublishGateWay/index.less) | Less | 9 | 0 | 1 | 10 |
| [src/page/ServiceRelease/components/PublishGateWay/index.tsx](/src/page/ServiceRelease/components/PublishGateWay/index.tsx) | TypeScript React | 127 | 3 | 9 | 139 |
| [src/page/ServiceRelease/components/PublishInterface/index.less](/src/page/ServiceRelease/components/PublishInterface/index.less) | Less | 16 | 0 | 3 | 19 |
| [src/page/ServiceRelease/components/PublishInterface/index.tsx](/src/page/ServiceRelease/components/PublishInterface/index.tsx) | TypeScript React | 1,063 | 83 | 61 | 1,207 |
| [src/page/ServiceRelease/components/ServiceAddress/index.tsx](/src/page/ServiceRelease/components/ServiceAddress/index.tsx) | TypeScript React | 1 | 0 | 1 | 2 |
| [src/page/ServiceRelease/components/ServiceMonitoringTable/index.tsx](/src/page/ServiceRelease/components/ServiceMonitoringTable/index.tsx) | TypeScript React | 208 | 19 | 8 | 235 |
| [src/page/ServiceRelease/components/index.tsx](/src/page/ServiceRelease/components/index.tsx) | TypeScript React | 38 | 0 | 2 | 40 |
| [src/page/ServiceRelease/index.tsx](/src/page/ServiceRelease/index.tsx) | TypeScript React | 5 | 0 | 2 | 7 |
| [src/page/ServiceRelease/route.tsx](/src/page/ServiceRelease/route.tsx) | TypeScript React | 115 | 0 | 4 | 119 |
| [src/page/ServiceRelease/utils/interfaceTable.ts](/src/page/ServiceRelease/utils/interfaceTable.ts) | TypeScript | 75 | 0 | 2 | 77 |
| [src/page/ServiceSubscription/CraDetails/index.tsx](/src/page/ServiceSubscription/CraDetails/index.tsx) | TypeScript React | 128 | 6 | 11 | 145 |
| [src/page/ServiceSubscription/CraManagement/index.tsx](/src/page/ServiceSubscription/CraManagement/index.tsx) | TypeScript React | 217 | 13 | 13 | 243 |
| [src/page/ServiceSubscription/SubscriptionList/apiColumns.tsx](/src/page/ServiceSubscription/SubscriptionList/apiColumns.tsx) | TypeScript React | 118 | 0 | 2 | 120 |
| [src/page/ServiceSubscription/SubscriptionList/columns.ts](/src/page/ServiceSubscription/SubscriptionList/columns.ts) | TypeScript | 55 | 0 | 1 | 56 |
| [src/page/ServiceSubscription/SubscriptionList/index.tsx](/src/page/ServiceSubscription/SubscriptionList/index.tsx) | TypeScript React | 209 | 36 | 12 | 257 |
| [src/page/ServiceSubscription/components/Audit/index.tsx](/src/page/ServiceSubscription/components/Audit/index.tsx) | TypeScript React | 86 | 0 | 7 | 93 |
| [src/page/ServiceSubscription/components/CraStepTwoForm/index.tsx](/src/page/ServiceSubscription/components/CraStepTwoForm/index.tsx) | TypeScript React | 109 | 0 | 7 | 116 |
| [src/page/ServiceSubscription/components/CreateSub/index.tsx](/src/page/ServiceSubscription/components/CreateSub/index.tsx) | TypeScript React | 72 | 0 | 6 | 78 |
| [src/page/ServiceSubscription/components/EditCra/index.tsx](/src/page/ServiceSubscription/components/EditCra/index.tsx) | TypeScript React | 147 | 11 | 11 | 169 |
| [src/page/ServiceSubscription/components/Subscription/index.tsx](/src/page/ServiceSubscription/components/Subscription/index.tsx) | TypeScript React | 141 | 3 | 6 | 150 |
| [src/page/ServiceSubscription/components/SubscriptionColumns/index.tsx](/src/page/ServiceSubscription/components/SubscriptionColumns/index.tsx) | TypeScript React | 154 | 14 | 3 | 171 |
| [src/page/ServiceSubscription/components/index.tsx](/src/page/ServiceSubscription/components/index.tsx) | TypeScript React | 6 | 0 | 2 | 8 |
| [src/page/ServiceSubscription/index.tsx](/src/page/ServiceSubscription/index.tsx) | TypeScript React | 5 | 0 | 2 | 7 |
| [src/page/ServiceSubscription/route.tsx](/src/page/ServiceSubscription/route.tsx) | TypeScript React | 38 | 0 | 4 | 42 |
| [src/page/SystemManagement/AuditManagement/AuditInterface.tsx](/src/page/SystemManagement/AuditManagement/AuditInterface.tsx) | TypeScript React | 144 | 11 | 15 | 170 |
| [src/page/SystemManagement/AuditManagement/SubscriptionReview.tsx](/src/page/SystemManagement/AuditManagement/SubscriptionReview.tsx) | TypeScript React | 105 | 14 | 13 | 132 |
| [src/page/SystemManagement/AuditManagement/index.tsx](/src/page/SystemManagement/AuditManagement/index.tsx) | TypeScript React | 40 | 0 | 3 | 43 |
| [src/page/SystemManagement/HospitalManagement/index.tsx](/src/page/SystemManagement/HospitalManagement/index.tsx) | TypeScript React | 114 | 11 | 8 | 133 |
| [src/page/SystemManagement/LogDetail/ProcessCall.tsx](/src/page/SystemManagement/LogDetail/ProcessCall.tsx) | TypeScript React | 244 | 8 | 25 | 277 |
| [src/page/SystemManagement/LogDetail/index.tsx](/src/page/SystemManagement/LogDetail/index.tsx) | TypeScript React | 33 | 0 | 3 | 36 |
| [src/page/SystemManagement/LogManagement/index.tsx](/src/page/SystemManagement/LogManagement/index.tsx) | TypeScript React | 278 | 5 | 9 | 292 |
| [src/page/SystemManagement/LogManagement/styles.module.less](/src/page/SystemManagement/LogManagement/styles.module.less) | Less | 16 | 0 | 1 | 17 |
| [src/page/SystemManagement/Menu/index.tsx](/src/page/SystemManagement/Menu/index.tsx) | TypeScript React | 156 | 17 | 11 | 184 |
| [src/page/SystemManagement/Role/index.tsx](/src/page/SystemManagement/Role/index.tsx) | TypeScript React | 117 | 14 | 10 | 141 |
| [src/page/SystemManagement/UserGroupManagement/index.tsx](/src/page/SystemManagement/UserGroupManagement/index.tsx) | TypeScript React | 117 | 14 | 10 | 141 |
| [src/page/SystemManagement/UserManagement/index.tsx](/src/page/SystemManagement/UserManagement/index.tsx) | TypeScript React | 181 | 14 | 10 | 205 |
| [src/page/SystemManagement/components/EditHospital/index.tsx](/src/page/SystemManagement/components/EditHospital/index.tsx) | TypeScript React | 122 | 7 | 11 | 140 |
| [src/page/SystemManagement/components/EditMenu/index.tsx](/src/page/SystemManagement/components/EditMenu/index.tsx) | TypeScript React | 285 | 9 | 17 | 311 |
| [src/page/SystemManagement/components/EditRole/PowerModal.tsx](/src/page/SystemManagement/components/EditRole/PowerModal.tsx) | TypeScript React | 71 | 0 | 8 | 79 |
| [src/page/SystemManagement/components/EditRole/index.tsx](/src/page/SystemManagement/components/EditRole/index.tsx) | TypeScript React | 227 | 10 | 17 | 254 |
| [src/page/SystemManagement/components/EditUser/index.tsx](/src/page/SystemManagement/components/EditUser/index.tsx) | TypeScript React | 175 | 10 | 15 | 200 |
| [src/page/SystemManagement/components/EditUserGroup/index.tsx](/src/page/SystemManagement/components/EditUserGroup/index.tsx) | TypeScript React | 129 | 7 | 14 | 150 |
| [src/page/SystemManagement/components/LogDetail/ProcessCall.tsx](/src/page/SystemManagement/components/LogDetail/ProcessCall.tsx) | TypeScript React | 33 | 0 | 5 | 38 |
| [src/page/SystemManagement/components/LogDetail/index.tsx](/src/page/SystemManagement/components/LogDetail/index.tsx) | TypeScript React | 46 | 0 | 5 | 51 |
| [src/page/SystemManagement/components/index.tsx](/src/page/SystemManagement/components/index.tsx) | TypeScript React | 7 | 0 | 2 | 9 |
| [src/page/SystemManagement/index.tsx](/src/page/SystemManagement/index.tsx) | TypeScript React | 5 | 0 | 2 | 7 |
| [src/page/SystemManagement/route.tsx](/src/page/SystemManagement/route.tsx) | TypeScript React | 78 | 7 | 4 | 89 |
| [src/page/index.tsx](/src/page/index.tsx) | TypeScript React | 9 | 0 | 3 | 12 |
| [src/page/route.tsx](/src/page/route.tsx) | TypeScript React | 74 | 12 | 9 | 95 |
| [src/service/bigScreen.ts](/src/service/bigScreen.ts) | TypeScript | 9 | 0 | 3 | 12 |
| [src/service/getway-list.ts](/src/service/getway-list.ts) | TypeScript | 19 | 0 | 3 | 22 |
| [src/service/index.ts](/src/service/index.ts) | TypeScript | 6 | 0 | 2 | 8 |
| [src/service/interface-integration.ts](/src/service/interface-integration.ts) | TypeScript | 35 | 0 | 3 | 38 |
| [src/service/process-orchestration.ts](/src/service/process-orchestration.ts) | TypeScript | 77 | 0 | 3 | 80 |
| [src/service/report-form.ts](/src/service/report-form.ts) | TypeScript | 7 | 0 | 3 | 10 |
| [src/service/service-release.ts](/src/service/service-release.ts) | TypeScript | 177 | 1 | 3 | 181 |
| [src/service/service-subscription.ts](/src/service/service-subscription.ts) | TypeScript | 38 | 0 | 3 | 41 |
| [src/service/system-management.ts](/src/service/system-management.ts) | TypeScript | 82 | 0 | 2 | 84 |
| [src/setupTests.js](/src/setupTests.js) | JavaScript | 1 | 4 | 1 | 6 |
| [src/store/global.ts](/src/store/global.ts) | TypeScript | 67 | 0 | 5 | 72 |
| [src/store/historyTab.ts](/src/store/historyTab.ts) | TypeScript | 20 | 0 | 5 | 25 |
| [src/store/index.ts](/src/store/index.ts) | TypeScript | 28 | 9 | 3 | 40 |
| [src/store/processOrchestration.ts](/src/store/processOrchestration.ts) | TypeScript | 99 | 3 | 5 | 107 |
| [src/utils/enum.ts](/src/utils/enum.ts) | TypeScript | 74 | 24 | 15 | 113 |
| [src/utils/host.ts](/src/utils/host.ts) | TypeScript | 18 | 3 | 6 | 27 |
| [src/utils/jsonToSchema.ts](/src/utils/jsonToSchema.ts) | TypeScript | 93 | 0 | 11 | 104 |
| [src/utils/reg.ts](/src/utils/reg.ts) | TypeScript | 3 | 1 | 1 | 5 |
| [src/utils/request.ts](/src/utils/request.ts) | TypeScript | 92 | 13 | 9 | 114 |
| [src/utils/resource.ts](/src/utils/resource.ts) | TypeScript | 171 | 10 | 2 | 183 |
| [src/utils/utils.ts](/src/utils/utils.ts) | TypeScript | 66 | 3 | 7 | 76 |
| [tsconfig.json](/tsconfig.json) | JSON with Comments | 15 | 3 | 1 | 19 |
| [typings.d.ts](/typings.d.ts) | TypeScript | 16 | 1 | 7 | 24 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)