{"file:///Users/<USER>/Desktop/work/apimange-web/src/store/index.ts": {"language": "TypeScript", "code": 28, "comment": 9, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/store/historyTab.ts": {"language": "TypeScript", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/logo.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/setupTests.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/App.less": {"language": "Less", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/store/global.ts": {"language": "TypeScript", "code": 67, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/store/processOrchestration.ts": {"language": "TypeScript", "code": 99, "comment": 3, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/index.css": {"language": "CSS", "code": 86, "comment": 36, "blank": 20}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/system-management.ts": {"language": "TypeScript", "code": 82, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/index.ts": {"language": "TypeScript", "code": 6, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/report-form.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/bigScreen.ts": {"language": "TypeScript", "code": 9, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/service-release.ts": {"language": "TypeScript", "code": 177, "comment": 1, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/App.tsx": {"language": "TypeScript React", "code": 7, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/tsconfig.json": {"language": "JSON with Comments", "code": 15, "comment": 3, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/getway-list.ts": {"language": "TypeScript", "code": 19, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/index.tsx": {"language": "TypeScript React", "code": 25, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/commitlint.config.js": {"language": "JavaScript", "code": 17, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/process-orchestration.ts": {"language": "TypeScript", "code": 77, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/utils/resource.ts": {"language": "TypeScript", "code": 171, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/utils/utils.ts": {"language": "TypeScript", "code": 66, "comment": 3, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/utils/reg.ts": {"language": "TypeScript", "code": 3, "comment": 1, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/utils/enum.ts": {"language": "TypeScript", "code": 74, "comment": 24, "blank": 15}, "file:///Users/<USER>/Desktop/work/apimange-web/src/utils/request.ts": {"language": "TypeScript", "code": 92, "comment": 13, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/utils/jsonToSchema.ts": {"language": "TypeScript", "code": 93, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/.prettierrc.js": {"language": "JavaScript", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/utils/host.ts": {"language": "TypeScript", "code": 18, "comment": 3, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/package.json": {"language": "JSON", "code": 220, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/index.tsx": {"language": "TypeScript React", "code": 9, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/typings.d.ts": {"language": "TypeScript", "code": 16, "comment": 1, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/PageTitle/index.tsx": {"language": "TypeScript React", "code": 11, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/README.md": {"language": "<PERSON><PERSON>", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useCancleRequest.tsx": {"language": "TypeScript React", "code": 14, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useLocationChange.tsx": {"language": "TypeScript React", "code": 20, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useAsyncEffect.tsx": {"language": "TypeScript React", "code": 28, "comment": 1, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/SelectApiTable/index.less": {"language": "Less", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useNoticeCount.tsx": {"language": "TypeScript React", "code": 16, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useNotice.tsx": {"language": "TypeScript React", "code": 80, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/route.tsx": {"language": "TypeScript React", "code": 74, "comment": 12, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/EchartsRender/index.tsx": {"language": "TypeScript React", "code": 54, "comment": 3, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/SelectApiTable/index.tsx": {"language": "TypeScript React", "code": 57, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/404/index.tsx": {"language": "TypeScript React", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/manifest.json": {"language": "JSON", "code": 25, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Editor/index.tsx": {"language": "TypeScript React", "code": 14, "comment": 1, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/AuthResource/index.tsx": {"language": "TypeScript React", "code": 23, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/CraDetails/index.tsx": {"language": "TypeScript React", "code": 128, "comment": 6, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/README.md": {"language": "<PERSON><PERSON>", "code": 55, "comment": 0, "blank": 42}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/package.json": {"language": "JSON", "code": 59, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/service-subscription.ts": {"language": "TypeScript", "code": 38, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/SubscriptionList/columns.ts": {"language": "TypeScript", "code": 55, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/SubscriptionList/apiColumns.tsx": {"language": "TypeScript React", "code": 118, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/StatisticsDetails/index.tsx": {"language": "TypeScript React", "code": 111, "comment": 1, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/SubscriptionList/index.tsx": {"language": "TypeScript React", "code": 209, "comment": 36, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/index.tsx": {"language": "TypeScript React", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/ApiAudit/columns.ts": {"language": "TypeScript", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/ApiAudit/index.tsx": {"language": "TypeScript React", "code": 117, "comment": 10, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/route.tsx": {"language": "TypeScript React", "code": 30, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/Subscribe/SubscriptionColumns.tsx": {"language": "TypeScript React", "code": 55, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/metadata.js": {"language": "JavaScript", "code": 544, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/metadata.d.ts": {"language": "TypeScript", "code": 14, "comment": 5, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/ReportDetails/index.tsx": {"language": "TypeScript React", "code": 99, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/loader.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/nls.metadata.json": {"language": "JSON", "code": 3654, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/PublishReport/index.tsx": {"language": "TypeScript React", "code": 195, "comment": 42, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/bundleInfo.json": {"language": "JSON", "code": 7259, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/loader.js": {"language": "JavaScript", "code": 1719, "comment": 238, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/PublishReport/styles.module.less": {"language": "Less", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.js": {"language": "JavaScript", "code": 1549, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.ja.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.es.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.fr.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.api.js": {"language": "JavaScript", "code": 49, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/markers/common/markers.js": {"language": "JavaScript", "code": 110, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/editor/common/editor.js": {"language": "JavaScript", "code": 5, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/jsonschemas/common/jsonContributionRegistry.js": {"language": "JavaScript", "code": 26, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/markers/common/markerService.js": {"language": "JavaScript", "code": 243, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/accessibility/common/accessibility.js": {"language": "JavaScript", "code": 4, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/dialogs/common/dialogs.js": {"language": "JavaScript", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/editorAction.js": {"language": "JavaScript", "code": 19, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model.js": {"language": "JavaScript", "code": 84, "comment": 37, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/editorContextKeys.js": {"language": "JavaScript", "code": 44, "comment": 20, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/clipboard/browser/clipboardService.js": {"language": "JavaScript", "code": 68, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/prefixSumComputer.js": {"language": "JavaScript", "code": 196, "comment": 27, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModel.js": {"language": "JavaScript", "code": 100, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelEventDispatcher.js": {"language": "JavaScript", "code": 265, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/monospaceLineBreaksComputer.js": {"language": "JavaScript", "code": 394, "comment": 46, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelDecorations.js": {"language": "JavaScript", "code": 140, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelLines.js": {"language": "JavaScript", "code": 855, "comment": 72, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewEventHandler.js": {"language": "JavaScript", "code": 177, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelImpl.js": {"language": "JavaScript", "code": 887, "comment": 36, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/minimapTokensColorTracker.js": {"language": "JavaScript", "code": 49, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjection.js": {"language": "JavaScript", "code": 297, "comment": 20, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjectionData.js": {"language": "JavaScript", "code": 219, "comment": 49, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/clipboard/common/clipboardService.js": {"language": "JavaScript", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/standaloneStrings.js": {"language": "JavaScript", "code": 62, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/standalone/standaloneEnums.js": {"language": "JavaScript", "code": 530, "comment": 328, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/config/fontInfo.js": {"language": "JavaScript", "code": 102, "comment": 37, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/standalone/standaloneBase.js": {"language": "JavaScript", "code": 36, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/config/editorOptions.js": {"language": "JavaScript", "code": 2362, "comment": 166, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/config/editorZoom.js": {"language": "JavaScript", "code": 19, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/config/commonEditorConfig.js": {"language": "JavaScript", "code": 550, "comment": 29, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorTypeOperations.js": {"language": "JavaScript", "code": 827, "comment": 78, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/oneCursor.js": {"language": "JavaScript", "code": 97, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/diff/diffComputer.js": {"language": "JavaScript", "code": 378, "comment": 20, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorEvents.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textModel.js": {"language": "JavaScript", "code": 2301, "comment": 145, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textModelEvents.js": {"language": "JavaScript", "code": 135, "comment": 39, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textModelTokens.js": {"language": "JavaScript", "code": 381, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/editStack.js": {"language": "JavaScript", "code": 352, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorMoveCommands.js": {"language": "JavaScript", "code": 601, "comment": 40, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/tokensStore.js": {"language": "JavaScript", "code": 873, "comment": 151, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorColumnSelection.js": {"language": "JavaScript", "code": 83, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports.js": {"language": "JavaScript", "code": 45, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/languageSelector.js": {"language": "JavaScript", "code": 79, "comment": 14, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursor.js": {"language": "JavaScript", "code": 776, "comment": 63, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorWordOperations.js": {"language": "JavaScript", "code": 643, "comment": 35, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/modesRegistry.js": {"language": "JavaScript", "code": 72, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/commands/replaceCommand.js": {"language": "JavaScript", "code": 78, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/languageFeatureRegistry.js": {"language": "JavaScript", "code": 182, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorCollection.js": {"language": "JavaScript", "code": 231, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/nullMode.js": {"language": "JavaScript", "code": 24, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/editorWorkerService.js": {"language": "JavaScript", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/languageConfigurationRegistry.js": {"language": "JavaScript", "code": 748, "comment": 63, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js": {"language": "JavaScript", "code": 247, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/webWorker.js": {"language": "JavaScript", "code": 56, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/wordCharacterClassifier.js": {"language": "JavaScript", "code": 21, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js": {"language": "JavaScript", "code": 446, "comment": 45, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/modeService.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/overviewZoneManager.js": {"language": "JavaScript", "code": 159, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/languageConfiguration.js": {"language": "JavaScript", "code": 93, "comment": 32, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/unicodeTextModelHighlighter.js": {"language": "JavaScript", "code": 145, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/markersDecorationService.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/editorColorRegistry.js": {"language": "JavaScript", "code": 90, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/commands/surroundSelectionCommand.js": {"language": "JavaScript", "code": 19, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textModelSearch.js": {"language": "JavaScript", "code": 413, "comment": 43, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorDeleteOperations.js": {"language": "JavaScript", "code": 199, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/linkComputer.js": {"language": "JavaScript", "code": 240, "comment": 25, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorCommon.js": {"language": "JavaScript", "code": 216, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/tokenizationRegistry.js": {"language": "JavaScript", "code": 75, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsImpl.js": {"language": "JavaScript", "code": 610, "comment": 44, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/commands/shiftCommand.js": {"language": "JavaScript", "code": 200, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorAtomicMoveOperations.js": {"language": "JavaScript", "code": 103, "comment": 39, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/commands/trimTrailingWhitespaceCommand.js": {"language": "JavaScript", "code": 67, "comment": 14, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/modelService.js": {"language": "JavaScript", "code": 5, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/getIconClasses.js": {"language": "JavaScript", "code": 63, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js": {"language": "JavaScript", "code": 73, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/markerDecorationsServiceImpl.js": {"language": "JavaScript", "code": 187, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLayout.js": {"language": "JavaScript", "code": 315, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/modeServiceImpl.js": {"language": "JavaScript", "code": 93, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/lineDecorations.js": {"language": "JavaScript", "code": 189, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLineRenderer.js": {"language": "JavaScript", "code": 799, "comment": 76, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/resolverService.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/colorizedBracketPairsDecorationProvider.js": {"language": "JavaScript", "code": 83, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js": {"language": "JavaScript", "code": 237, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLinesViewportData.js": {"language": "JavaScript", "code": 19, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/getSemanticTokens.js": {"language": "JavaScript", "code": 198, "comment": 19, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/indentationGuesser.js": {"language": "JavaScript", "code": 138, "comment": 38, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/linesLayout.js": {"language": "JavaScript", "code": 600, "comment": 154, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorColumns.js": {"language": "JavaScript", "code": 111, "comment": 20, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorMoveOperations.js": {"language": "JavaScript", "code": 246, "comment": 25, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/textToHtmlTokenizer.js": {"language": "JavaScript", "code": 114, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/wordHelper.js": {"language": "JavaScript", "code": 97, "comment": 19, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textChange.js": {"language": "JavaScript", "code": 237, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/modelServiceImpl.js": {"language": "JavaScript", "code": 713, "comment": 31, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/intervalTree.js": {"language": "JavaScript", "code": 860, "comment": 112, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer.js": {"language": "JavaScript", "code": 408, "comment": 37, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/decorationProvider.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairs.js": {"language": "JavaScript", "code": 25, "comment": 14, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/mirrorTextModel.js": {"language": "JavaScript", "code": 94, "comment": 20, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase.js": {"language": "JavaScript", "code": 344, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder.js": {"language": "JavaScript", "code": 125, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase.js": {"language": "JavaScript", "code": 1366, "comment": 86, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/editorCommon.js": {"language": "JavaScript", "code": 4, "comment": 3, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/accessibility/browser/accessibilityService.js": {"language": "JavaScript", "code": 48, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/textResourceConfigurationService.js": {"language": "JavaScript", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/tokenization.js": {"language": "JavaScript", "code": 263, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/renderingContext.js": {"language": "JavaScript", "code": 89, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/viewContext.js": {"language": "JavaScript", "code": 28, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/inplaceReplaceSupport.js": {"language": "JavaScript", "code": 79, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/viewEvents.js": {"language": "JavaScript", "code": 126, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/indentRules.js": {"language": "JavaScript", "code": 53, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/electricCharacter.js": {"language": "JavaScript", "code": 47, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/richEditBrackets.js": {"language": "JavaScript", "code": 262, "comment": 100, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/onEnter.js": {"language": "JavaScript", "code": 99, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/characterPair.js": {"language": "JavaScript", "code": 47, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/browser/iconsStyleSheet.js": {"language": "JavaScript", "code": 47, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/length.js": {"language": "JavaScript", "code": 87, "comment": 33, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/colorRegistry.js": {"language": "JavaScript", "code": 353, "comment": 87, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/styler.js": {"language": "JavaScript", "code": 78, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/theme.js": {"language": "JavaScript", "code": 6, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/iconRegistry.js": {"language": "JavaScript", "code": 128, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/concat23Trees.js": {"language": "JavaScript", "code": 147, "comment": 42, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/editorWorkerServiceImpl.js": {"language": "JavaScript", "code": 416, "comment": 22, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/bracketPairsTree.js": {"language": "JavaScript", "code": 163, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/themeService.js": {"language": "JavaScript", "code": 113, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/brackets.js": {"language": "JavaScript", "code": 94, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/nodeReader.js": {"language": "JavaScript", "code": 95, "comment": 29, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/environment/common/environment.js": {"language": "JavaScript", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/position.js": {"language": "JavaScript", "code": 77, "comment": 57, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/commands/common/commands.js": {"language": "JavaScript", "code": 81, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/stringBuilder.js": {"language": "JavaScript", "code": 136, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/editOperation.js": {"language": "JavaScript", "code": 29, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/lineTokens.js": {"language": "JavaScript", "code": 204, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/selection.js": {"language": "JavaScript", "code": 91, "comment": 51, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/token.js": {"language": "JavaScript", "code": 25, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/characterClassifier.js": {"language": "JavaScript", "code": 44, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/range.js": {"language": "JavaScript", "code": 266, "comment": 110, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/beforeEditPositionMapper.js": {"language": "JavaScript", "code": 75, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/files/common/files.js": {"language": "JavaScript", "code": 6, "comment": 3, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/ast.js": {"language": "JavaScript", "code": 423, "comment": 44, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/telemetry/common/telemetry.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/tokenizer.js": {"language": "JavaScript", "code": 254, "comment": 41, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/parser.js": {"language": "JavaScript", "code": 92, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet.js": {"language": "JavaScript", "code": 89, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/telemetry/common/gdprTypings.js": {"language": "JavaScript", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/registry/common/platform.js": {"language": "JavaScript", "code": 17, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/log/common/log.js": {"language": "JavaScript", "code": 81, "comment": 1, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/rgba.js": {"language": "JavaScript", "code": 25, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/browser/contextScopedHistoryWidget.js": {"language": "JavaScript", "code": 85, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/browser/historyWidgetKeybindingHint.js": {"language": "JavaScript", "code": 4, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/undoRedo/common/undoRedo.js": {"language": "JavaScript", "code": 36, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/list/browser/listService.js": {"language": "JavaScript", "code": 877, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/actions/common/actions.js": {"language": "JavaScript", "code": 204, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.de.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.it.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/actions/common/menuService.js": {"language": "JavaScript", "code": 167, "comment": 30, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/undoRedo/common/undoRedoService.js": {"language": "JavaScript", "code": 1048, "comment": 49, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/opener/common/opener.js": {"language": "JavaScript", "code": 39, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.js": {"language": "JavaScript", "code": 397, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/severityIcon/common/severityIcon.js": {"language": "JavaScript", "code": 63, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/opener/browser/link.js": {"language": "JavaScript", "code": 90, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.zh-tw.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextkey/common/contextkey.js": {"language": "JavaScript", "code": 1170, "comment": 38, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/extensions/common/extensions.js": {"language": "JavaScript", "code": 12, "comment": 19, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextkey/common/contextkeys.js": {"language": "JavaScript", "code": 11, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextkey/browser/contextKeyService.js": {"language": "JavaScript", "code": 380, "comment": 21, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.css": {"language": "CSS", "code": 49, "comment": 4, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.zh-cn.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.css": {"language": "CSS", "code": 3, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/workspaces/common/workspaces.js": {"language": "JavaScript", "code": 23, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes.js": {"language": "JavaScript", "code": 270, "comment": 145, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextView.js": {"language": "JavaScript", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.js": {"language": "JavaScript", "code": 116, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/label/common/label.js": {"language": "JavaScript", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextViewService.js": {"language": "JavaScript", "code": 61, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuService.js": {"language": "JavaScript", "code": 47, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/common/quickAccess.js": {"language": "JavaScript", "code": 40, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/common/quickInput.js": {"language": "JavaScript", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/storage/common/storage.js": {"language": "JavaScript", "code": 134, "comment": 22, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/event.js": {"language": "JavaScript", "code": 21, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.ko.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/keyboardEvent.js": {"language": "JavaScript", "code": 108, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/worker/defaultWorkerFactory.js": {"language": "JavaScript", "code": 76, "comment": 31, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/fastDomNode.js": {"language": "JavaScript", "code": 199, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/mouseEvent.js": {"language": "JavaScript", "code": 109, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/touch.js": {"language": "JavaScript", "code": 244, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputList.js": {"language": "JavaScript", "code": 595, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputBox.js": {"language": "JavaScript", "code": 85, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/types.js": {"language": "JavaScript", "code": 108, "comment": 40, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/editorExtensions.js": {"language": "JavaScript", "code": 416, "comment": 25, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/keybindings.js": {"language": "JavaScript", "code": 92, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/assert.js": {"language": "JavaScript", "code": 5, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/marshalling.js": {"language": "JavaScript", "code": 35, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/mime.js": {"language": "JavaScript", "code": 163, "comment": 28, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/codicons.js": {"language": "JavaScript", "code": 158, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/actions.js": {"language": "JavaScript", "code": 156, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/bulkEditService.js": {"language": "JavaScript", "code": 42, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/event.js": {"language": "JavaScript", "code": 483, "comment": 101, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/history.js": {"language": "JavaScript", "code": 69, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/fuzzyScorer.js": {"language": "JavaScript", "code": 116, "comment": 22, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/filters.js": {"language": "JavaScript", "code": 617, "comment": 76, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/abstractCodeEditorService.js": {"language": "JavaScript", "code": 73, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/markerDecorations.js": {"language": "JavaScript", "code": 23, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/codeEditorServiceImpl.js": {"language": "JavaScript", "code": 39, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/common/quickInput.js": {"language": "JavaScript", "code": 14, "comment": 14, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/media/quickInput.css": {"language": "CSS", "code": 230, "comment": 10, "blank": 52}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/decorators.js": {"language": "JavaScript", "code": 30, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/errors.js": {"language": "JavaScript", "code": 84, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/async.js": {"language": "JavaScript", "code": 652, "comment": 136, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/openerService.js": {"language": "JavaScript", "code": 226, "comment": 27, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/htmlContent.js": {"language": "JavaScript", "code": 92, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/extpath.js": {"language": "JavaScript", "code": 118, "comment": 33, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/codeEditorService.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/charCode.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/jsonSchema.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/color.js": {"language": "JavaScript", "code": 395, "comment": 48, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/objects.js": {"language": "JavaScript", "code": 150, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/base/worker/workerMain.js": {"language": "JavaScript", "code": 11039, "comment": 2334, "blank": 44}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/severity.js": {"language": "JavaScript", "code": 41, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/lazy.js": {"language": "JavaScript", "code": 24, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/arrays.js": {"language": "JavaScript", "code": 249, "comment": 68, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/uuid.js": {"language": "JavaScript", "code": 45, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/numbers.js": {"language": "JavaScript", "code": 17, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/glob.js": {"language": "JavaScript", "code": 453, "comment": 46, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/hash.js": {"language": "JavaScript", "code": 239, "comment": 19, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/keybindingLabels.js": {"language": "JavaScript", "code": 97, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/labels.js": {"language": "JavaScript", "code": 25, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/keyCodes.js": {"language": "JavaScript", "code": 349, "comment": 24, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/comparers.js": {"language": "JavaScript", "code": 53, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/diff/diffChange.js": {"language": "JavaScript", "code": 14, "comment": 18, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/diff/diff.js": {"language": "JavaScript", "code": 652, "comment": 247, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/marked/marked.js": {"language": "JavaScript", "code": 2055, "comment": 419, "blank": 520}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewUserInputEvents.js": {"language": "JavaScript", "code": 100, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/domLineBreaksComputer.js": {"language": "JavaScript", "code": 272, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewLayer.js": {"language": "JavaScript", "code": 416, "comment": 47, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewController.js": {"language": "JavaScript", "code": 257, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/dynamicViewOverlay.js": {"language": "JavaScript", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewOverlays.js": {"language": "JavaScript", "code": 192, "comment": 14, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewImpl.js": {"language": "JavaScript", "code": 381, "comment": 27, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewPart.js": {"language": "JavaScript", "code": 47, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/lifecycle.js": {"language": "JavaScript", "code": 214, "comment": 34, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/buffer.js": {"language": "JavaScript", "code": 55, "comment": 2, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/network.js": {"language": "JavaScript", "code": 100, "comment": 48, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/iconLabels.js": {"language": "JavaScript", "code": 97, "comment": 24, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/uri.js": {"language": "JavaScript", "code": 437, "comment": 152, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/process.js": {"language": "JavaScript", "code": 30, "comment": 25, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/resources.js": {"language": "JavaScript", "code": 205, "comment": 22, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/search.js": {"language": "JavaScript", "code": 44, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/idGenerator.js": {"language": "JavaScript", "code": 10, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/stopwatch.js": {"language": "JavaScript", "code": 24, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/platform.js": {"language": "JavaScript", "code": 132, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/scrollable.js": {"language": "JavaScript", "code": 289, "comment": 27, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/functional.js": {"language": "JavaScript", "code": 13, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/navigator.js": {"language": "JavaScript", "code": 30, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/collections.js": {"language": "JavaScript", "code": 43, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/errorMessage.js": {"language": "JavaScript", "code": 53, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/iterator.js": {"language": "JavaScript", "code": 134, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/path.js": {"language": "JavaScript", "code": 1120, "comment": 259, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/paging.js": {"language": "JavaScript", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/styler.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/uint.js": {"language": "JavaScript", "code": 18, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/map.js": {"language": "JavaScript", "code": 984, "comment": 58, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/cancellation.js": {"language": "JavaScript", "code": 98, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/strings.js": {"language": "JavaScript", "code": 567, "comment": 187, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/textAreaInput.js": {"language": "JavaScript", "code": 510, "comment": 106, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/worker/simpleWorker.js": {"language": "JavaScript", "code": 407, "comment": 34, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/textAreaState.js": {"language": "JavaScript", "code": 242, "comment": 29, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/dompurify/dompurify.js": {"language": "JavaScript", "code": 772, "comment": 376, "blank": 237}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/textAreaHandler.css": {"language": "CSS", "code": 16, "comment": 17, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/mouseTarget.js": {"language": "JavaScript", "code": 746, "comment": 73, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/pointerHandler.js": {"language": "JavaScript", "code": 110, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/textAreaHandler.js": {"language": "JavaScript", "code": 498, "comment": 46, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/sequence.js": {"language": "JavaScript", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/range.js": {"language": "JavaScript", "code": 36, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/linkedList.js": {"language": "JavaScript", "code": 113, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/editorBrowser.js": {"language": "JavaScript", "code": 26, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/mouseHandler.js": {"language": "JavaScript", "code": 439, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/coreCommands.js": {"language": "JavaScript", "code": 1503, "comment": 37, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/embeddedCodeEditorWidget.js": {"language": "JavaScript", "code": 48, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/inlineDiffMargin.js": {"language": "JavaScript", "code": 178, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/media/diffEditor.css": {"language": "CSS", "code": 43, "comment": 8, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/media/diffReview.css": {"language": "CSS", "code": 46, "comment": 4, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/diffReview.js": {"language": "JavaScript", "code": 712, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/codeEditorWidget.js": {"language": "JavaScript", "code": 1497, "comment": 19, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/diffNavigator.js": {"language": "JavaScript", "code": 153, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/media/editor.css": {"language": "CSS", "code": 19, "comment": 14, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/diffEditorWidget.js": {"language": "JavaScript", "code": 1881, "comment": 63, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/config/charWidthReader.js": {"language": "JavaScript", "code": 107, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/config/configuration.js": {"language": "JavaScript", "code": 246, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/editorDom.js": {"language": "JavaScript", "code": 222, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/config/elementSizeObserver.js": {"language": "JavaScript", "code": 77, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInput.js": {"language": "JavaScript", "code": 1268, "comment": 32, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputUtils.js": {"language": "JavaScript", "code": 22, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/core/keybindingCancellation.js": {"language": "JavaScript", "code": 72, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/core/markdownRenderer.js": {"language": "JavaScript", "code": 97, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/core/editorState.js": {"language": "JavaScript", "code": 134, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/rulers/rulers.css": {"language": "CSS", "code": 4, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/rulers/rulers.js": {"language": "JavaScript", "code": 75, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.js": {"language": "JavaScript", "code": 73, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.css": {"language": "CSS", "code": 6, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLines.css": {"language": "CSS", "code": 21, "comment": 20, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLines.js": {"language": "JavaScript", "code": 577, "comment": 73, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/rangeUtil.js": {"language": "JavaScript", "code": 95, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.js": {"language": "JavaScript", "code": 149, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler/overviewRuler.js": {"language": "JavaScript", "code": 121, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler.js": {"language": "JavaScript", "code": 341, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/margin/margin.js": {"language": "JavaScript", "code": 55, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.css": {"language": "CSS", "code": 4, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/contentWidgets/contentWidgets.js": {"language": "JavaScript", "code": 413, "comment": 32, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/viewZones/viewZones.js": {"language": "JavaScript", "code": 318, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.css": {"language": "CSS", "code": 5, "comment": 4, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.js": {"language": "JavaScript", "code": 97, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/decorations/decorations.css": {"language": "CSS", "code": 3, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/decorations/decorations.js": {"language": "JavaScript", "code": 182, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.js": {"language": "JavaScript", "code": 68, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLine.js": {"language": "JavaScript", "code": 445, "comment": 63, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.css": {"language": "CSS", "code": 10, "comment": 8, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.css": {"language": "CSS", "code": 6, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.js": {"language": "JavaScript", "code": 220, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.css": {"language": "CSS", "code": 68, "comment": 8, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/editorScrollbar/editorScrollbar.js": {"language": "JavaScript", "code": 169, "comment": 12, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.js": {"language": "JavaScript", "code": 173, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.js": {"language": "JavaScript", "code": 301, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimap.css": {"language": "CSS", "code": 24, "comment": 6, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursor.js": {"language": "JavaScript", "code": 152, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.css": {"language": "CSS", "code": 17, "comment": 4, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapPreBaked.js": {"language": "JavaScript", "code": 30, "comment": 19, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharSheet.js": {"language": "JavaScript", "code": 18, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimap.js": {"language": "JavaScript", "code": 1331, "comment": 107, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRenderer.js": {"language": "JavaScript", "code": 83, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.css": {"language": "CSS", "code": 18, "comment": 4, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRendererFactory.js": {"language": "JavaScript", "code": 97, "comment": 36, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/storage/common/storage.js": {"language": "JavaScript", "code": 167, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.js": {"language": "JavaScript", "code": 154, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/formattedTextRenderer.js": {"language": "JavaScript", "code": 163, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/browser.js": {"language": "JavaScript", "code": 55, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/canIUse.js": {"language": "JavaScript", "code": 22, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/dom.js": {"language": "JavaScript", "code": 888, "comment": 107, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/contextmenu.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/markdownRenderer.js": {"language": "JavaScript", "code": 285, "comment": 41, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/history.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/globalMouseMoveMonitor.js": {"language": "JavaScript", "code": 87, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/dnd.js": {"language": "JavaScript", "code": 21, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/iframe.js": {"language": "JavaScript", "code": 79, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.css": {"language": "CSS", "code": 9, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.js": {"language": "JavaScript", "code": 88, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/quickInput.js": {"language": "JavaScript", "code": 160, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/widget.js": {"language": "JavaScript", "code": 37, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/verticalScrollbar.js": {"language": "JavaScript", "code": 85, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/sash/sash.css": {"language": "CSS", "code": 108, "comment": 5, "blank": 24}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/menu/menu.js": {"language": "JavaScript", "code": 1021, "comment": 57, "blank": 63}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/abstractScrollbar.js": {"language": "JavaScript", "code": 184, "comment": 20, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarArrow.js": {"language": "JavaScript", "code": 64, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElementOptions.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarVisibilityController.js": {"language": "JavaScript", "code": 86, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarState.js": {"language": "JavaScript", "code": 134, "comment": 27, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElement.js": {"language": "JavaScript", "code": 454, "comment": 57, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/sash/sash.js": {"language": "JavaScript", "code": 359, "comment": 71, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/horizontalScrollbar.js": {"language": "JavaScript", "code": 84, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.js": {"language": "JavaScript", "code": 517, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInputCheckboxes.js": {"language": "JavaScript", "code": 42, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/findinput/replaceInput.js": {"language": "JavaScript", "code": 241, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/media/scrollbars.css": {"language": "CSS", "code": 41, "comment": 7, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.css": {"language": "CSS", "code": 85, "comment": 8, "blank": 19}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.js": {"language": "JavaScript", "code": 298, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/listPaging.js": {"language": "JavaScript", "code": 113, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.css": {"language": "CSS", "code": 48, "comment": 9, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/aria/aria.css": {"language": "CSS", "code": 4, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/aria/aria.js": {"language": "JavaScript", "code": 75, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.css": {"language": "CSS", "code": 86, "comment": 6, "blank": 19}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.js": {"language": "JavaScript", "code": 366, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionViewItems.js": {"language": "JavaScript", "code": 271, "comment": 21, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/table/table.css": {"language": "CSS", "code": 44, "comment": 9, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/table/table.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/table/tableWidget.js": {"language": "JavaScript", "code": 161, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.css": {"language": "CSS", "code": 18, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.js": {"language": "JavaScript", "code": 54, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/button/button.css": {"language": "CSS", "code": 41, "comment": 4, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.js": {"language": "JavaScript", "code": 87, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/button/button.js": {"language": "JavaScript", "code": 145, "comment": 1, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.js": {"language": "JavaScript", "code": 255, "comment": 20, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.css": {"language": "CSS", "code": 30, "comment": 14, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.css": {"language": "CSS", "code": 12, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.js": {"language": "JavaScript", "code": 99, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/selections/selections.css": {"language": "CSS", "code": 11, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.css": {"language": "CSS", "code": 41, "comment": 5, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/selections/selections.js": {"language": "JavaScript", "code": 314, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/rowCache.js": {"language": "JavaScript", "code": 66, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/splice.js": {"language": "JavaScript", "code": 8, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/list.js": {"language": "JavaScript", "code": 5, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/listWidget.js": {"language": "JavaScript", "code": 1341, "comment": 58, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/list.css": {"language": "CSS", "code": 128, "comment": 9, "blank": 25}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/listView.js": {"language": "JavaScript", "code": 961, "comment": 40, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabelHover.js": {"language": "JavaScript", "code": 161, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/hover/hover.css": {"language": "CSS", "code": 120, "comment": 7, "blank": 28}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.js": {"language": "JavaScript", "code": 124, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.css": {"language": "CSS", "code": 27, "comment": 4, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/hover/hoverWidget.js": {"language": "JavaScript", "code": 55, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabel.js": {"language": "JavaScript", "code": 214, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconlabel.css": {"language": "CSS", "code": 76, "comment": 9, "blank": 20}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconHoverDelegate.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabels.js": {"language": "JavaScript", "code": 24, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/rangeMap.js": {"language": "JavaScript", "code": 108, "comment": 37, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/mouseCursor/mouseCursor.css": {"language": "CSS", "code": 7, "comment": 5, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/highlightedlabel/highlightedLabel.js": {"language": "JavaScript", "code": 86, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/splitview/splitview.css": {"language": "CSS", "code": 54, "comment": 4, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.css": {"language": "CSS", "code": 35, "comment": 4, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/splitview/splitview.js": {"language": "JavaScript", "code": 639, "comment": 109, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/mouseCursor/mouseCursor.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdownActionViewItem.js": {"language": "JavaScript", "code": 84, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.js": {"language": "JavaScript", "code": 135, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/dataTree.js": {"language": "JavaScript", "code": 13, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/objectTreeModel.js": {"language": "JavaScript", "code": 161, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/abstractTree.js": {"language": "JavaScript", "code": 1140, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/compressedObjectTreeModel.js": {"language": "JavaScript", "code": 337, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/tree.js": {"language": "JavaScript", "code": 25, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/indexTreeModel.js": {"language": "JavaScript", "code": 520, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/objectTree.js": {"language": "JavaScript", "code": 126, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/media/tree.css": {"language": "CSS", "code": 53, "comment": 5, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/quickAccess.js": {"language": "JavaScript", "code": 156, "comment": 35, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/helpQuickAccess.js": {"language": "JavaScript", "code": 72, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/asyncDataTree.js": {"language": "JavaScript", "code": 757, "comment": 21, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/pickerQuickAccess.js": {"language": "JavaScript", "code": 208, "comment": 42, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/commandsQuickAccess.js": {"language": "JavaScript", "code": 219, "comment": 18, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon/codicon.css": {"language": "CSS", "code": 17, "comment": 5, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon/codicon-modifiers.css": {"language": "CSS", "code": 22, "comment": 6, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/codicons/codiconStyles.js": {"language": "JavaScript", "code": 10, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.worker.js": {"language": "JavaScript", "code": 20, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.main.js": {"language": "JavaScript", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.api.d.ts": {"language": "TypeScript", "code": 2976, "comment": 4449, "blank": 359}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/layout/browser/layoutService.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.all.js": {"language": "JavaScript", "code": 49, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/workspace/common/workspace.js": {"language": "JavaScript", "code": 62, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/workspace/common/workspaceTrust.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/json/jsonMode.js": {"language": "JavaScript", "code": 9, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/instantiation.js": {"language": "JavaScript", "code": 34, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/edcore.main.js": {"language": "JavaScript", "code": 11, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/serviceCollection.js": {"language": "JavaScript", "code": 19, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/json/jsonWorker.js": {"language": "JavaScript", "code": 27, "comment": 6, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/descriptors.js": {"language": "JavaScript", "code": 7, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/graph.js": {"language": "JavaScript", "code": 79, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/extensions.js": {"language": "JavaScript", "code": 11, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/instantiationService.js": {"language": "JavaScript", "code": 263, "comment": 21, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/progress/common/progress.js": {"language": "JavaScript", "code": 17, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/notification/common/notification.js": {"language": "JavaScript", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/configuration/common/configuration.js": {"language": "JavaScript", "code": 75, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/configuration/common/configurationModels.js": {"language": "JavaScript", "code": 366, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/configuration/common/configurationRegistry.js": {"language": "JavaScript", "code": 279, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/typescript/tsMode.js": {"language": "JavaScript", "code": 11, "comment": 6, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.ru.js": {"language": "JavaScript", "code": 1549, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.css": {"language": "CSS", "code": 3183, "comment": 476, "blank": 740}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/html/htmlWorker.js": {"language": "JavaScript", "code": 257, "comment": 6, "blank": 191}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/html/htmlMode.js": {"language": "JavaScript", "code": 7, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/usLayoutResolvedKeybinding.js": {"language": "JavaScript", "code": 155, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/keybindingResolver.js": {"language": "JavaScript", "code": 223, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/keybinding.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/resolvedKeybindingItem.js": {"language": "JavaScript", "code": 28, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/keybindingsRegistry.js": {"language": "JavaScript", "code": 121, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/baseResolvedKeybinding.js": {"language": "JavaScript", "code": 43, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/css/cssMode.js": {"language": "JavaScript", "code": 7, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/symbolIcons/symbolIcons.js": {"language": "JavaScript", "code": 304, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchTypes.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/themes.js": {"language": "JavaScript", "code": 169, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchCommon.js": {"language": "JavaScript", "code": 87, "comment": 37, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchLexer.js": {"language": "JavaScript", "code": 680, "comment": 55, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/standaloneThemeService.js": {"language": "JavaScript", "code": 2, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/nls.js": {"language": "JavaScript", "code": 16, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/wordHighlighter/wordHighlighter.js": {"language": "JavaScript", "code": 519, "comment": 38, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/solidity/solidity.js": {"language": "JavaScript", "code": 1340, "comment": 28, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/markdown/markdown.js": {"language": "JavaScript", "code": 211, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/twig/twig.js": {"language": "JavaScript", "code": 332, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/csp/csp.js": {"language": "JavaScript", "code": 66, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/clipboard/clipboard.js": {"language": "JavaScript", "code": 219, "comment": 21, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/flow9/flow9.js": {"language": "JavaScript", "code": 135, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/indentation/indentation.js": {"language": "JavaScript", "code": 551, "comment": 25, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/indentation/indentUtils.js": {"language": "JavaScript", "code": 27, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/ecl/ecl.js": {"language": "JavaScript", "code": 448, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/abstractKeybindingService.js": {"language": "JavaScript", "code": 210, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/sql/sql.js": {"language": "JavaScript", "code": 827, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/protobuf/protobuf.js": {"language": "JavaScript", "code": 404, "comment": 34, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchCompile.js": {"language": "JavaScript", "code": 445, "comment": 70, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/goToCommands.js": {"language": "JavaScript", "code": 680, "comment": 28, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/css/cssWorker.js": {"language": "JavaScript", "code": 38, "comment": 6, "blank": 20}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/symbolNavigation.js": {"language": "JavaScript", "code": 170, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/goToSymbol.js": {"language": "JavaScript", "code": 82, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/msdax/msdax.js": {"language": "JavaScript", "code": 386, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/ruby/ruby.js": {"language": "JavaScript", "code": 453, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesTree.js": {"language": "JavaScript", "code": 184, "comment": 18, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/referencesModel.js": {"language": "JavaScript", "code": 243, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/javascript/javascript.js": {"language": "JavaScript", "code": 285, "comment": 146, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/bat/bat.js": {"language": "JavaScript", "code": 104, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/bicep/bicep.js": {"language": "JavaScript", "code": 97, "comment": 35, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/restructuredtext/restructuredtext.js": {"language": "JavaScript", "code": 171, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.js": {"language": "JavaScript", "code": 129481, "comment": 12497, "blank": 2992}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/typescript/tsWorker.js": {"language": "JavaScript", "code": 20714, "comment": 11099, "blank": 3510}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesWidget.js": {"language": "JavaScript", "code": 469, "comment": 25, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesController.js": {"language": "JavaScript", "code": 369, "comment": 22, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linkedEditing/linkedEditing.js": {"language": "JavaScript", "code": 353, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesWidget.css": {"language": "CSS", "code": 62, "comment": 6, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/julia/julia.js": {"language": "JavaScript", "code": 505, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/wordOperations/wordOperations.js": {"language": "JavaScript", "code": 435, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/quickAccess/gotoLineQuickAccess.js": {"language": "JavaScript", "code": 112, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/quickAccess/commandsQuickAccess.js": {"language": "JavaScript", "code": 22, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/quickAccess/gotoSymbolQuickAccess.js": {"language": "JavaScript", "code": 339, "comment": 51, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/quickAccess/editorNavigationQuickAccess.js": {"language": "JavaScript", "code": 112, "comment": 35, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/yaml/yaml.js": {"language": "JavaScript", "code": 177, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/m3/m3.js": {"language": "JavaScript", "code": 219, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unusualLineTerminators/unusualLineTerminators.js": {"language": "JavaScript", "code": 99, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/csharp/csharp.js": {"language": "JavaScript", "code": 296, "comment": 40, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/objective-c/objective-c.js": {"language": "JavaScript", "code": 197, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/clojure/clojure.js": {"language": "JavaScript", "code": 763, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/mysql/mysql.js": {"language": "JavaScript", "code": 884, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/html/html.js": {"language": "JavaScript", "code": 296, "comment": 10, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pgsql/pgsql.js": {"language": "JavaScript", "code": 852, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/coffee/coffee.js": {"language": "JavaScript", "code": 237, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/clickLinkGesture.js": {"language": "JavaScript", "code": 130, "comment": 14, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/replacePattern.js": {"language": "JavaScript", "code": 235, "comment": 51, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/qsharp/qsharp.js": {"language": "JavaScript", "code": 290, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/viewportSemanticTokens/viewportSemanticTokens.js": {"language": "JavaScript", "code": 112, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHints.css": {"language": "CSS", "code": 86, "comment": 4, "blank": 21}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHintsWidget.js": {"language": "JavaScript", "code": 354, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/replaceAllCommand.js": {"language": "JavaScript", "code": 43, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.js": {"language": "JavaScript", "code": 280, "comment": 21, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findDecorations.js": {"language": "JavaScript", "code": 275, "comment": 14, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.css": {"language": "CSS", "code": 4, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/provideSignatureHelp.js": {"language": "JavaScript", "code": 61, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHints.js": {"language": "JavaScript", "code": 106, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findWidget.css": {"language": "CSS", "code": 176, "comment": 12, "blank": 37}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHintsModel.js": {"language": "JavaScript", "code": 250, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findModel.js": {"language": "JavaScript", "code": 448, "comment": 30, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findState.js": {"language": "JavaScript", "code": 215, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findController.js": {"language": "JavaScript", "code": 869, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findOptionsWidget.js": {"language": "JavaScript", "code": 166, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findWidget.js": {"language": "JavaScript", "code": 1132, "comment": 57, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/zoneWidget/zoneWidget.css": {"language": "CSS", "code": 11, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/graphql/graphql.js": {"language": "JavaScript", "code": 150, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensController.js": {"language": "JavaScript", "code": 434, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace/inPlaceReplaceCommand.js": {"language": "JavaScript", "code": 19, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace/inPlaceReplace.js": {"language": "JavaScript", "code": 148, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/rename/rename.js": {"language": "JavaScript", "code": 341, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/zoneWidget/zoneWidget.js": {"language": "JavaScript", "code": 378, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/rename/renameInputField.js": {"language": "JavaScript", "code": 173, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/rename/renameInputField.css": {"language": "CSS", "code": 18, "comment": 4, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensWidget.css": {"language": "CSS", "code": 40, "comment": 4, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensWidget.js": {"language": "JavaScript", "code": 248, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codelens.js": {"language": "JavaScript", "code": 104, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codeLensCache.js": {"language": "JavaScript", "code": 100, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/swift/swift.js": {"language": "JavaScript", "code": 261, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/ini/ini.js": {"language": "JavaScript", "code": 77, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/scala/scala.js": {"language": "JavaScript", "code": 360, "comment": 9, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/hcl/hcl.js": {"language": "JavaScript", "code": 159, "comment": 35, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/color.js": {"language": "JavaScript", "code": 66, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/handlebars/handlebars.js": {"language": "JavaScript", "code": 420, "comment": 10, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlayHints/inlayHintsController.js": {"language": "JavaScript", "code": 305, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/cpp/cpp.js": {"language": "JavaScript", "code": 363, "comment": 40, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/tcl/tcl.js": {"language": "JavaScript", "code": 241, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/links/links.css": {"language": "CSS", "code": 8, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/links/getLinks.js": {"language": "JavaScript", "code": 146, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPickerWidget.js": {"language": "JavaScript", "code": 259, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorDetector.js": {"language": "JavaScript", "code": 196, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/links/links.js": {"language": "JavaScript", "code": 368, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorContributions.js": {"language": "JavaScript", "code": 36, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/consts.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPicker.css": {"language": "CSS", "code": 114, "comment": 7, "blank": 24}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant.js": {"language": "JavaScript", "code": 156, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPickerModel.js": {"language": "JavaScript", "code": 53, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextController.js": {"language": "JavaScript", "code": 269, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextModel.js": {"language": "JavaScript", "code": 152, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionsModel.js": {"language": "JavaScript", "code": 482, "comment": 28, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/utils.js": {"language": "JavaScript", "code": 21, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/suggestWidgetPreviewModel.js": {"language": "JavaScript", "code": 126, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextWidget.js": {"language": "JavaScript", "code": 320, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostText.css": {"language": "CSS", "code": 23, "comment": 4, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/suggestWidgetInlineCompletionProvider.js": {"language": "JavaScript", "code": 194, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/mips/mips.js": {"language": "JavaScript", "code": 203, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/lightBulbWidget.css": {"language": "CSS", "code": 10, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/lightBulbWidget.js": {"language": "JavaScript", "code": 202, "comment": 18, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText.js": {"language": "JavaScript", "code": 136, "comment": 32, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionModel.js": {"language": "JavaScript", "code": 223, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostText.js": {"language": "JavaScript", "code": 78, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/anchorSelect/anchorSelect.js": {"language": "JavaScript", "code": 173, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionUi.js": {"language": "JavaScript", "code": 155, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionMenu.js": {"language": "JavaScript", "code": 180, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/types.js": {"language": "JavaScript", "code": 103, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionCommands.js": {"language": "JavaScript", "code": 349, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/anchorSelect/anchorSelect.css": {"language": "CSS", "code": 4, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/powershell/powershell.js": {"language": "JavaScript", "code": 242, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionContributions.js": {"language": "JavaScript", "code": 10, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/vb/vb.js": {"language": "JavaScript", "code": 373, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pla/pla.js": {"language": "JavaScript", "code": 140, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/format/formatActions.js": {"language": "JavaScript", "code": 244, "comment": 21, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/tokenization/tokenization.js": {"language": "JavaScript", "code": 25, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeAction.js": {"language": "JavaScript", "code": 214, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pascal/pascal.js": {"language": "JavaScript", "code": 254, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/format/formattingEdit.js": {"language": "JavaScript", "code": 46, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/lineSelection/lineSelection.js": {"language": "JavaScript", "code": 30, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/format/format.js": {"language": "JavaScript", "code": 380, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pascaligo/pascaligo.js": {"language": "JavaScript", "code": 167, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/bracketMatching/bracketMatching.js": {"language": "JavaScript", "code": 308, "comment": 16, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/bracketMatching/bracketMatching.css": {"language": "CSS", "code": 3, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/scheme/scheme.js": {"language": "JavaScript", "code": 123, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/wordPartOperations/wordPartOperations.js": {"language": "JavaScript", "code": 136, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/lua/lua.js": {"language": "JavaScript", "code": 166, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/redshift/redshift.js": {"language": "JavaScript", "code": 815, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/php/php.js": {"language": "JavaScript", "code": 480, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/kotlin/kotlin.js": {"language": "JavaScript", "code": 231, "comment": 31, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/st/st.js": {"language": "JavaScript", "code": 420, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/xml/xml.js": {"language": "JavaScript", "code": 123, "comment": 10, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/message/messageController.js": {"language": "JavaScript", "code": 138, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/fontZoom/fontZoom.js": {"language": "JavaScript", "code": 45, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/abap/abap.js": {"language": "JavaScript", "code": 1317, "comment": 13, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/contextmenu/contextmenu.js": {"language": "JavaScript", "code": 224, "comment": 20, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/message/messageController.css": {"language": "CSS", "code": 55, "comment": 4, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/r/r.js": {"language": "JavaScript", "code": 254, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.fr.js": {"language": "JavaScript", "code": 20, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.js": {"language": "JavaScript", "code": 20, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetParser.js": {"language": "JavaScript", "code": 816, "comment": 52, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/bannerController.css": {"language": "CSS", "code": 66, "comment": 4, "blank": 16}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/bannerController.js": {"language": "JavaScript", "code": 106, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.es.js": {"language": "JavaScript", "code": 22, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetSession.js": {"language": "JavaScript", "code": 425, "comment": 86, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetSession.css": {"language": "CSS", "code": 13, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.css": {"language": "CSS", "code": 4, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.js": {"language": "JavaScript", "code": 575, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetController2.js": {"language": "JavaScript", "code": 210, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/liquid/liquid.js": {"language": "JavaScript", "code": 267, "comment": 10, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/java/java.js": {"language": "JavaScript", "code": 205, "comment": 33, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetVariables.js": {"language": "JavaScript", "code": 302, "comment": 18, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/fsharp/fsharp.js": {"language": "JavaScript", "code": 219, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pug/pug.js": {"language": "JavaScript", "code": 402, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.it.js": {"language": "JavaScript", "code": 20, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linesOperations/copyLinesCommand.js": {"language": "JavaScript", "code": 65, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/dnd/dragAndDropCommand.js": {"language": "JavaScript", "code": 53, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linesOperations/linesOperations.js": {"language": "JavaScript", "code": 967, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.de.js": {"language": "JavaScript", "code": 22, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/dnd/dnd.css": {"language": "CSS", "code": 22, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/dnd/dnd.js": {"language": "JavaScript", "code": 190, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.zh-tw.js": {"language": "JavaScript", "code": 20, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linesOperations/sortLinesCommand.js": {"language": "JavaScript", "code": 73, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/perl/perl.js": {"language": "JavaScript", "code": 608, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.ja.js": {"language": "JavaScript", "code": 22, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/caretOperations/transpose.js": {"language": "JavaScript", "code": 58, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/caretOperations/caretOperations.js": {"language": "JavaScript", "code": 45, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/caretOperations/moveCaretCommand.js": {"language": "JavaScript", "code": 42, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/typescript/typescript.js": {"language": "JavaScript", "code": 285, "comment": 80, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linesOperations/moveLinesCommand.js": {"language": "JavaScript", "code": 298, "comment": 38, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/sophia/sophia.js": {"language": "JavaScript", "code": 179, "comment": 28, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.zh-cn.js": {"language": "JavaScript", "code": 22, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/comment/blockCommentCommand.js": {"language": "JavaScript", "code": 128, "comment": 18, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/comment/lineCommentCommand.js": {"language": "JavaScript", "code": 279, "comment": 44, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.ko.js": {"language": "JavaScript", "code": 20, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/comment/comment.js": {"language": "JavaScript", "code": 135, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/scss/scss.js": {"language": "JavaScript", "code": 251, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.css": {"language": "CSS", "code": 1, "comment": 5, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/peekView/peekView.js": {"language": "JavaScript", "code": 228, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.js": {"language": "JavaScript", "code": 33, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/peekView/media/peekViewWidget.css": {"language": "CSS", "code": 55, "comment": 4, "blank": 15}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/shell/shell.js": {"language": "JavaScript", "code": 230, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.ru.js": {"language": "JavaScript", "code": 22, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoError/gotoErrorWidget.js": {"language": "JavaScript", "code": 331, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoError/markerNavigationService.js": {"language": "JavaScript", "code": 184, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoError/gotoError.js": {"language": "JavaScript", "code": 273, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoError/media/gotoErrorWidget.css": {"language": "CSS", "code": 58, "comment": 6, "blank": 16}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/base/worker/workerMain.js": {"language": "JavaScript", "code": 12, "comment": 8, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/dart/dart.js": {"language": "JavaScript", "code": 219, "comment": 68, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/documentSymbols/outlineModel.js": {"language": "JavaScript", "code": 235, "comment": 13, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/smartSelect/bracketSelections.js": {"language": "JavaScript", "code": 140, "comment": 15, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/multicursor/multicursor.js": {"language": "JavaScript", "code": 900, "comment": 39, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/documentSymbols/documentSymbols.js": {"language": "JavaScript", "code": 41, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/getHover.js": {"language": "JavaScript", "code": 49, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/smartSelect/wordSelections.js": {"language": "JavaScript", "code": 64, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/dockerfile/dockerfile.js": {"language": "JavaScript", "code": 141, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/smartSelect/smartSelect.js": {"language": "JavaScript", "code": 254, "comment": 21, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/hoverOperation.js": {"language": "JavaScript", "code": 158, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/colorHoverParticipant.js": {"language": "JavaScript", "code": 146, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/hover.js": {"language": "JavaScript", "code": 286, "comment": 12, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/hoverTypes.js": {"language": "JavaScript", "code": 27, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/markdownHoverParticipant.js": {"language": "JavaScript", "code": 127, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/markerHoverParticipant.js": {"language": "JavaScript", "code": 224, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/modesGlyphHover.js": {"language": "JavaScript", "code": 163, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetStatus.js": {"language": "JavaScript", "code": 82, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/modesContentHover.js": {"language": "JavaScript", "code": 467, "comment": 24, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestAlternatives.js": {"language": "JavaScript", "code": 85, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/wordDistance.js": {"language": "JavaScript", "code": 67, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/less/less.js": {"language": "JavaScript", "code": 176, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/redis/redis.js": {"language": "JavaScript", "code": 307, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetRenderer.js": {"language": "JavaScript", "code": 204, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/wordContextKey.js": {"language": "JavaScript", "code": 60, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestCommitCharacters.js": {"language": "JavaScript", "code": 41, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/elixir/elixir.js": {"language": "JavaScript", "code": 476, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestModel.js": {"language": "JavaScript", "code": 572, "comment": 69, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestOvertypingCapturer.js": {"language": "JavaScript", "code": 53, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestController.js": {"language": "JavaScript", "code": 773, "comment": 48, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetDetails.js": {"language": "JavaScript", "code": 367, "comment": 11, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestMemory.js": {"language": "JavaScript", "code": 236, "comment": 14, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidget.js": {"language": "JavaScript", "code": 777, "comment": 33, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/completionModel.js": {"language": "JavaScript", "code": 197, "comment": 37, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/media/suggest.css": {"language": "CSS", "code": 337, "comment": 20, "blank": 97}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/resizable.js": {"language": "JavaScript", "code": 143, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggest.js": {"language": "JavaScript", "code": 294, "comment": 27, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/css/css.js": {"language": "JavaScript", "code": 193, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/json/jsonWorker.js": {"language": "JavaScript", "code": 7231, "comment": 34, "blank": 29}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/html/htmlMode.js": {"language": "JavaScript", "code": 1862, "comment": 13, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/typescript/tsMode.js": {"language": "JavaScript", "code": 1317, "comment": 15, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/json/jsonMode.js": {"language": "JavaScript", "code": 2327, "comment": 193, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/python/python.js": {"language": "JavaScript", "code": 284, "comment": 10, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/sparql/sparql.js": {"language": "JavaScript", "code": 202, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/systemverilog/systemverilog.js": {"language": "JavaScript", "code": 517, "comment": 56, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/azcli/azcli.js": {"language": "JavaScript", "code": 83, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/apex/apex.js": {"language": "JavaScript", "code": 300, "comment": 36, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/go/go.js": {"language": "JavaScript", "code": 195, "comment": 30, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/powerquery/powerquery.js": {"language": "JavaScript", "code": 899, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/razor/razor.js": {"language": "JavaScript", "code": 541, "comment": 10, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/rust/rust.js": {"language": "JavaScript", "code": 312, "comment": 42, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/css/cssMode.js": {"language": "JavaScript", "code": 1954, "comment": 13, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/sb/sb.js": {"language": "JavaScript", "code": 119, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch.js": {"language": "JavaScript", "code": 32, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/cameligo/cameligo.js": {"language": "JavaScript", "code": 177, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/postiats/postiats.js": {"language": "JavaScript", "code": 548, "comment": 12, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/json/json.worker.js": {"language": "JavaScript", "code": 7218, "comment": 35, "blank": 30}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/json/monaco.contribution.js": {"language": "JavaScript", "code": 91, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/json/jsonMode.js": {"language": "JavaScript", "code": 2303, "comment": 191, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneLanguages.js": {"language": "JavaScript", "code": 334, "comment": 125, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/html/monaco.contribution.js": {"language": "JavaScript", "code": 132, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneThemeServiceImpl.js": {"language": "JavaScript", "code": 306, "comment": 8, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/html/htmlMode.js": {"language": "JavaScript", "code": 1838, "comment": 11, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneCodeEditor.js": {"language": "JavaScript", "code": 304, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/simpleServices.js": {"language": "JavaScript", "code": 461, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneServices.js": {"language": "JavaScript", "code": 180, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneCodeServiceImpl.js": {"language": "JavaScript", "code": 86, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/typescript/monaco.contribution.js": {"language": "JavaScript", "code": 239, "comment": 9, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/typescript/tsMode.js": {"language": "JavaScript", "code": 1286, "comment": 13, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standalone-tokens.css": {"language": "CSS", "code": 190, "comment": 36, "blank": 24}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/cursorUndo/cursorUndo.js": {"language": "JavaScript", "code": 127, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/css/cssMode.js": {"language": "JavaScript", "code": 1930, "comment": 11, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/folding.css": {"language": "CSS", "code": 24, "comment": 4, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/lexon/lexon.js": {"language": "JavaScript", "code": 160, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneEditor.js": {"language": "JavaScript", "code": 215, "comment": 95, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/colorizer.js": {"language": "JavaScript", "code": 150, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/hiddenRangeModel.js": {"language": "JavaScript", "code": 134, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/syntaxRangeProvider.js": {"language": "JavaScript", "code": 162, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/foldingRanges.js": {"language": "JavaScript", "code": 163, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/indentRangeProvider.js": {"language": "JavaScript", "code": 142, "comment": 17, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/css/monaco.contribution.js": {"language": "JavaScript", "code": 116, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/intializingRangeProvider.js": {"language": "JavaScript", "code": 48, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/foldingModel.js": {"language": "JavaScript", "code": 426, "comment": 86, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/folding.js": {"language": "JavaScript", "code": 948, "comment": 22, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/foldingDecorations.js": {"language": "JavaScript", "code": 65, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/Subscribe/index.tsx": {"language": "TypeScript React", "code": 117, "comment": 10, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast.js": {"language": "JavaScript", "code": 26, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.css": {"language": "CSS", "code": 18, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.js": {"language": "JavaScript", "code": 66, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/StatisticsReport/index.tsx": {"language": "TypeScript React", "code": 158, "comment": 10, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/route.tsx": {"language": "TypeScript React", "code": 54, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/html/htmlWorker.js": {"language": "JavaScript", "code": 16042, "comment": 40, "blank": 35}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/html/html.worker.js": {"language": "JavaScript", "code": 16029, "comment": 41, "blank": 36}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens/inspectTokens.js": {"language": "JavaScript", "code": 249, "comment": 5, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens/inspectTokens.css": {"language": "CSS", "code": 29, "comment": 4, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/index.tsx": {"language": "TypeScript React", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.js": {"language": "JavaScript", "code": 302, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/route.tsx": {"language": "TypeScript React", "code": 38, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/SubscribeReport/styles.module.less": {"language": "Less", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/SubscribeReport/index.tsx": {"language": "TypeScript React", "code": 195, "comment": 42, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/monaco.d.ts": {"language": "TypeScript", "code": 2976, "comment": 4449, "blank": 363}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.css": {"language": "CSS", "code": 5, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/Login/styles.module.less": {"language": "Less", "code": 61, "comment": 1, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/Login/index.tsx": {"language": "TypeScript React", "code": 95, "comment": 0, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/sb/sb.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/CHANGELOG.md": {"language": "<PERSON><PERSON>", "code": 954, "comment": 0, "blank": 401}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/index.tsx": {"language": "TypeScript React", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess.js": {"language": "JavaScript", "code": 58, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess.js": {"language": "JavaScript", "code": 9, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess.js": {"language": "JavaScript", "code": 81, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInput.css": {"language": "CSS", "code": 39, "comment": 4, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/flow9/flow9.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sql/sql.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess.js": {"language": "JavaScript", "code": 66, "comment": 4, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInputServiceImpl.js": {"language": "JavaScript", "code": 114, "comment": 7, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sql/sql.js": {"language": "JavaScript", "code": 813, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sparql/sparql.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/ruby/ruby.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/flow9/flow9.js": {"language": "JavaScript", "code": 121, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/flow9/flow9.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/ecl/ecl.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/sql/sql.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sparql/sparql.js": {"language": "JavaScript", "code": 188, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/css/cssWorker.js": {"language": "JavaScript", "code": 35437, "comment": 50, "blank": 45}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/css/css.worker.js": {"language": "JavaScript", "code": 35424, "comment": 51, "blank": 46}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.js": {"language": "JavaScript", "code": 663, "comment": 57, "blank": 87}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sophia/sophia.js": {"language": "JavaScript", "code": 165, "comment": 28, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sophia/sophia.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/MsgBodyManag/index.tsx": {"language": "TypeScript React", "code": 192, "comment": 19, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/MsgCovertDetail/index.tsx": {"language": "TypeScript React", "code": 81, "comment": 2, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/List/index.tsx": {"language": "TypeScript React", "code": 114, "comment": 10, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/MsgDetail/index.tsx": {"language": "TypeScript React", "code": 117, "comment": 8, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/objective-c/objective-c.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ruby/ruby.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ecl/ecl.js": {"language": "JavaScript", "code": 434, "comment": 23, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ruby/ruby.js": {"language": "JavaScript", "code": 439, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Service/index.tsx": {"language": "TypeScript React", "code": 140, "comment": 30, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js": {"language": "JavaScript", "code": 390, "comment": 34, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ecl/ecl.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/index.ts": {"language": "TypeScript", "code": 46, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js": {"language": "JavaScript", "code": 183, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/SelectPath/index.tsx": {"language": "TypeScript React", "code": 32, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useNodeClick.ts": {"language": "TypeScript", "code": 40, "comment": 1, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AddConfigService/index.tsx": {"language": "TypeScript React", "code": 210, "comment": 3, "blank": 14}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/typescript/tsWorker.js": {"language": "JavaScript", "code": 142897, "comment": 15165, "blank": 2321}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/typescript/ts.worker.js": {"language": "JavaScript", "code": 142897, "comment": 15166, "blank": 2323}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useDnd.ts": {"language": "TypeScript", "code": 178, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useBlankClick.ts": {"language": "TypeScript", "code": 27, "comment": 1, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useGeneral.ts": {"language": "TypeScript", "code": 28, "comment": 1, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useNode.ts": {"language": "TypeScript", "code": 127, "comment": 10, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useEdgeClick.ts": {"language": "TypeScript", "code": 33, "comment": 1, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/usePortClick.ts": {"language": "TypeScript", "code": 56, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useProcessInfo.ts": {"language": "TypeScript", "code": 57, "comment": 5, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useMouseDown.ts": {"language": "TypeScript", "code": 18, "comment": 1, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useGraph.ts": {"language": "TypeScript", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useGroupList.ts": {"language": "TypeScript", "code": 78, "comment": 6, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useMouseMove.ts": {"language": "TypeScript", "code": 57, "comment": 1, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/usePreferences.ts": {"language": "TypeScript", "code": 25, "comment": 1, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/index.ts": {"language": "TypeScript", "code": 18, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useTableAssign.tsx": {"language": "TypeScript React", "code": 67, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useEdge.ts": {"language": "TypeScript", "code": 73, "comment": 4, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useTableEdit.tsx": {"language": "TypeScript React", "code": 48, "comment": 1, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useDown.ts": {"language": "TypeScript", "code": 71, "comment": 1, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/PathManagement/index.tsx": {"language": "TypeScript React", "code": 93, "comment": 15, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/SelectMsg/index.tsx": {"language": "TypeScript React", "code": 86, "comment": 0, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/JsonCovertSchema/index.tsx": {"language": "TypeScript React", "code": 102, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditMsgBody/index.tsx": {"language": "TypeScript React", "code": 158, "comment": 4, "blank": 15}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useEdgeConnected.ts": {"language": "TypeScript", "code": 48, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AddService/index.tsx": {"language": "TypeScript React", "code": 76, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/index.tsx": {"language": "TypeScript React", "code": 34, "comment": 21, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/useAssignLinkTable.tsx": {"language": "TypeScript React", "code": 72, "comment": 12, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/utils.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/useAssignLinkSvg.tsx": {"language": "TypeScript React", "code": 741, "comment": 40, "blank": 38}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/useSelectedForm.tsx": {"language": "TypeScript React", "code": 273, "comment": 46, "blank": 15}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/SelectTransform/index.tsx": {"language": "TypeScript React", "code": 84, "comment": 1, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditContext/index.tsx": {"language": "TypeScript React", "code": 117, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/index.less": {"language": "Less", "code": 113, "comment": 0, "blank": 19}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Start/index.tsx": {"language": "TypeScript React", "code": 29, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/protobuf/protobuf.js": {"language": "JavaScript", "code": 5, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/TestService/index.tsx": {"language": "TypeScript React", "code": 169, "comment": 125, "blank": 14}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditAssignParams/index.tsx": {"language": "TypeScript React", "code": 80, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Context.tsx": {"language": "TypeScript React", "code": 272, "comment": 8, "blank": 14}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Modal.tsx": {"language": "TypeScript React", "code": 41, "comment": 3, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Preferences.tsx": {"language": "TypeScript React", "code": 52, "comment": 8, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/General.tsx": {"language": "TypeScript React", "code": 63, "comment": 3, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Tools/index.less": {"language": "Less", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Tools/index.tsx": {"language": "TypeScript React", "code": 398, "comment": 11, "blank": 28}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Activity.tsx": {"language": "TypeScript React", "code": 134, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Start/index.less": {"language": "Less", "code": 21, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/hooks.tsx": {"language": "TypeScript React", "code": 109, "comment": 0, "blank": 14}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/index.tsx": {"language": "TypeScript React", "code": 64, "comment": 3, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/DragShape/index.less": {"language": "Less", "code": 21, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditProject/index.tsx": {"language": "TypeScript React", "code": 102, "comment": 3, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditMsgCovert/index.tsx": {"language": "TypeScript React", "code": 163, "comment": 3, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/DragShape/index.tsx": {"language": "TypeScript React", "code": 32, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AddDataSource/index.tsx": {"language": "TypeScript React", "code": 134, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditAssignKeyValue/index.tsx": {"language": "TypeScript React", "code": 64, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/monaco.contribution.js": {"language": "JavaScript", "code": 77, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/twig/twig.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/PathManagement/index.tsx": {"language": "TypeScript React", "code": 191, "comment": 7, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/restructuredtext/restructuredtext.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/CopyFlow/index.tsx": {"language": "TypeScript React", "code": 55, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/msdax/msdax.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/twig/twig.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/route.tsx": {"language": "TypeScript React", "code": 86, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js": {"language": "JavaScript", "code": 157, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/msdax/msdax.js": {"language": "JavaScript", "code": 372, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/twig/twig.js": {"language": "JavaScript", "code": 318, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/msdax/msdax.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Project/index.tsx": {"language": "TypeScript React", "code": 117, "comment": 11, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/bicep/bicep.js": {"language": "JavaScript", "code": 3, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/bat/bat.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/javascript/javascript.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Edit/index.less": {"language": "Less", "code": 24, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/julia/julia.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Edit/index.tsx": {"language": "TypeScript React", "code": 149, "comment": 7, "blank": 14}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/MessageConversion/index.tsx": {"language": "TypeScript React", "code": 200, "comment": 24, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/javascript/javascript.js": {"language": "JavaScript", "code": 69, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution.js": {"language": "JavaScript", "code": 18, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/julia/julia.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/csharp/csharp.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/bat/bat.js": {"language": "JavaScript", "code": 90, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/bat/bat.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useActivityModel.ts": {"language": "TypeScript", "code": 87, "comment": 5, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/bicep/bicep.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/bicep/bicep.js": {"language": "JavaScript", "code": 83, "comment": 35, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/julia/julia.js": {"language": "JavaScript", "code": 491, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/m3/m3.js": {"language": "JavaScript", "code": 205, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/m3/m3.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/validator.ts": {"language": "TypeScript", "code": 16, "comment": 1, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Code.tsx": {"language": "TypeScript React", "code": 51, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/event.ts": {"language": "TypeScript", "code": 34, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/groupNode.ts": {"language": "TypeScript", "code": 12, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Compensate.tsx": {"language": "TypeScript React", "code": 65, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/edge.ts": {"language": "TypeScript", "code": 146, "comment": 11, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/index.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Catch.tsx": {"language": "TypeScript React", "code": 66, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/node.ts": {"language": "TypeScript", "code": 405, "comment": 12, "blank": 42}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/csharp/csharp.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Edge.tsx": {"language": "TypeScript React", "code": 73, "comment": 1, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Milestone.tsx": {"language": "TypeScript React", "code": 64, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Join.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/connector.ts": {"language": "TypeScript", "code": 283, "comment": 58, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/yaml/yaml.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Rule.tsx": {"language": "TypeScript React", "code": 77, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Continue.tsx": {"language": "TypeScript React", "code": 39, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/CacheDBCall.tsx": {"language": "TypeScript React", "code": 23, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/index.tsx": {"language": "TypeScript React", "code": 35, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/edgeKeyFeames.less": {"language": "Less", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Reply.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/const.ts": {"language": "TypeScript", "code": 76, "comment": 3, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/HttpCall.tsx": {"language": "TypeScript React", "code": 419, "comment": 11, "blank": 28}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/GenCall.tsx": {"language": "TypeScript React", "code": 448, "comment": 15, "blank": 32}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/yaml/yaml.js": {"language": "JavaScript", "code": 163, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/csharp/csharp.js": {"language": "JavaScript", "code": 282, "comment": 40, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/yaml/yaml.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/csp/csp.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pgsql/pgsql.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/mysql/mysql.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/mysql/mysql.js": {"language": "JavaScript", "code": 870, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.js": {"language": "JavaScript", "code": 838, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js": {"language": "JavaScript", "code": 276, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/mysql/mysql.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Xslt.tsx": {"language": "TypeScript React", "code": 129, "comment": 1, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/graphql/graphql.js": {"language": "JavaScript", "code": 136, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/swift/swift.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/graphql/graphql.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Delay.tsx": {"language": "TypeScript React", "code": 64, "comment": 1, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/swift/swift.js": {"language": "JavaScript", "code": 247, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/qsharp/qsharp.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/GenCallTypeEnum.tsx": {"language": "TypeScript React", "code": 16, "comment": 3, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/m3/m3.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Empty.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/coffee/coffee.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Label.tsx": {"language": "TypeScript React", "code": 39, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/swift/swift.js": {"language": "JavaScript", "code": 4, "comment": 9, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/End.tsx": {"language": "TypeScript React", "code": 39, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Xpath.tsx": {"language": "TypeScript React", "code": 72, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Throw.tsx": {"language": "TypeScript React", "code": 65, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/cpp/cpp.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/graphql/graphql.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/csp/csp.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/ForEach.tsx": {"language": "TypeScript React", "code": 69, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Transform.tsx": {"language": "TypeScript React", "code": 90, "comment": 4, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/JdbcCall.tsx": {"language": "TypeScript React", "code": 236, "comment": 9, "blank": 20}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/hcl/hcl.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/scala/scala.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/csp/csp.js": {"language": "JavaScript", "code": 52, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Switch.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Sql.tsx": {"language": "TypeScript React", "code": 51, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Start.tsx": {"language": "TypeScript React", "code": 39, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Branch.tsx": {"language": "TypeScript React", "code": 57, "comment": 16, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Break.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Trace.tsx": {"language": "TypeScript React", "code": 58, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/CatchAll.tsx": {"language": "TypeScript React", "code": 41, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/If.tsx": {"language": "TypeScript React", "code": 57, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Sequence.tsx": {"language": "TypeScript React", "code": 41, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Assign.tsx": {"language": "TypeScript React", "code": 78, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Compensationhandler.tsx": {"language": "TypeScript React", "code": 41, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Sync.tsx": {"language": "TypeScript React", "code": 73, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/coffee/coffee.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/coffee/coffee.js": {"language": "JavaScript", "code": 223, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ini/ini.js": {"language": "JavaScript", "code": 63, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/hcl/hcl.js": {"language": "JavaScript", "code": 145, "comment": 35, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/While.tsx": {"language": "TypeScript React", "code": 66, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Until.tsx": {"language": "TypeScript React", "code": 66, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/hcl/hcl.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ini/ini.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/join.tsx": {"language": "TypeScript React", "code": 29, "comment": 2, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/connector.tsx": {"language": "TypeScript React", "code": 42, "comment": 29, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/commonGroupShape.tsx": {"language": "TypeScript React", "code": 93, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/index.tsx": {"language": "TypeScript React", "code": 12, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/title.tsx": {"language": "TypeScript React", "code": 71, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js": {"language": "JavaScript", "code": 396, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/subTitle.tsx": {"language": "TypeScript React", "code": 47, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/end.tsx": {"language": "TypeScript React", "code": 41, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/cpp/cpp.js": {"language": "JavaScript", "code": 349, "comment": 40, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/start.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/cpp/cpp.contribution.js": {"language": "JavaScript", "code": 29, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scala/scala.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/tcl/tcl.js": {"language": "JavaScript", "code": 227, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/tcl/tcl.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/branch.tsx": {"language": "TypeScript React", "code": 44, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/if.tsx": {"language": "TypeScript React", "code": 44, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/switch.tsx": {"language": "TypeScript React", "code": 44, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/label.tsx": {"language": "TypeScript React", "code": 44, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/commonShape.tsx": {"language": "TypeScript React", "code": 62, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/mock.ts": {"language": "TypeScript", "code": 929, "comment": 43, "blank": 34}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scala/scala.js": {"language": "JavaScript", "code": 346, "comment": 9, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pla/pla.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/powershell/powershell.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/DataSource/index.tsx": {"language": "TypeScript React", "code": 101, "comment": 8, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/handlebars/handlebars.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/ini/ini.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/vb/vb.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/redshift/redshift.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/index.tsx": {"language": "TypeScript React", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/mips/mips.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/tcl/tcl.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/st/st.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/solidity/solidity.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/mips/mips.js": {"language": "JavaScript", "code": 189, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/vb/vb.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/vb/vb.js": {"language": "JavaScript", "code": 359, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/mips/mips.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pla/pla.js": {"language": "JavaScript", "code": 126, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pla/pla.contribution.js": {"language": "JavaScript", "code": 14, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/powershell/powershell.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/st/st.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/xml/xml.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/powershell/powershell.js": {"language": "JavaScript", "code": 228, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/xml/xml.contribution.js": {"language": "JavaScript", "code": 31, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/redshift/redshift.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pascaligo/pascaligo.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pascaligo/pascaligo.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/lua/lua.js": {"language": "JavaScript", "code": 152, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/st/st.js": {"language": "JavaScript", "code": 406, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/solidity/solidity.js": {"language": "JavaScript", "code": 1326, "comment": 28, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/lua/lua.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/xml/xml.js": {"language": "JavaScript", "code": 99, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pascaligo/pascaligo.js": {"language": "JavaScript", "code": 153, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/solidity/solidity.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/redshift/redshift.js": {"language": "JavaScript", "code": 801, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/kotlin/kotlin.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/php/php.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/lua/lua.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/abap/abap.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/java/java.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/liquid/liquid.js": {"language": "JavaScript", "code": 243, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/html/html.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/java/java.js": {"language": "JavaScript", "code": 191, "comment": 33, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/java/java.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/abap/abap.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/liquid/liquid.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/abap/abap.js": {"language": "JavaScript", "code": 1303, "comment": 13, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/liquid/liquid.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/fsharp/fsharp.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/fsharp/fsharp.js": {"language": "JavaScript", "code": 205, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/r/r.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/r/r.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pug/pug.js": {"language": "JavaScript", "code": 388, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pug/pug.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pug/pug.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/rust/rust.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/rust/rust.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/rust/rust.js": {"language": "JavaScript", "code": 298, "comment": 42, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/typescript/typescript.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/typescript/typescript.js": {"language": "JavaScript", "code": 261, "comment": 78, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/perl/perl.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/html/html.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/perl/perl.js": {"language": "JavaScript", "code": 594, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/fsharp/fsharp.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/php/php.js": {"language": "JavaScript", "code": 466, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scss/scss.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scss/scss.js": {"language": "JavaScript", "code": 237, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/html/html.js": {"language": "JavaScript", "code": 272, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/php/php.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/kotlin/kotlin.js": {"language": "JavaScript", "code": 217, "comment": 31, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/perl/perl.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/kotlin/kotlin.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/sophia/sophia.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/typescript/typescript.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/scss/scss.js": {"language": "JavaScript", "code": 6, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/_.contribution.js": {"language": "JavaScript", "code": 64, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/markdown/markdown.js": {"language": "JavaScript", "code": 197, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/markdown/markdown.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/components/charts/options.ts": {"language": "TypeScript", "code": 212, "comment": 6, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/components/charts/styles.module.less": {"language": "Less", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/components/charts/index.tsx": {"language": "TypeScript React", "code": 33, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/dart/dart.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/shell/shell.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/shell/shell.js": {"language": "JavaScript", "code": 216, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/shell/shell.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/dockerfile/dockerfile.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.js": {"language": "JavaScript", "code": 127, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/lexon/lexon.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/lexon/lexon.js": {"language": "JavaScript", "code": 146, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/lexon/lexon.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/dart/dart.js": {"language": "JavaScript", "code": 205, "comment": 68, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/dart/dart.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/redis/redis.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/elixir/elixir.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/redis/redis.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/elixir/elixir.js": {"language": "JavaScript", "code": 462, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/elixir/elixir.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/less/less.js": {"language": "JavaScript", "code": 5, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/clojure/clojure.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/redis/redis.js": {"language": "JavaScript", "code": 293, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/clojure/clojure.js": {"language": "JavaScript", "code": 749, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/css/css.js": {"language": "JavaScript", "code": 6, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/r/r.js": {"language": "JavaScript", "code": 240, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/clojure/clojure.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/css/css.js": {"language": "JavaScript", "code": 179, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/css/css.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/less/less.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/python/python.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/python/python.js": {"language": "JavaScript", "code": 260, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/less/less.js": {"language": "JavaScript", "code": 162, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/systemverilog/systemverilog.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/azcli/azcli.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js": {"language": "JavaScript", "code": 503, "comment": 56, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/python/python.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/azcli/azcli.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/go/go.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.contribution.js": {"language": "JavaScript", "code": 29, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/sparql/sparql.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/azcli/azcli.js": {"language": "JavaScript", "code": 69, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/markdown/markdown.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/components/queryFilter/index.tsx": {"language": "TypeScript React", "code": 51, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/EditCra/index.tsx": {"language": "TypeScript React", "code": 147, "comment": 11, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/powerquery/powerquery.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/razor/razor.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/apex/apex.js": {"language": "JavaScript", "code": 2, "comment": 9, "blank": 0}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/Audit/index.tsx": {"language": "TypeScript React", "code": 86, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/razor/razor.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/powerquery/powerquery.js": {"language": "JavaScript", "code": 885, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/razor/razor.js": {"language": "JavaScript", "code": 517, "comment": 8, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/powerquery/powerquery.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/Subscription/index.tsx": {"language": "TypeScript React", "code": 141, "comment": 3, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/cameligo/cameligo.js": {"language": "JavaScript", "code": 163, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/index.tsx": {"language": "TypeScript React", "code": 6, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/cameligo/cameligo.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pascal/pascal.js": {"language": "JavaScript", "code": 240, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pascal/pascal.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/CraManagement/index.tsx": {"language": "TypeScript React", "code": 217, "comment": 13, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/CraStepTwoForm/index.tsx": {"language": "TypeScript React", "code": 109, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/CreateSub/index.tsx": {"language": "TypeScript React", "code": 72, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/SubscriptionColumns/index.tsx": {"language": "TypeScript React", "code": 154, "comment": 14, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/go/go.js": {"language": "JavaScript", "code": 181, "comment": 30, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/go/go.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/scheme/scheme.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scheme/scheme.js": {"language": "JavaScript", "code": 109, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pascal/pascal.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/postiats/postiats.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/cameligo/cameligo.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scheme/scheme.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useMount.tsx": {"language": "TypeScript React", "code": 8, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useQuery.ts": {"language": "TypeScript", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useUnmount.tsx": {"language": "TypeScript React", "code": 10, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useHistoryCache.tsx": {"language": "TypeScript React", "code": 22, "comment": 22, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useHistory.tsx": {"language": "TypeScript React", "code": 12, "comment": 1, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useModal.tsx": {"language": "TypeScript React", "code": 10, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sb/sb.js": {"language": "JavaScript", "code": 105, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sb/sb.contribution.js": {"language": "JavaScript", "code": 15, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/index.html": {"language": "HTML", "code": 13, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/postiats/postiats.js": {"language": "JavaScript", "code": 4, "comment": 6, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/index.tsx": {"language": "TypeScript React", "code": 11, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/hooks/useTable.tsx": {"language": "TypeScript React", "code": 81, "comment": 4, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/apex/apex.contribution.js": {"language": "JavaScript", "code": 16, "comment": 7, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/route.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/postiats/postiats.js": {"language": "JavaScript", "code": 534, "comment": 12, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/AuthStrategyDetail/index.tsx": {"language": "TypeScript React", "code": 136, "comment": 11, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/AuthProvider/index.tsx": {"language": "TypeScript React", "code": 42, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/utils/interfaceTable.ts": {"language": "TypeScript", "code": 75, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/DebugApi/index.tsx": {"language": "TypeScript React", "code": 453, "comment": 27, "blank": 30}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/RequireData/index.tsx": {"language": "TypeScript React", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/route.tsx": {"language": "TypeScript React", "code": 20, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceGroupManagement/index.tsx": {"language": "TypeScript React", "code": 206, "comment": 4, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditUserGroup/index.tsx": {"language": "TypeScript React", "code": 129, "comment": 7, "blank": 14}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/EditInterfaceGroup/index.tsx": {"language": "TypeScript React", "code": 105, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ImportSwagger/index.tsx": {"language": "TypeScript React", "code": 96, "comment": 12, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceGroupDetails/columns.tsx": {"language": "TypeScript React", "code": 124, "comment": 3, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceGroupDetails/index.tsx": {"language": "TypeScript React", "code": 158, "comment": 4, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ImportExcel/index.tsx": {"language": "TypeScript React", "code": 103, "comment": 12, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ServiceAddress/index.tsx": {"language": "TypeScript React", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/EditGateWay/index.tsx": {"language": "TypeScript React", "code": 30, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/NewGateWay/index.tsx": {"language": "TypeScript React", "code": 73, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditUser/index.tsx": {"language": "TypeScript React", "code": 175, "comment": 10, "blank": 15}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/index.tsx": {"language": "TypeScript React", "code": 285, "comment": 28, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ApiSubCert/index.tsx": {"language": "TypeScript React", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/columns.ts": {"language": "TypeScript", "code": 22, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/index1.tsx": {"language": "TypeScript React", "code": 49, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/AuthStrategy.tsx": {"language": "TypeScript React", "code": 184, "comment": 7, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/TelescopicCapacity/index.tsx": {"language": "TypeScript React", "code": 211, "comment": 4, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/AccessStrategy.tsx": {"language": "TypeScript React", "code": 183, "comment": 7, "blank": 16}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/NewPackage/index.tsx": {"language": "TypeScript React", "code": 267, "comment": 4, "blank": 19}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/TelescopicCapacity/index.less": {"language": "Less", "code": 9, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/index.tsx": {"language": "TypeScript React", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddApi/index.less": {"language": "Less", "code": 9, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/AddInterface/index.less": {"language": "Less", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/AddInterface/index.tsx": {"language": "TypeScript React", "code": 139, "comment": 4, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddApi/index.tsx": {"language": "TypeScript React", "code": 264, "comment": 25, "blank": 18}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddFlowControl/index.tsx": {"language": "TypeScript React", "code": 359, "comment": 0, "blank": 18}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/Audit/index.tsx": {"language": "TypeScript React", "code": 86, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditHospital/index.tsx": {"language": "TypeScript React", "code": 122, "comment": 7, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/LogDetail/index.tsx": {"language": "TypeScript React", "code": 46, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/LogDetail/ProcessCall.tsx": {"language": "TypeScript React", "code": 33, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddAuthStrategy/index.tsx": {"language": "TypeScript React", "code": 238, "comment": 4, "blank": 20}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddInputParameter/index.tsx": {"language": "TypeScript React", "code": 115, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddAccessStrategy/index.tsx": {"language": "TypeScript React", "code": 220, "comment": 2, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/InterfaceSetting/index.tsx": {"language": "TypeScript React", "code": 211, "comment": 7, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditRole/index.tsx": {"language": "TypeScript React", "code": 227, "comment": 10, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditRole/PowerModal.tsx": {"language": "TypeScript React", "code": 71, "comment": 0, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/EditProxyCacheStrategy/index.tsx": {"language": "TypeScript React", "code": 194, "comment": 3, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/AccessStrategyDetail/index.tsx": {"language": "TypeScript React", "code": 136, "comment": 11, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/PublishGateWay/index.tsx": {"language": "TypeScript React", "code": 127, "comment": 3, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/InterfaceHeader/index.tsx": {"language": "TypeScript React", "code": 116, "comment": 18, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/VersionManagement/index.tsx": {"language": "TypeScript React", "code": 168, "comment": 17, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/PublishInterface/index.less": {"language": "Less", "code": 16, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/styles.module.less": {"language": "Less", "code": 91, "comment": 1, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/index.tsx": {"language": "TypeScript React", "code": 38, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ServiceMonitoringTable/index.tsx": {"language": "TypeScript React", "code": 208, "comment": 19, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/index.tsx": {"language": "TypeScript React", "code": 116, "comment": 16, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/PublishGateWay/index.less": {"language": "Less", "code": 9, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/useDataZoom.ts": {"language": "TypeScript", "code": 52, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/PublishInterface/index.tsx": {"language": "TypeScript React", "code": 1063, "comment": 83, "blank": 61}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/LogManagement/styles.module.less": {"language": "Less", "code": 16, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/option.ts": {"language": "TypeScript", "code": 324, "comment": 6, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/EditVersion/index.tsx": {"language": "TypeScript React", "code": 357, "comment": 15, "blank": 22}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/LogManagement/index.tsx": {"language": "TypeScript React", "code": 278, "comment": 5, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/PackageManagement/columns.tsx": {"language": "TypeScript React", "code": 127, "comment": 8, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/PackageManagement/index.tsx": {"language": "TypeScript React", "code": 275, "comment": 38, "blank": 19}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/index.tsx": {"language": "TypeScript React", "code": 132, "comment": 14, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditMenu/index.tsx": {"language": "TypeScript React", "code": 285, "comment": 9, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/UserGroupManagement/index.tsx": {"language": "TypeScript React", "code": 117, "comment": 14, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/LogDetail/ProcessCall.tsx": {"language": "TypeScript React", "code": 244, "comment": 8, "blank": 25}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/EditBackendService/index.tsx": {"language": "TypeScript React", "code": 136, "comment": 8, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/LogDetail/index.tsx": {"language": "TypeScript React", "code": 33, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/index.tsx": {"language": "TypeScript React", "code": 7, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/ProxyCacheDetails/index.tsx": {"language": "TypeScript React", "code": 184, "comment": 11, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/route.tsx": {"language": "TypeScript React", "code": 115, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/ServiceMonitoring/index.tsx": {"language": "TypeScript React", "code": 179, "comment": 3, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceManagement/index.tsx": {"language": "TypeScript React", "code": 175, "comment": 7, "blank": 15}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/index.tsx": {"language": "TypeScript React", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/apex/apex.js": {"language": "JavaScript", "code": 286, "comment": 36, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/Menu/index.tsx": {"language": "TypeScript React", "code": 156, "comment": 17, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/PackageManagement/styles.module.less": {"language": "Less", "code": 17, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/BackgroundServiceApi/index.tsx": {"language": "TypeScript React", "code": 309, "comment": 20, "blank": 18}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/FlowControlDetails/index.tsx": {"language": "TypeScript React", "code": 135, "comment": 11, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/AuditManagement/AuditInterface.tsx": {"language": "TypeScript React", "code": 144, "comment": 11, "blank": 15}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/AuditManagement/index.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/index.tsx": {"language": "TypeScript React", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/index.tsx": {"language": "TypeScript React", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/route.tsx": {"language": "TypeScript React", "code": 78, "comment": 7, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/constant.ts": {"language": "TypeScript", "code": 17, "comment": 12, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddResInformation/index.tsx": {"language": "TypeScript React", "code": 63, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/Role/index.tsx": {"language": "TypeScript React", "code": 117, "comment": 14, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/HospitalManagement/index.tsx": {"language": "TypeScript React", "code": 114, "comment": 11, "blank": 8}, "file:///Users/<USER>/Desktop/work/apimange-web/scripts/build.js": {"language": "JavaScript", "code": 169, "comment": 20, "blank": 24}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/BackendService/index.tsx": {"language": "TypeScript React", "code": 250, "comment": 22, "blank": 12}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/AuditManagement/SubscriptionReview.tsx": {"language": "TypeScript React", "code": 105, "comment": 14, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/FlowControl/index.tsx": {"language": "TypeScript React", "code": 218, "comment": 25, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/ProxyCache/index.tsx": {"language": "TypeScript React", "code": 218, "comment": 28, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/CustomTable/index.tsx": {"language": "TypeScript React", "code": 6, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/HistoryTabs/styles.module.less": {"language": "Less", "code": 20, "comment": 1, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/scripts/test.js": {"language": "JavaScript", "code": 36, "comment": 7, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/HistoryTabs/index.tsx": {"language": "TypeScript React", "code": 125, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/AlertTips/styles.less": {"language": "Less", "code": 11, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/LeftMenu/index.tsx": {"language": "TypeScript React", "code": 93, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/AlertTips/index.tsx": {"language": "TypeScript React", "code": 35, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/scripts/start.js": {"language": "JavaScript", "code": 133, "comment": 17, "blank": 17}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/UserManagement/index.tsx": {"language": "TypeScript React", "code": 181, "comment": 14, "blank": 10}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/ModalCustomTable/index.tsx": {"language": "TypeScript React", "code": 133, "comment": 13, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/ModifyPassword/index.tsx": {"language": "TypeScript React", "code": 85, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Layout/index.less": {"language": "Less", "code": 89, "comment": 10, "blank": 18}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Layout/styles.module.less": {"language": "Less", "code": 11, "comment": 3, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceGroup/index.tsx": {"language": "TypeScript React", "code": 73, "comment": 1, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/TigBtn/index.tsx": {"language": "TypeScript React", "code": 21, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/mock/login.js": {"language": "JavaScript", "code": 27, "comment": 1, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Title/styles.module.less": {"language": "Less", "code": 19, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Title/index.tsx": {"language": "TypeScript React", "code": 14, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Layout/index.tsx": {"language": "TypeScript React", "code": 226, "comment": 11, "blank": 14}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/ChooseGateWay/index.tsx": {"language": "TypeScript React", "code": 13, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/RequireAuth/index.tsx": {"language": "TypeScript React", "code": 18, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Layout/BreadcrumbNav/styles.module.less": {"language": "Less", "code": 11, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/mock/index.js": {"language": "JavaScript", "code": 19, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Layout/BreadcrumbNav/index.tsx": {"language": "TypeScript React", "code": 58, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/PageView/styles.module.less": {"language": "Less", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/Layout/UpdataPasswordNode/index.tsx": {"language": "TypeScript React", "code": 116, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/PageView/index.tsx": {"language": "TypeScript React", "code": 16, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/index.tsx": {"language": "TypeScript React", "code": 43, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/NoticeList/index.tsx": {"language": "TypeScript React", "code": 127, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/TigBtn/styles.module.less": {"language": "Less", "code": 15, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/CustomProTable/index.tsx": {"language": "TypeScript React", "code": 32, "comment": 30, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/RenderTags/index.tsx": {"language": "TypeScript React", "code": 122, "comment": 0, "blank": 14}, "file:///Users/<USER>/Desktop/work/apimange-web/src/service/interface-integration.ts": {"language": "TypeScript", "code": 35, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/SearchCard/index.tsx": {"language": "TypeScript React", "code": 17, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/components/RenderTags/index.less": {"language": "Less", "code": 12, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/config/jest/babelTransform.js": {"language": "JavaScript", "code": 25, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/OutFlow/index.tsx": {"language": "TypeScript React", "code": 63, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/config/jest/cssTransform.js": {"language": "JavaScript", "code": 9, "comment": 3, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/config/jest/fileTransform.js": {"language": "JavaScript", "code": 31, "comment": 4, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/config/modules.js": {"language": "JavaScript", "code": 80, "comment": 30, "blank": 25}, "file:///Users/<USER>/Desktop/work/apimange-web/config/env.js": {"language": "JavaScript", "code": 68, "comment": 40, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/config/pnpTs.js": {"language": "JavaScript", "code": 32, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/config/paths.js": {"language": "JavaScript", "code": 55, "comment": 10, "blank": 13}, "file:///Users/<USER>/Desktop/work/apimange-web/config/getHttpsConfig.js": {"language": "JavaScript", "code": 51, "comment": 7, "blank": 9}, "file:///Users/<USER>/Desktop/work/apimange-web/config/webpackDevServer.config.js": {"language": "JavaScript", "code": 65, "comment": 74, "blank": 7}, "file:///Users/<USER>/Desktop/work/apimange-web/config/theme.js": {"language": "JavaScript", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/config/proxy.js": {"language": "JavaScript", "code": 23, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/config/webpack.config.js": {"language": "JavaScript", "code": 566, "comment": 233, "blank": 27}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ServiceUseRatio/styles.module.less": {"language": "Less", "code": 22, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ServiceUseRatio/index.tsx": {"language": "TypeScript React", "code": 112, "comment": 1, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ServiceUseRatioA/styles.module.less": {"language": "Less", "code": 68, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ServiceUseRatioA/index.tsx": {"language": "TypeScript React", "code": 182, "comment": 1, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/PageConainer/styles.module.less": {"language": "Less", "code": 16, "comment": 20, "blank": 4}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/PageConainer/index.tsx": {"language": "TypeScript React", "code": 23, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ErrorCount/index.tsx": {"language": "TypeScript React", "code": 55, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/InletFlow/index.tsx": {"language": "TypeScript React", "code": 63, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/RequestCount/styles.module.less": {"language": "Less", "code": 2, "comment": 1, "blank": 1}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/DelayProbability/index.tsx": {"language": "TypeScript React", "code": 62, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/Empty/styles.module.less": {"language": "Less", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/Empty/index.tsx": {"language": "TypeScript React", "code": 12, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/RequestCount/index.tsx": {"language": "TypeScript React", "code": 61, "comment": 1, "blank": 5}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/Respond/index.tsx": {"language": "TypeScript React", "code": 143, "comment": 3, "blank": 11}, "file:///Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/Respond/styles.module.less": {"language": "Less", "code": 35, "comment": 0, "blank": 5}}