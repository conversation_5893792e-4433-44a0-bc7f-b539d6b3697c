"filename", "language", "TypeScript", "XML", "JavaScript", "Less", "CSS", "TypeScript React", "JSON with Comments", "JSON", "Markdown", "HTML", "comment", "blank", "total"
"/Users/<USER>/Desktop/work/apimange-web/.prettierrc.js", "JavaScript", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Desktop/work/apimange-web/README.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 2
"/Users/<USER>/Desktop/work/apimange-web/commitlint.config.js", "JavaScript", 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18
"/Users/<USER>/Desktop/work/apimange-web/config/env.js", "JavaScript", 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 40, 13, 121
"/Users/<USER>/Desktop/work/apimange-web/config/getHttpsConfig.js", "JavaScript", 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 7, 9, 67
"/Users/<USER>/Desktop/work/apimange-web/config/jest/babelTransform.js", "JavaScript", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 5, 30
"/Users/<USER>/Desktop/work/apimange-web/config/jest/cssTransform.js", "JavaScript", 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 3, 3, 15
"/Users/<USER>/Desktop/work/apimange-web/config/jest/fileTransform.js", "JavaScript", 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 4, 6, 41
"/Users/<USER>/Desktop/work/apimange-web/config/modules.js", "JavaScript", 0, 0, 80, 0, 0, 0, 0, 0, 0, 0, 30, 25, 135
"/Users/<USER>/Desktop/work/apimange-web/config/paths.js", "JavaScript", 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 10, 13, 78
"/Users/<USER>/Desktop/work/apimange-web/config/pnpTs.js", "JavaScript", 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 4, 36
"/Users/<USER>/Desktop/work/apimange-web/config/proxy.js", "JavaScript", 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 4, 2, 29
"/Users/<USER>/Desktop/work/apimange-web/config/theme.js", "JavaScript", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Desktop/work/apimange-web/config/webpack.config.js", "JavaScript", 0, 0, 566, 0, 0, 0, 0, 0, 0, 0, 233, 27, 826
"/Users/<USER>/Desktop/work/apimange-web/config/webpackDevServer.config.js", "JavaScript", 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 74, 7, 146
"/Users/<USER>/Desktop/work/apimange-web/mock/index.js", "JavaScript", 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 5, 24
"/Users/<USER>/Desktop/work/apimange-web/mock/login.js", "JavaScript", 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 1, 4, 32
"/Users/<USER>/Desktop/work/apimange-web/package.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 220, 0, 0, 0, 1, 221
"/Users/<USER>/Desktop/work/apimange-web/public/index.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 4, 17
"/Users/<USER>/Desktop/work/apimange-web/public/manifest.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 0, 1, 26
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/CHANGELOG.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 954, 0, 0, 401, 1355
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/README.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 55, 0, 0, 42, 97
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/bundleInfo.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 7259, 0, 0, 0, 0, 7259
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/nls.metadata.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 3654, 0, 0, 0, 0, 3654
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/base/worker/workerMain.js", "JavaScript", 0, 0, 11039, 0, 0, 0, 0, 0, 0, 0, 2334, 44, 13417
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/abap/abap.js", "JavaScript", 0, 0, 1317, 0, 0, 0, 0, 0, 0, 0, 13, 2, 1332
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/apex/apex.js", "JavaScript", 0, 0, 300, 0, 0, 0, 0, 0, 0, 0, 36, 1, 337
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/azcli/azcli.js", "JavaScript", 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 7, 2, 92
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/bat/bat.js", "JavaScript", 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 7, 2, 113
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/bicep/bicep.js", "JavaScript", 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 35, 1, 133
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/cameligo/cameligo.js", "JavaScript", 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 7, 2, 186
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/clojure/clojure.js", "JavaScript", 0, 0, 763, 0, 0, 0, 0, 0, 0, 0, 7, 2, 772
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/coffee/coffee.js", "JavaScript", 0, 0, 237, 0, 0, 0, 0, 0, 0, 0, 7, 2, 246
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/cpp/cpp.js", "JavaScript", 0, 0, 363, 0, 0, 0, 0, 0, 0, 0, 40, 2, 405
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/csharp/csharp.js", "JavaScript", 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 40, 2, 338
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/csp/csp.js", "JavaScript", 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 7, 2, 75
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/css/css.js", "JavaScript", 0, 0, 193, 0, 0, 0, 0, 0, 0, 0, 7, 2, 202
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/dart/dart.js", "JavaScript", 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 68, 1, 288
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/dockerfile/dockerfile.js", "JavaScript", 0, 0, 141, 0, 0, 0, 0, 0, 0, 0, 7, 2, 150
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/ecl/ecl.js", "JavaScript", 0, 0, 448, 0, 0, 0, 0, 0, 0, 0, 23, 1, 472
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/elixir/elixir.js", "JavaScript", 0, 0, 476, 0, 0, 0, 0, 0, 0, 0, 7, 2, 485
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/flow9/flow9.js", "JavaScript", 0, 0, 135, 0, 0, 0, 0, 0, 0, 0, 23, 1, 159
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/fsharp/fsharp.js", "JavaScript", 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 7, 2, 228
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/go/go.js", "JavaScript", 0, 0, 195, 0, 0, 0, 0, 0, 0, 0, 30, 1, 226
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/graphql/graphql.js", "JavaScript", 0, 0, 150, 0, 0, 0, 0, 0, 0, 0, 7, 2, 159
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/handlebars/handlebars.js", "JavaScript", 0, 0, 420, 0, 0, 0, 0, 0, 0, 0, 10, 5, 435
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/hcl/hcl.js", "JavaScript", 0, 0, 159, 0, 0, 0, 0, 0, 0, 0, 35, 1, 195
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/html/html.js", "JavaScript", 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 10, 5, 311
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/ini/ini.js", "JavaScript", 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 7, 2, 86
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/java/java.js", "JavaScript", 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 33, 1, 239
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/javascript/javascript.js", "JavaScript", 0, 0, 285, 0, 0, 0, 0, 0, 0, 0, 146, 4, 435
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/julia/julia.js", "JavaScript", 0, 0, 505, 0, 0, 0, 0, 0, 0, 0, 7, 2, 514
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/kotlin/kotlin.js", "JavaScript", 0, 0, 231, 0, 0, 0, 0, 0, 0, 0, 31, 1, 263
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/less/less.js", "JavaScript", 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 7, 2, 185
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/lexon/lexon.js", "JavaScript", 0, 0, 160, 0, 0, 0, 0, 0, 0, 0, 7, 2, 169
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/liquid/liquid.js", "JavaScript", 0, 0, 267, 0, 0, 0, 0, 0, 0, 0, 10, 5, 282
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/lua/lua.js", "JavaScript", 0, 0, 166, 0, 0, 0, 0, 0, 0, 0, 7, 2, 175
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/m3/m3.js", "JavaScript", 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 7, 2, 228
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/markdown/markdown.js", "JavaScript", 0, 0, 211, 0, 0, 0, 0, 0, 0, 0, 7, 2, 220
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/mips/mips.js", "JavaScript", 0, 0, 203, 0, 0, 0, 0, 0, 0, 0, 7, 2, 212
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/msdax/msdax.js", "JavaScript", 0, 0, 386, 0, 0, 0, 0, 0, 0, 0, 7, 2, 395
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/mysql/mysql.js", "JavaScript", 0, 0, 884, 0, 0, 0, 0, 0, 0, 0, 7, 2, 893
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/objective-c/objective-c.js", "JavaScript", 0, 0, 197, 0, 0, 0, 0, 0, 0, 0, 7, 2, 206
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pascal/pascal.js", "JavaScript", 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 7, 2, 263
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pascaligo/pascaligo.js", "JavaScript", 0, 0, 167, 0, 0, 0, 0, 0, 0, 0, 7, 2, 176
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/perl/perl.js", "JavaScript", 0, 0, 608, 0, 0, 0, 0, 0, 0, 0, 7, 2, 617
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pgsql/pgsql.js", "JavaScript", 0, 0, 852, 0, 0, 0, 0, 0, 0, 0, 7, 2, 861
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/php/php.js", "JavaScript", 0, 0, 480, 0, 0, 0, 0, 0, 0, 0, 7, 2, 489
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pla/pla.js", "JavaScript", 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 7, 2, 149
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/postiats/postiats.js", "JavaScript", 0, 0, 548, 0, 0, 0, 0, 0, 0, 0, 12, 2, 562
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/powerquery/powerquery.js", "JavaScript", 0, 0, 899, 0, 0, 0, 0, 0, 0, 0, 7, 2, 908
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/powershell/powershell.js", "JavaScript", 0, 0, 242, 0, 0, 0, 0, 0, 0, 0, 7, 2, 251
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/protobuf/protobuf.js", "JavaScript", 0, 0, 404, 0, 0, 0, 0, 0, 0, 0, 34, 2, 440
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/pug/pug.js", "JavaScript", 0, 0, 402, 0, 0, 0, 0, 0, 0, 0, 7, 2, 411
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/python/python.js", "JavaScript", 0, 0, 284, 0, 0, 0, 0, 0, 0, 0, 10, 5, 299
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/qsharp/qsharp.js", "JavaScript", 0, 0, 290, 0, 0, 0, 0, 0, 0, 0, 7, 2, 299
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/r/r.js", "JavaScript", 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 7, 2, 263
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/razor/razor.js", "JavaScript", 0, 0, 541, 0, 0, 0, 0, 0, 0, 0, 10, 5, 556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/redis/redis.js", "JavaScript", 0, 0, 307, 0, 0, 0, 0, 0, 0, 0, 7, 2, 316
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/redshift/redshift.js", "JavaScript", 0, 0, 815, 0, 0, 0, 0, 0, 0, 0, 7, 2, 824
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/restructuredtext/restructuredtext.js", "JavaScript", 0, 0, 171, 0, 0, 0, 0, 0, 0, 0, 7, 2, 180
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/ruby/ruby.js", "JavaScript", 0, 0, 453, 0, 0, 0, 0, 0, 0, 0, 7, 2, 462
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/rust/rust.js", "JavaScript", 0, 0, 312, 0, 0, 0, 0, 0, 0, 0, 42, 1, 355
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/sb/sb.js", "JavaScript", 0, 0, 119, 0, 0, 0, 0, 0, 0, 0, 7, 2, 128
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/scala/scala.js", "JavaScript", 0, 0, 360, 0, 0, 0, 0, 0, 0, 0, 9, 2, 371
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/scheme/scheme.js", "JavaScript", 0, 0, 123, 0, 0, 0, 0, 0, 0, 0, 7, 2, 132
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/scss/scss.js", "JavaScript", 0, 0, 251, 0, 0, 0, 0, 0, 0, 0, 7, 2, 260
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/shell/shell.js", "JavaScript", 0, 0, 230, 0, 0, 0, 0, 0, 0, 0, 7, 2, 239
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/solidity/solidity.js", "JavaScript", 0, 0, 1340, 0, 0, 0, 0, 0, 0, 0, 28, 1, 1369
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/sophia/sophia.js", "JavaScript", 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 28, 1, 208
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/sparql/sparql.js", "JavaScript", 0, 0, 202, 0, 0, 0, 0, 0, 0, 0, 7, 2, 211
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/sql/sql.js", "JavaScript", 0, 0, 827, 0, 0, 0, 0, 0, 0, 0, 7, 2, 836
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/st/st.js", "JavaScript", 0, 0, 420, 0, 0, 0, 0, 0, 0, 0, 10, 2, 432
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/swift/swift.js", "JavaScript", 0, 0, 261, 0, 0, 0, 0, 0, 0, 0, 10, 2, 273
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/systemverilog/systemverilog.js", "JavaScript", 0, 0, 517, 0, 0, 0, 0, 0, 0, 0, 56, 1, 574
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/tcl/tcl.js", "JavaScript", 0, 0, 241, 0, 0, 0, 0, 0, 0, 0, 7, 2, 250
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/twig/twig.js", "JavaScript", 0, 0, 332, 0, 0, 0, 0, 0, 0, 0, 7, 2, 341
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/typescript/typescript.js", "JavaScript", 0, 0, 285, 0, 0, 0, 0, 0, 0, 0, 80, 4, 369
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/vb/vb.js", "JavaScript", 0, 0, 373, 0, 0, 0, 0, 0, 0, 0, 7, 2, 382
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/xml/xml.js", "JavaScript", 0, 0, 123, 0, 0, 0, 0, 0, 0, 0, 10, 5, 138
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/basic-languages/yaml/yaml.js", "JavaScript", 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 7, 2, 186
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.css", "CSS", 0, 0, 0, 0, 3183, 0, 0, 0, 0, 0, 476, 740, 4399
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.js", "JavaScript", 0, 0, 129481, 0, 0, 0, 0, 0, 0, 0, 12497, 2992, 144970
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.de.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.es.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.fr.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.it.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.ja.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 9, 1, 1559
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.ko.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.ru.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.zh-cn.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/editor/editor.main.nls.zh-tw.js", "JavaScript", 0, 0, 1549, 0, 0, 0, 0, 0, 0, 0, 6, 1, 1556
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/css/cssMode.js", "JavaScript", 0, 0, 1954, 0, 0, 0, 0, 0, 0, 0, 13, 8, 1975
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/css/cssWorker.js", "JavaScript", 0, 0, 35437, 0, 0, 0, 0, 0, 0, 0, 50, 45, 35532
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/html/htmlMode.js", "JavaScript", 0, 0, 1862, 0, 0, 0, 0, 0, 0, 0, 13, 8, 1883
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/html/htmlWorker.js", "JavaScript", 0, 0, 16042, 0, 0, 0, 0, 0, 0, 0, 40, 35, 16117
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/json/jsonMode.js", "JavaScript", 0, 0, 2327, 0, 0, 0, 0, 0, 0, 0, 193, 13, 2533
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/json/jsonWorker.js", "JavaScript", 0, 0, 7231, 0, 0, 0, 0, 0, 0, 0, 34, 29, 7294
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/typescript/tsMode.js", "JavaScript", 0, 0, 1317, 0, 0, 0, 0, 0, 0, 0, 15, 11, 1343
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/language/typescript/tsWorker.js", "JavaScript", 0, 0, 142897, 0, 0, 0, 0, 0, 0, 0, 15165, 2321, 160383
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/dev/vs/loader.js", "JavaScript", 0, 0, 1719, 0, 0, 0, 0, 0, 0, 0, 238, 2, 1959
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/metadata.d.ts", "TypeScript", 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 10, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/metadata.js", "JavaScript", 0, 0, 544, 0, 0, 0, 0, 0, 0, 0, 0, 2, 546
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/browser.js", "JavaScript", 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 9, 1, 65
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/canIUse.js", "JavaScript", 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 9, 1, 32
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/contextmenu.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/dnd.js", "JavaScript", 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 17, 1, 39
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/dom.js", "JavaScript", 0, 0, 888, 0, 0, 0, 0, 0, 0, 0, 107, 1, 996
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/dompurify/dompurify.js", "JavaScript", 0, 0, 772, 0, 0, 0, 0, 0, 0, 0, 376, 237, 1385
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/event.js", "JavaScript", 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 4, 1, 26
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/fastDomNode.js", "JavaScript", 0, 0, 199, 0, 0, 0, 0, 0, 0, 0, 4, 1, 204
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/formattedTextRenderer.js", "JavaScript", 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 5, 1, 169
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/globalMouseMoveMonitor.js", "JavaScript", 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 12, 1, 100
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/history.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/iframe.js", "JavaScript", 0, 0, 79, 0, 0, 0, 0, 0, 0, 0, 17, 1, 97
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/keyboardEvent.js", "JavaScript", 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 10, 1, 119
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/markdownRenderer.js", "JavaScript", 0, 0, 285, 0, 0, 0, 0, 0, 0, 0, 41, 1, 327
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/mouseEvent.js", "JavaScript", 0, 0, 109, 0, 0, 0, 0, 0, 0, 0, 16, 1, 126
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/touch.js", "JavaScript", 0, 0, 244, 0, 0, 0, 0, 0, 0, 0, 12, 1, 257
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionViewItems.js", "JavaScript", 0, 0, 271, 0, 0, 0, 0, 0, 0, 0, 21, 1, 293
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.css", "CSS", 0, 0, 0, 0, 86, 0, 0, 0, 0, 0, 6, 19, 111
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/actionbar/actionbar.js", "JavaScript", 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 16, 1, 383
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/aria/aria.css", "CSS", 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 1, 9
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/aria/aria.js", "JavaScript", 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 13, 1, 89
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/button/button.css", "CSS", 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 4, 11, 56
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/button/button.js", "JavaScript", 0, 0, 145, 0, 0, 0, 0, 0, 0, 0, 1, 1, 147
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.css", "CSS", 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 5, 8, 54
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/checkbox/checkbox.js", "JavaScript", 0, 0, 99, 0, 0, 0, 0, 0, 0, 0, 4, 1, 104
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon/codicon-modifiers.css", "CSS", 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 6, 6, 34
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon/codicon.css", "CSS", 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 5, 4, 26
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/codicons/codiconStyles.js", "JavaScript", 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 4, 1, 15
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.css", "CSS", 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 4, 3, 19
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/contextview/contextview.js", "JavaScript", 0, 0, 255, 0, 0, 0, 0, 0, 0, 0, 20, 7, 282
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.css", "CSS", 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 4, 3, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/countBadge/countBadge.js", "JavaScript", 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 4, 1, 59
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.css", "CSS", 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 4, 8, 47
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdown.js", "JavaScript", 0, 0, 135, 0, 0, 0, 0, 0, 0, 0, 4, 1, 140
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/dropdown/dropdownActionViewItem.js", "JavaScript", 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 5, 1, 90
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.css", "CSS", 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 9, 8, 65
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInput.js", "JavaScript", 0, 0, 298, 0, 0, 0, 0, 0, 0, 0, 5, 1, 304
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/findinput/findInputCheckboxes.js", "JavaScript", 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 4, 1, 47
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/findinput/replaceInput.js", "JavaScript", 0, 0, 241, 0, 0, 0, 0, 0, 0, 0, 6, 1, 248
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/highlightedlabel/highlightedLabel.js", "JavaScript", 0, 0, 86, 0, 0, 0, 0, 0, 0, 0, 26, 1, 113
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/hover/hover.css", "CSS", 0, 0, 0, 0, 120, 0, 0, 0, 0, 0, 7, 28, 155
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/hover/hoverWidget.js", "JavaScript", 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 4, 1, 60
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconHoverDelegate.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabel.js", "JavaScript", 0, 0, 214, 0, 0, 0, 0, 0, 0, 0, 4, 1, 219
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabelHover.js", "JavaScript", 0, 0, 161, 0, 0, 0, 0, 0, 0, 0, 11, 1, 173
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconLabels.js", "JavaScript", 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 4, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/iconLabel/iconlabel.css", "CSS", 0, 0, 0, 0, 76, 0, 0, 0, 0, 0, 9, 20, 105
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.css", "CSS", 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 8, 19, 112
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/inputbox/inputBox.js", "JavaScript", 0, 0, 517, 0, 0, 0, 0, 0, 0, 0, 13, 1, 531
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.css", "CSS", 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 4, 7, 38
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel/keybindingLabel.js", "JavaScript", 0, 0, 124, 0, 0, 0, 0, 0, 0, 0, 4, 1, 129
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/list.css", "CSS", 0, 0, 0, 0, 128, 0, 0, 0, 0, 0, 9, 25, 162
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/list.js", "JavaScript", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 4, 1, 10
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/listPaging.js", "JavaScript", 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 4, 1, 118
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/listView.js", "JavaScript", 0, 0, 961, 0, 0, 0, 0, 0, 0, 0, 40, 1, 1002
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/listWidget.js", "JavaScript", 0, 0, 1341, 0, 0, 0, 0, 0, 0, 0, 58, 1, 1400
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/rangeMap.js", "JavaScript", 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 37, 1, 146
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/rowCache.js", "JavaScript", 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 12, 1, 79
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/list/splice.js", "JavaScript", 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 4, 1, 13
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/menu/menu.js", "JavaScript", 0, 0, 1021, 0, 0, 0, 0, 0, 0, 0, 57, 63, 1141
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/mouseCursor/mouseCursor.css", "CSS", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 5, 3, 15
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/mouseCursor/mouseCursor.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.css", "CSS", 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 14, 8, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/progressbar/progressbar.js", "JavaScript", 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 15, 1, 103
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/sash/sash.css", "CSS", 0, 0, 0, 0, 108, 0, 0, 0, 0, 0, 5, 24, 137
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/sash/sash.js", "JavaScript", 0, 0, 359, 0, 0, 0, 0, 0, 0, 0, 71, 1, 431
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/abstractScrollbar.js", "JavaScript", 0, 0, 184, 0, 0, 0, 0, 0, 0, 0, 20, 1, 205
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/horizontalScrollbar.js", "JavaScript", 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 4, 1, 89
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/media/scrollbars.css", "CSS", 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 7, 6, 54
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElement.js", "JavaScript", 0, 0, 454, 0, 0, 0, 0, 0, 0, 0, 57, 1, 512
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollableElementOptions.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarArrow.js", "JavaScript", 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 8, 1, 73
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarState.js", "JavaScript", 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 27, 1, 162
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/scrollbarVisibilityController.js", "JavaScript", 0, 0, 86, 0, 0, 0, 0, 0, 0, 0, 8, 1, 95
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/scrollbar/verticalScrollbar.js", "JavaScript", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 6, 1, 92
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/splitview/splitview.css", "CSS", 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 4, 13, 71
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/splitview/splitview.js", "JavaScript", 0, 0, 639, 0, 0, 0, 0, 0, 0, 0, 109, 1, 749
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/table/table.css", "CSS", 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 9, 9, 62
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/table/table.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/table/tableWidget.js", "JavaScript", 0, 0, 161, 0, 0, 0, 0, 0, 0, 0, 4, 1, 166
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/abstractTree.js", "JavaScript", 0, 0, 1140, 0, 0, 0, 0, 0, 0, 0, 26, 1, 1167
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/asyncDataTree.js", "JavaScript", 0, 0, 757, 0, 0, 0, 0, 0, 0, 0, 21, 1, 779
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/compressedObjectTreeModel.js", "JavaScript", 0, 0, 337, 0, 0, 0, 0, 0, 0, 0, 13, 1, 351
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/dataTree.js", "JavaScript", 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 4, 1, 18
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/indexTreeModel.js", "JavaScript", 0, 0, 520, 0, 0, 0, 0, 0, 0, 0, 16, 1, 537
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/media/tree.css", "CSS", 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 5, 12, 70
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/objectTree.js", "JavaScript", 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 4, 1, 131
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/objectTreeModel.js", "JavaScript", 0, 0, 161, 0, 0, 0, 0, 0, 0, 0, 4, 1, 166
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/tree/tree.js", "JavaScript", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 4, 1, 30
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/browser/ui/widget.js", "JavaScript", 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 4, 1, 42
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/actions.js", "JavaScript", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 7, 1, 164
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/arrays.js", "JavaScript", 0, 0, 249, 0, 0, 0, 0, 0, 0, 0, 68, 1, 318
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/assert.js", "JavaScript", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 7, 1, 13
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/async.js", "JavaScript", 0, 0, 652, 0, 0, 0, 0, 0, 0, 0, 136, 1, 789
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/buffer.js", "JavaScript", 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 2, 1, 58
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/cancellation.js", "JavaScript", 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 12, 1, 111
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/charCode.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/codicons.js", "JavaScript", 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 15, 1, 174
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/collections.js", "JavaScript", 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 8, 1, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/color.js", "JavaScript", 0, 0, 395, 0, 0, 0, 0, 0, 0, 0, 48, 1, 444
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/comparers.js", "JavaScript", 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 16, 1, 70
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/decorators.js", "JavaScript", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 1, 31
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/diff/diff.js", "JavaScript", 0, 0, 652, 0, 0, 0, 0, 0, 0, 0, 247, 1, 900
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/diff/diffChange.js", "JavaScript", 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 18, 1, 33
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/errorMessage.js", "JavaScript", 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 11, 1, 65
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/errors.js", "JavaScript", 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 11, 1, 96
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/event.js", "JavaScript", 0, 0, 483, 0, 0, 0, 0, 0, 0, 0, 101, 1, 585
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/extpath.js", "JavaScript", 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 33, 1, 152
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/filters.js", "JavaScript", 0, 0, 617, 0, 0, 0, 0, 0, 0, 0, 76, 1, 694
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/functional.js", "JavaScript", 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 4, 1, 18
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/fuzzyScorer.js", "JavaScript", 0, 0, 116, 0, 0, 0, 0, 0, 0, 0, 22, 1, 139
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/glob.js", "JavaScript", 0, 0, 453, 0, 0, 0, 0, 0, 0, 0, 46, 1, 500
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/hash.js", "JavaScript", 0, 0, 239, 0, 0, 0, 0, 0, 0, 0, 19, 1, 259
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/history.js", "JavaScript", 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 4, 1, 74
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/htmlContent.js", "JavaScript", 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 5, 1, 98
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/iconLabels.js", "JavaScript", 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 24, 1, 122
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/idGenerator.js", "JavaScript", 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 4, 1, 15
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/iterator.js", "JavaScript", 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 15, 1, 150
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/jsonSchema.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/keyCodes.js", "JavaScript", 0, 0, 349, 0, 0, 0, 0, 0, 0, 0, 24, 1, 374
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/keybindingLabels.js", "JavaScript", 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 17, 1, 115
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/keybindings.js", "JavaScript", 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 13, 1, 106
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/labels.js", "JavaScript", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 5, 1, 31
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/lazy.js", "JavaScript", 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 13, 1, 38
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/lifecycle.js", "JavaScript", 0, 0, 214, 0, 0, 0, 0, 0, 0, 0, 34, 1, 249
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/linkedList.js", "JavaScript", 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 11, 1, 125
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/map.js", "JavaScript", 0, 0, 984, 0, 0, 0, 0, 0, 0, 0, 58, 1, 1043
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/marked/marked.js", "JavaScript", 0, 0, 2055, 0, 0, 0, 0, 0, 0, 0, 419, 520, 2994
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/marshalling.js", "JavaScript", 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 5, 1, 41
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/mime.js", "JavaScript", 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 28, 1, 192
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/navigator.js", "JavaScript", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 4, 1, 35
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/network.js", "JavaScript", 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 48, 1, 149
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/numbers.js", "JavaScript", 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 4, 1, 22
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/objects.js", "JavaScript", 0, 0, 150, 0, 0, 0, 0, 0, 0, 0, 9, 1, 160
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/paging.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/path.js", "JavaScript", 0, 0, 1120, 0, 0, 0, 0, 0, 0, 0, 259, 1, 1380
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/platform.js", "JavaScript", 0, 0, 132, 0, 0, 0, 0, 0, 0, 0, 26, 1, 159
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/process.js", "JavaScript", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 25, 1, 56
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/range.js", "JavaScript", 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 8, 1, 45
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/resources.js", "JavaScript", 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 22, 1, 228
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/scrollable.js", "JavaScript", 0, 0, 289, 0, 0, 0, 0, 0, 0, 0, 27, 1, 317
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/search.js", "JavaScript", 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 5, 1, 50
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/sequence.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/severity.js", "JavaScript", 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 8, 1, 50
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/stopwatch.js", "JavaScript", 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 4, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/strings.js", "JavaScript", 0, 0, 567, 0, 0, 0, 0, 0, 0, 0, 187, 1, 755
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/styler.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/types.js", "JavaScript", 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 40, 1, 149
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/uint.js", "JavaScript", 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 4, 1, 23
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/uri.js", "JavaScript", 0, 0, 437, 0, 0, 0, 0, 0, 0, 0, 152, 1, 590
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/uuid.js", "JavaScript", 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 6, 1, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/common/worker/simpleWorker.js", "JavaScript", 0, 0, 407, 0, 0, 0, 0, 0, 0, 0, 34, 1, 442
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/media/quickInput.css", "CSS", 0, 0, 0, 0, 230, 0, 0, 0, 0, 0, 10, 52, 292
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInput.js", "JavaScript", 0, 0, 1268, 0, 0, 0, 0, 0, 0, 0, 32, 1, 1301
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputBox.js", "JavaScript", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 4, 1, 90
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputList.js", "JavaScript", 0, 0, 595, 0, 0, 0, 0, 0, 0, 0, 26, 1, 622
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/browser/quickInputUtils.js", "JavaScript", 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 4, 1, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/quickinput/common/quickInput.js", "JavaScript", 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 14, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/parts/storage/common/storage.js", "JavaScript", 0, 0, 167, 0, 0, 0, 0, 0, 0, 0, 23, 1, 191
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/base/worker/defaultWorkerFactory.js", "JavaScript", 0, 0, 76, 0, 0, 0, 0, 0, 0, 0, 31, 1, 108
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/_.contribution.js", "JavaScript", 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 8, 4, 76
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/abap/abap.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/abap/abap.js", "JavaScript", 0, 0, 1303, 0, 0, 0, 0, 0, 0, 0, 13, 2, 1318
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/apex/apex.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/apex/apex.js", "JavaScript", 0, 0, 286, 0, 0, 0, 0, 0, 0, 0, 36, 1, 323
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/azcli/azcli.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/azcli/azcli.js", "JavaScript", 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 7, 2, 78
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/bat/bat.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/bat/bat.js", "JavaScript", 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 7, 2, 99
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/bicep/bicep.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/bicep/bicep.js", "JavaScript", 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 35, 1, 119
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/cameligo/cameligo.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/cameligo/cameligo.js", "JavaScript", 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 7, 2, 172
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/clojure/clojure.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/clojure/clojure.js", "JavaScript", 0, 0, 749, 0, 0, 0, 0, 0, 0, 0, 7, 2, 758
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/coffee/coffee.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/coffee/coffee.js", "JavaScript", 0, 0, 223, 0, 0, 0, 0, 0, 0, 0, 7, 2, 232
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/cpp/cpp.contribution.js", "JavaScript", 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 7, 2, 38
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/cpp/cpp.js", "JavaScript", 0, 0, 349, 0, 0, 0, 0, 0, 0, 0, 40, 2, 391
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/csharp/csharp.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/csharp/csharp.js", "JavaScript", 0, 0, 282, 0, 0, 0, 0, 0, 0, 0, 40, 2, 324
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/csp/csp.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/csp/csp.js", "JavaScript", 0, 0, 52, 0, 0, 0, 0, 0, 0, 0, 7, 2, 61
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/css/css.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/css/css.js", "JavaScript", 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 7, 2, 188
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/dart/dart.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/dart/dart.js", "JavaScript", 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 68, 1, 274
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/dockerfile/dockerfile.js", "JavaScript", 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 7, 2, 136
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ecl/ecl.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ecl/ecl.js", "JavaScript", 0, 0, 434, 0, 0, 0, 0, 0, 0, 0, 23, 1, 458
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/elixir/elixir.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/elixir/elixir.js", "JavaScript", 0, 0, 462, 0, 0, 0, 0, 0, 0, 0, 7, 2, 471
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/flow9/flow9.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/flow9/flow9.js", "JavaScript", 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 23, 1, 145
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/fsharp/fsharp.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/fsharp/fsharp.js", "JavaScript", 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 7, 2, 214
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/go/go.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/go/go.js", "JavaScript", 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 30, 1, 212
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/graphql/graphql.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/graphql/graphql.js", "JavaScript", 0, 0, 136, 0, 0, 0, 0, 0, 0, 0, 7, 2, 145
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/handlebars/handlebars.js", "JavaScript", 0, 0, 396, 0, 0, 0, 0, 0, 0, 0, 8, 4, 408
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/hcl/hcl.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/hcl/hcl.js", "JavaScript", 0, 0, 145, 0, 0, 0, 0, 0, 0, 0, 35, 1, 181
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/html/html.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/html/html.js", "JavaScript", 0, 0, 272, 0, 0, 0, 0, 0, 0, 0, 8, 4, 284
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ini/ini.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ini/ini.js", "JavaScript", 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 7, 2, 72
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/java/java.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/java/java.js", "JavaScript", 0, 0, 191, 0, 0, 0, 0, 0, 0, 0, 33, 1, 225
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution.js", "JavaScript", 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 7, 2, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/javascript/javascript.js", "JavaScript", 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 7, 2, 78
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/julia/julia.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/julia/julia.js", "JavaScript", 0, 0, 491, 0, 0, 0, 0, 0, 0, 0, 7, 2, 500
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/kotlin/kotlin.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/kotlin/kotlin.js", "JavaScript", 0, 0, 217, 0, 0, 0, 0, 0, 0, 0, 31, 1, 249
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/less/less.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/less/less.js", "JavaScript", 0, 0, 162, 0, 0, 0, 0, 0, 0, 0, 7, 2, 171
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/lexon/lexon.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/lexon/lexon.js", "JavaScript", 0, 0, 146, 0, 0, 0, 0, 0, 0, 0, 7, 2, 155
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/liquid/liquid.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/liquid/liquid.js", "JavaScript", 0, 0, 243, 0, 0, 0, 0, 0, 0, 0, 8, 4, 255
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/lua/lua.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/lua/lua.js", "JavaScript", 0, 0, 152, 0, 0, 0, 0, 0, 0, 0, 7, 2, 161
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/m3/m3.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/m3/m3.js", "JavaScript", 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 7, 2, 214
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/markdown/markdown.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/markdown/markdown.js", "JavaScript", 0, 0, 197, 0, 0, 0, 0, 0, 0, 0, 7, 2, 206
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/mips/mips.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/mips/mips.js", "JavaScript", 0, 0, 189, 0, 0, 0, 0, 0, 0, 0, 7, 2, 198
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/monaco.contribution.js", "JavaScript", 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 7, 2, 86
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/msdax/msdax.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/msdax/msdax.js", "JavaScript", 0, 0, 372, 0, 0, 0, 0, 0, 0, 0, 7, 2, 381
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/mysql/mysql.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/mysql/mysql.js", "JavaScript", 0, 0, 870, 0, 0, 0, 0, 0, 0, 0, 7, 2, 879
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js", "JavaScript", 0, 0, 183, 0, 0, 0, 0, 0, 0, 0, 7, 2, 192
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pascal/pascal.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pascal/pascal.js", "JavaScript", 0, 0, 240, 0, 0, 0, 0, 0, 0, 0, 7, 2, 249
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pascaligo/pascaligo.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pascaligo/pascaligo.js", "JavaScript", 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 7, 2, 162
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/perl/perl.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/perl/perl.js", "JavaScript", 0, 0, 594, 0, 0, 0, 0, 0, 0, 0, 7, 2, 603
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pgsql/pgsql.js", "JavaScript", 0, 0, 838, 0, 0, 0, 0, 0, 0, 0, 7, 2, 847
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/php/php.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/php/php.js", "JavaScript", 0, 0, 466, 0, 0, 0, 0, 0, 0, 0, 7, 2, 475
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pla/pla.contribution.js", "JavaScript", 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 7, 2, 23
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pla/pla.js", "JavaScript", 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 7, 2, 135
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/postiats/postiats.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/postiats/postiats.js", "JavaScript", 0, 0, 534, 0, 0, 0, 0, 0, 0, 0, 12, 2, 548
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/powerquery/powerquery.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/powerquery/powerquery.js", "JavaScript", 0, 0, 885, 0, 0, 0, 0, 0, 0, 0, 7, 2, 894
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/powershell/powershell.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/powershell/powershell.js", "JavaScript", 0, 0, 228, 0, 0, 0, 0, 0, 0, 0, 7, 2, 237
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js", "JavaScript", 0, 0, 390, 0, 0, 0, 0, 0, 0, 0, 34, 2, 426
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pug/pug.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/pug/pug.js", "JavaScript", 0, 0, 388, 0, 0, 0, 0, 0, 0, 0, 7, 2, 397
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/python/python.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/python/python.js", "JavaScript", 0, 0, 260, 0, 0, 0, 0, 0, 0, 0, 8, 4, 272
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js", "JavaScript", 0, 0, 276, 0, 0, 0, 0, 0, 0, 0, 7, 2, 285
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/r/r.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/r/r.js", "JavaScript", 0, 0, 240, 0, 0, 0, 0, 0, 0, 0, 7, 2, 249
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/razor/razor.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/razor/razor.js", "JavaScript", 0, 0, 517, 0, 0, 0, 0, 0, 0, 0, 8, 4, 529
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/redis/redis.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/redis/redis.js", "JavaScript", 0, 0, 293, 0, 0, 0, 0, 0, 0, 0, 7, 2, 302
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/redshift/redshift.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/redshift/redshift.js", "JavaScript", 0, 0, 801, 0, 0, 0, 0, 0, 0, 0, 7, 2, 810
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js", "JavaScript", 0, 0, 157, 0, 0, 0, 0, 0, 0, 0, 7, 2, 166
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ruby/ruby.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/ruby/ruby.js", "JavaScript", 0, 0, 439, 0, 0, 0, 0, 0, 0, 0, 7, 2, 448
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/rust/rust.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/rust/rust.js", "JavaScript", 0, 0, 298, 0, 0, 0, 0, 0, 0, 0, 42, 1, 341
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sb/sb.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sb/sb.js", "JavaScript", 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 7, 2, 114
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scala/scala.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scala/scala.js", "JavaScript", 0, 0, 346, 0, 0, 0, 0, 0, 0, 0, 9, 2, 357
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scheme/scheme.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scheme/scheme.js", "JavaScript", 0, 0, 109, 0, 0, 0, 0, 0, 0, 0, 7, 2, 118
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scss/scss.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/scss/scss.js", "JavaScript", 0, 0, 237, 0, 0, 0, 0, 0, 0, 0, 7, 2, 246
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/shell/shell.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/shell/shell.js", "JavaScript", 0, 0, 216, 0, 0, 0, 0, 0, 0, 0, 7, 2, 225
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/solidity/solidity.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/solidity/solidity.js", "JavaScript", 0, 0, 1326, 0, 0, 0, 0, 0, 0, 0, 28, 1, 1355
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sophia/sophia.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sophia/sophia.js", "JavaScript", 0, 0, 165, 0, 0, 0, 0, 0, 0, 0, 28, 1, 194
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sparql/sparql.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sparql/sparql.js", "JavaScript", 0, 0, 188, 0, 0, 0, 0, 0, 0, 0, 7, 2, 197
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sql/sql.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/sql/sql.js", "JavaScript", 0, 0, 813, 0, 0, 0, 0, 0, 0, 0, 7, 2, 822
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/st/st.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/st/st.js", "JavaScript", 0, 0, 406, 0, 0, 0, 0, 0, 0, 0, 10, 2, 418
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/swift/swift.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/swift/swift.js", "JavaScript", 0, 0, 247, 0, 0, 0, 0, 0, 0, 0, 10, 2, 259
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.contribution.js", "JavaScript", 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 7, 2, 38
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js", "JavaScript", 0, 0, 503, 0, 0, 0, 0, 0, 0, 0, 56, 1, 560
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/tcl/tcl.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/tcl/tcl.js", "JavaScript", 0, 0, 227, 0, 0, 0, 0, 0, 0, 0, 7, 2, 236
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/twig/twig.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/twig/twig.js", "JavaScript", 0, 0, 318, 0, 0, 0, 0, 0, 0, 0, 7, 2, 327
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/typescript/typescript.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/typescript/typescript.js", "JavaScript", 0, 0, 261, 0, 0, 0, 0, 0, 0, 0, 78, 3, 342
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/vb/vb.contribution.js", "JavaScript", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 7, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/vb/vb.js", "JavaScript", 0, 0, 359, 0, 0, 0, 0, 0, 0, 0, 7, 2, 368
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/xml/xml.contribution.js", "JavaScript", 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 7, 2, 40
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/xml/xml.js", "JavaScript", 0, 0, 99, 0, 0, 0, 0, 0, 0, 0, 8, 4, 111
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/yaml/yaml.contribution.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 7, 2, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/basic-languages/yaml/yaml.js", "JavaScript", 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 7, 2, 172
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/config/charWidthReader.js", "JavaScript", 0, 0, 107, 0, 0, 0, 0, 0, 0, 0, 10, 1, 118
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/config/configuration.js", "JavaScript", 0, 0, 246, 0, 0, 0, 0, 0, 0, 0, 15, 1, 262
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/config/elementSizeObserver.js", "JavaScript", 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 5, 1, 83
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/coreCommands.js", "JavaScript", 0, 0, 1503, 0, 0, 0, 0, 0, 0, 0, 37, 1, 1541
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/mouseHandler.js", "JavaScript", 0, 0, 439, 0, 0, 0, 0, 0, 0, 0, 26, 1, 466
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/mouseTarget.js", "JavaScript", 0, 0, 746, 0, 0, 0, 0, 0, 0, 0, 73, 1, 820
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/pointerHandler.js", "JavaScript", 0, 0, 110, 0, 0, 0, 0, 0, 0, 0, 10, 1, 121
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/textAreaHandler.css", "CSS", 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 17, 2, 35
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/textAreaHandler.js", "JavaScript", 0, 0, 498, 0, 0, 0, 0, 0, 0, 0, 46, 1, 545
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/textAreaInput.js", "JavaScript", 0, 0, 510, 0, 0, 0, 0, 0, 0, 0, 106, 1, 617
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/controller/textAreaState.js", "JavaScript", 0, 0, 242, 0, 0, 0, 0, 0, 0, 0, 29, 1, 272
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/core/editorState.js", "JavaScript", 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 12, 1, 147
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/core/keybindingCancellation.js", "JavaScript", 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 6, 1, 79
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/core/markdownRenderer.js", "JavaScript", 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 12, 1, 110
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/editorBrowser.js", "JavaScript", 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 13, 1, 40
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/editorDom.js", "JavaScript", 0, 0, 222, 0, 0, 0, 0, 0, 0, 0, 26, 1, 249
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/editorExtensions.js", "JavaScript", 0, 0, 416, 0, 0, 0, 0, 0, 0, 0, 25, 1, 442
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/abstractCodeEditorService.js", "JavaScript", 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 5, 1, 79
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/bulkEditService.js", "JavaScript", 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 4, 1, 47
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/codeEditorService.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/codeEditorServiceImpl.js", "JavaScript", 0, 0, 39, 0, 0, 0, 0, 0, 0, 0, 0, 1, 40
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/markerDecorations.js", "JavaScript", 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 5, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/services/openerService.js", "JavaScript", 0, 0, 226, 0, 0, 0, 0, 0, 0, 0, 27, 1, 254
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/domLineBreaksComputer.js", "JavaScript", 0, 0, 272, 0, 0, 0, 0, 0, 0, 0, 16, 1, 289
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/dynamicViewOverlay.js", "JavaScript", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 4, 1, 8
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewController.js", "JavaScript", 0, 0, 257, 0, 0, 0, 0, 0, 0, 0, 7, 1, 265
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewImpl.js", "JavaScript", 0, 0, 381, 0, 0, 0, 0, 0, 0, 0, 27, 1, 409
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewLayer.js", "JavaScript", 0, 0, 416, 0, 0, 0, 0, 0, 0, 0, 47, 1, 464
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewOverlays.js", "JavaScript", 0, 0, 192, 0, 0, 0, 0, 0, 0, 0, 14, 1, 207
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewPart.js", "JavaScript", 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 4, 1, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/view/viewUserInputEvents.js", "JavaScript", 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 4, 1, 105
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/contentWidgets/contentWidgets.js", "JavaScript", 0, 0, 413, 0, 0, 0, 0, 0, 0, 0, 32, 1, 446
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.css", "CSS", 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 4, 4, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight.js", "JavaScript", 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 7, 1, 181
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/decorations/decorations.css", "CSS", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 8, 1, 12
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/decorations/decorations.js", "JavaScript", 0, 0, 182, 0, 0, 0, 0, 0, 0, 0, 13, 1, 196
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/editorScrollbar/editorScrollbar.js", "JavaScript", 0, 0, 169, 0, 0, 0, 0, 0, 0, 0, 12, 3, 184
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.css", "CSS", 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 8, 3, 21
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin/glyphMargin.js", "JavaScript", 0, 0, 149, 0, 0, 0, 0, 0, 0, 0, 6, 1, 156
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.css", "CSS", 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 2, 10
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides/indentGuides.js", "JavaScript", 0, 0, 220, 0, 0, 0, 0, 0, 0, 0, 8, 1, 229
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.css", "CSS", 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 4, 4, 26
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers/lineNumbers.js", "JavaScript", 0, 0, 154, 0, 0, 0, 0, 0, 0, 0, 8, 1, 163
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/rangeUtil.js", "JavaScript", 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 16, 1, 112
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLine.js", "JavaScript", 0, 0, 445, 0, 0, 0, 0, 0, 0, 0, 63, 1, 509
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLines.css", "CSS", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 20, 8, 49
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/lines/viewLines.js", "JavaScript", 0, 0, 577, 0, 0, 0, 0, 0, 0, 0, 73, 1, 651
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.css", "CSS", 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 8, 1, 18
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations/linesDecorations.js", "JavaScript", 0, 0, 88, 0, 0, 0, 0, 0, 0, 0, 6, 1, 95
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/margin/margin.js", "JavaScript", 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 7, 1, 63
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.css", "CSS", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 8, 1, 15
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations/marginDecorations.js", "JavaScript", 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 6, 1, 80
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimap.css", "CSS", 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 6, 3, 33
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimap.js", "JavaScript", 0, 0, 1331, 0, 0, 0, 0, 0, 0, 0, 107, 1, 1439
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRenderer.js", "JavaScript", 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 4, 1, 88
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharRendererFactory.js", "JavaScript", 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 36, 1, 134
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapCharSheet.js", "JavaScript", 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 5, 1, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/minimap/minimapPreBaked.js", "JavaScript", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 19, 1, 50
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.css", "CSS", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 4, 0, 9
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets/overlayWidgets.js", "JavaScript", 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 8, 1, 106
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler.js", "JavaScript", 0, 0, 341, 0, 0, 0, 0, 0, 0, 0, 16, 1, 358
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler/overviewRuler.js", "JavaScript", 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 6, 1, 128
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/rulers/rulers.css", "CSS", 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 1, 9
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/rulers/rulers.js", "JavaScript", 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 8, 1, 84
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.css", "CSS", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 4, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration/scrollDecoration.js", "JavaScript", 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 7, 1, 76
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/selections/selections.css", "CSS", 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 8, 4, 23
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/selections/selections.js", "JavaScript", 0, 0, 314, 0, 0, 0, 0, 0, 0, 0, 23, 1, 338
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursor.js", "JavaScript", 0, 0, 152, 0, 0, 0, 0, 0, 0, 0, 10, 1, 163
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.css", "CSS", 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 8, 12, 88
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors/viewCursors.js", "JavaScript", 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 15, 1, 317
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/viewParts/viewZones/viewZones.js", "JavaScript", 0, 0, 318, 0, 0, 0, 0, 0, 0, 0, 11, 1, 330
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/codeEditorWidget.js", "JavaScript", 0, 0, 1497, 0, 0, 0, 0, 0, 0, 0, 19, 1, 1517
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/diffEditorWidget.js", "JavaScript", 0, 0, 1881, 0, 0, 0, 0, 0, 0, 0, 63, 1, 1945
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/diffNavigator.js", "JavaScript", 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 16, 1, 170
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/diffReview.js", "JavaScript", 0, 0, 712, 0, 0, 0, 0, 0, 0, 0, 16, 1, 729
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/embeddedCodeEditorWidget.js", "JavaScript", 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 5, 1, 54
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/inlineDiffMargin.js", "JavaScript", 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 8, 1, 187
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/media/diffEditor.css", "CSS", 0, 0, 0, 0, 43, 0, 0, 0, 0, 0, 8, 12, 63
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/media/diffReview.css", "CSS", 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 4, 12, 62
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/browser/widget/media/editor.css", "CSS", 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 14, 10, 43
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/commands/replaceCommand.js", "JavaScript", 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 4, 1, 83
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/commands/shiftCommand.js", "JavaScript", 0, 0, 200, 0, 0, 0, 0, 0, 0, 0, 23, 1, 224
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/commands/surroundSelectionCommand.js", "JavaScript", 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 4, 1, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/commands/trimTrailingWhitespaceCommand.js", "JavaScript", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 14, 1, 82
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/config/commonEditorConfig.js", "JavaScript", 0, 0, 550, 0, 0, 0, 0, 0, 0, 0, 29, 1, 580
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/config/editorOptions.js", "JavaScript", 0, 0, 2362, 0, 0, 0, 0, 0, 0, 0, 166, 1, 2529
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/config/editorZoom.js", "JavaScript", 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 4, 1, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/config/fontInfo.js", "JavaScript", 0, 0, 102, 0, 0, 0, 0, 0, 0, 0, 37, 1, 140
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursor.js", "JavaScript", 0, 0, 776, 0, 0, 0, 0, 0, 0, 0, 63, 1, 840
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorAtomicMoveOperations.js", "JavaScript", 0, 0, 103, 0, 0, 0, 0, 0, 0, 0, 39, 1, 143
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorCollection.js", "JavaScript", 0, 0, 231, 0, 0, 0, 0, 0, 0, 0, 11, 1, 243
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorColumnSelection.js", "JavaScript", 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 7, 1, 91
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorColumns.js", "JavaScript", 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 20, 1, 132
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorCommon.js", "JavaScript", 0, 0, 216, 0, 0, 0, 0, 0, 0, 0, 9, 1, 226
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorDeleteOperations.js", "JavaScript", 0, 0, 199, 0, 0, 0, 0, 0, 0, 0, 15, 1, 215
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorEvents.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorMoveCommands.js", "JavaScript", 0, 0, 601, 0, 0, 0, 0, 0, 0, 0, 40, 1, 642
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorMoveOperations.js", "JavaScript", 0, 0, 246, 0, 0, 0, 0, 0, 0, 0, 25, 1, 272
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorTypeOperations.js", "JavaScript", 0, 0, 827, 0, 0, 0, 0, 0, 0, 0, 78, 1, 906
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/cursorWordOperations.js", "JavaScript", 0, 0, 643, 0, 0, 0, 0, 0, 0, 0, 35, 1, 679
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/oneCursor.js", "JavaScript", 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 13, 1, 111
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/controller/wordCharacterClassifier.js", "JavaScript", 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 4, 1, 26
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/characterClassifier.js", "JavaScript", 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 7, 1, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/editOperation.js", "JavaScript", 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 4, 1, 34
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/lineTokens.js", "JavaScript", 0, 0, 204, 0, 0, 0, 0, 0, 0, 0, 15, 1, 220
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/position.js", "JavaScript", 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 57, 1, 135
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/range.js", "JavaScript", 0, 0, 266, 0, 0, 0, 0, 0, 0, 0, 110, 1, 377
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/rgba.js", "JavaScript", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 8, 1, 34
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/selection.js", "JavaScript", 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 51, 1, 143
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/stringBuilder.js", "JavaScript", 0, 0, 136, 0, 0, 0, 0, 0, 0, 0, 10, 1, 147
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/core/token.js", "JavaScript", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 4, 1, 30
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/diff/diffComputer.js", "JavaScript", 0, 0, 378, 0, 0, 0, 0, 0, 0, 0, 20, 1, 399
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/editorAction.js", "JavaScript", 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 4, 1, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/editorCommon.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 3, 1, 8
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/editorContextKeys.js", "JavaScript", 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 20, 1, 65
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model.js", "JavaScript", 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 37, 1, 122
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairs.js", "JavaScript", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 14, 1, 40
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsImpl.js", "JavaScript", 0, 0, 610, 0, 0, 0, 0, 0, 0, 0, 44, 1, 655
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/ast.js", "JavaScript", 0, 0, 423, 0, 0, 0, 0, 0, 0, 0, 44, 1, 468
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/beforeEditPositionMapper.js", "JavaScript", 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 17, 1, 93
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/bracketPairsTree.js", "JavaScript", 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 13, 1, 177
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/brackets.js", "JavaScript", 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 8, 1, 103
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/concat23Trees.js", "JavaScript", 0, 0, 147, 0, 0, 0, 0, 0, 0, 0, 42, 1, 190
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/length.js", "JavaScript", 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 33, 1, 121
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/nodeReader.js", "JavaScript", 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 29, 1, 125
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/parser.js", "JavaScript", 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 13, 1, 106
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet.js", "JavaScript", 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 15, 1, 105
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree/tokenizer.js", "JavaScript", 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 41, 1, 296
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/bracketPairs/colorizedBracketPairsDecorationProvider.js", "JavaScript", 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 6, 1, 90
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/decorationProvider.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/editStack.js", "JavaScript", 0, 0, 352, 0, 0, 0, 0, 0, 0, 0, 7, 1, 360
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/indentationGuesser.js", "JavaScript", 0, 0, 138, 0, 0, 0, 0, 0, 0, 0, 38, 1, 177
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/intervalTree.js", "JavaScript", 0, 0, 860, 0, 0, 0, 0, 0, 0, 0, 112, 1, 973
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/mirrorTextModel.js", "JavaScript", 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 20, 1, 115
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase.js", "JavaScript", 0, 0, 1366, 0, 0, 0, 0, 0, 0, 0, 86, 1, 1453
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer.js", "JavaScript", 0, 0, 408, 0, 0, 0, 0, 0, 0, 0, 37, 1, 446
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder.js", "JavaScript", 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 13, 1, 139
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase.js", "JavaScript", 0, 0, 344, 0, 0, 0, 0, 0, 0, 0, 16, 1, 361
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textChange.js", "JavaScript", 0, 0, 237, 0, 0, 0, 0, 0, 0, 0, 6, 1, 244
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textModel.js", "JavaScript", 0, 0, 2301, 0, 0, 0, 0, 0, 0, 0, 145, 1, 2447
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textModelEvents.js", "JavaScript", 0, 0, 135, 0, 0, 0, 0, 0, 0, 0, 39, 1, 175
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textModelSearch.js", "JavaScript", 0, 0, 413, 0, 0, 0, 0, 0, 0, 0, 43, 1, 457
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/textModelTokens.js", "JavaScript", 0, 0, 381, 0, 0, 0, 0, 0, 0, 0, 15, 1, 397
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/tokensStore.js", "JavaScript", 0, 0, 873, 0, 0, 0, 0, 0, 0, 0, 151, 1, 1025
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/model/wordHelper.js", "JavaScript", 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 19, 1, 117
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes.js", "JavaScript", 0, 0, 270, 0, 0, 0, 0, 0, 0, 0, 145, 1, 416
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/languageConfiguration.js", "JavaScript", 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 32, 1, 126
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/languageConfigurationRegistry.js", "JavaScript", 0, 0, 748, 0, 0, 0, 0, 0, 0, 0, 63, 1, 812
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/languageFeatureRegistry.js", "JavaScript", 0, 0, 182, 0, 0, 0, 0, 0, 0, 0, 13, 1, 196
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/languageSelector.js", "JavaScript", 0, 0, 79, 0, 0, 0, 0, 0, 0, 0, 14, 1, 94
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/linkComputer.js", "JavaScript", 0, 0, 240, 0, 0, 0, 0, 0, 0, 0, 25, 1, 266
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/modesRegistry.js", "JavaScript", 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 6, 1, 79
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/nullMode.js", "JavaScript", 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 4, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports.js", "JavaScript", 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 4, 1, 50
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/characterPair.js", "JavaScript", 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 9, 1, 57
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/electricCharacter.js", "JavaScript", 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 5, 1, 53
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/indentRules.js", "JavaScript", 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 8, 1, 62
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/inplaceReplaceSupport.js", "JavaScript", 0, 0, 79, 0, 0, 0, 0, 0, 0, 0, 6, 1, 86
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/onEnter.js", "JavaScript", 0, 0, 99, 0, 0, 0, 0, 0, 0, 0, 7, 1, 107
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/richEditBrackets.js", "JavaScript", 0, 0, 262, 0, 0, 0, 0, 0, 0, 0, 100, 1, 363
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/supports/tokenization.js", "JavaScript", 0, 0, 263, 0, 0, 0, 0, 0, 0, 0, 15, 1, 279
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/textToHtmlTokenizer.js", "JavaScript", 0, 0, 114, 0, 0, 0, 0, 0, 0, 0, 5, 1, 120
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/tokenizationRegistry.js", "JavaScript", 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 4, 1, 80
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/modes/unicodeTextModelHighlighter.js", "JavaScript", 0, 0, 145, 0, 0, 0, 0, 0, 0, 0, 7, 1, 153
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js", "JavaScript", 0, 0, 446, 0, 0, 0, 0, 0, 0, 0, 45, 1, 492
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/editorWorkerService.js", "JavaScript", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 4, 1, 8
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/editorWorkerServiceImpl.js", "JavaScript", 0, 0, 416, 0, 0, 0, 0, 0, 0, 0, 22, 1, 439
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/getIconClasses.js", "JavaScript", 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 17, 1, 81
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/getSemanticTokens.js", "JavaScript", 0, 0, 198, 0, 0, 0, 0, 0, 0, 0, 19, 1, 218
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js", "JavaScript", 0, 0, 237, 0, 0, 0, 0, 0, 0, 0, 12, 1, 250
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/markerDecorationsServiceImpl.js", "JavaScript", 0, 0, 187, 0, 0, 0, 0, 0, 0, 0, 8, 1, 196
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/markersDecorationService.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/modeService.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/modeServiceImpl.js", "JavaScript", 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 8, 1, 102
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/modelService.js", "JavaScript", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 4, 1, 10
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/modelServiceImpl.js", "JavaScript", 0, 0, 713, 0, 0, 0, 0, 0, 0, 0, 31, 1, 745
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/resolverService.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js", "JavaScript", 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 6, 1, 80
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js", "JavaScript", 0, 0, 247, 0, 0, 0, 0, 0, 0, 0, 12, 1, 260
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/textResourceConfigurationService.js", "JavaScript", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/services/webWorker.js", "JavaScript", 0, 0, 56, 0, 0, 0, 0, 0, 0, 0, 9, 1, 66
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/standalone/standaloneBase.js", "JavaScript", 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 4, 1, 41
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/standalone/standaloneEnums.js", "JavaScript", 0, 0, 530, 0, 0, 0, 0, 0, 0, 0, 328, 1, 859
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/standaloneStrings.js", "JavaScript", 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 4, 1, 67
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/editorColorRegistry.js", "JavaScript", 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 8, 1, 99
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/overviewZoneManager.js", "JavaScript", 0, 0, 159, 0, 0, 0, 0, 0, 0, 0, 7, 1, 167
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/renderingContext.js", "JavaScript", 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 4, 1, 94
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/viewContext.js", "JavaScript", 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 4, 1, 33
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/view/viewEvents.js", "JavaScript", 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 8, 1, 135
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/lineDecorations.js", "JavaScript", 0, 0, 189, 0, 0, 0, 0, 0, 0, 0, 15, 1, 205
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/linesLayout.js", "JavaScript", 0, 0, 600, 0, 0, 0, 0, 0, 0, 0, 154, 1, 755
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLayout.js", "JavaScript", 0, 0, 315, 0, 0, 0, 0, 0, 0, 0, 17, 1, 333
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLineRenderer.js", "JavaScript", 0, 0, 799, 0, 0, 0, 0, 0, 0, 0, 76, 1, 876
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewLayout/viewLinesViewportData.js", "JavaScript", 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 7, 1, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/minimapTokensColorTracker.js", "JavaScript", 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 6, 1, 56
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjection.js", "JavaScript", 0, 0, 297, 0, 0, 0, 0, 0, 0, 0, 20, 1, 318
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/modelLineProjectionData.js", "JavaScript", 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 49, 1, 269
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/monospaceLineBreaksComputer.js", "JavaScript", 0, 0, 394, 0, 0, 0, 0, 0, 0, 0, 46, 1, 441
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/prefixSumComputer.js", "JavaScript", 0, 0, 196, 0, 0, 0, 0, 0, 0, 0, 27, 1, 224
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewEventHandler.js", "JavaScript", 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 6, 1, 184
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModel.js", "JavaScript", 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 10, 1, 111
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelDecorations.js", "JavaScript", 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 11, 1, 152
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelEventDispatcher.js", "JavaScript", 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 8, 1, 274
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelImpl.js", "JavaScript", 0, 0, 887, 0, 0, 0, 0, 0, 0, 0, 36, 1, 924
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/common/viewModel/viewModelLines.js", "JavaScript", 0, 0, 855, 0, 0, 0, 0, 0, 0, 0, 72, 1, 928
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/anchorSelect/anchorSelect.css", "CSS", 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 2, 10
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/anchorSelect/anchorSelect.js", "JavaScript", 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 4, 1, 178
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/bracketMatching/bracketMatching.css", "CSS", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 4, 2, 9
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/bracketMatching/bracketMatching.js", "JavaScript", 0, 0, 308, 0, 0, 0, 0, 0, 0, 0, 16, 1, 325
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/caretOperations/caretOperations.js", "JavaScript", 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 4, 1, 50
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/caretOperations/moveCaretCommand.js", "JavaScript", 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 4, 1, 47
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/caretOperations/transpose.js", "JavaScript", 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 7, 1, 66
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/clipboard/clipboard.js", "JavaScript", 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 21, 1, 241
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeAction.js", "JavaScript", 0, 0, 214, 0, 0, 0, 0, 0, 0, 0, 8, 1, 223
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionCommands.js", "JavaScript", 0, 0, 349, 0, 0, 0, 0, 0, 0, 0, 4, 1, 354
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionContributions.js", "JavaScript", 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 4, 1, 15
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionMenu.js", "JavaScript", 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 10, 1, 191
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionModel.js", "JavaScript", 0, 0, 223, 0, 0, 0, 0, 0, 0, 0, 10, 1, 234
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/codeActionUi.js", "JavaScript", 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 8, 1, 164
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/lightBulbWidget.css", "CSS", 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 4, 3, 17
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/lightBulbWidget.js", "JavaScript", 0, 0, 202, 0, 0, 0, 0, 0, 0, 0, 18, 1, 221
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codeAction/types.js", "JavaScript", 0, 0, 103, 0, 0, 0, 0, 0, 0, 0, 9, 1, 113
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codeLensCache.js", "JavaScript", 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 11, 1, 112
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codelens.js", "JavaScript", 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 7, 1, 112
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensController.js", "JavaScript", 0, 0, 434, 0, 0, 0, 0, 0, 0, 0, 23, 1, 458
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensWidget.css", "CSS", 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 4, 10, 54
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/codelens/codelensWidget.js", "JavaScript", 0, 0, 248, 0, 0, 0, 0, 0, 0, 0, 11, 1, 260
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/color.js", "JavaScript", 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 4, 1, 71
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorContributions.js", "JavaScript", 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 5, 1, 42
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorDetector.js", "JavaScript", 0, 0, 196, 0, 0, 0, 0, 0, 0, 0, 5, 1, 202
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPicker.css", "CSS", 0, 0, 0, 0, 114, 0, 0, 0, 0, 0, 7, 24, 145
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPickerModel.js", "JavaScript", 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 4, 1, 58
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/colorPicker/colorPickerWidget.js", "JavaScript", 0, 0, 259, 0, 0, 0, 0, 0, 0, 0, 6, 1, 266
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/comment/blockCommentCommand.js", "JavaScript", 0, 0, 128, 0, 0, 0, 0, 0, 0, 0, 18, 1, 147
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/comment/comment.js", "JavaScript", 0, 0, 135, 0, 0, 0, 0, 0, 0, 0, 8, 1, 144
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/comment/lineCommentCommand.js", "JavaScript", 0, 0, 279, 0, 0, 0, 0, 0, 0, 0, 44, 1, 324
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/contextmenu/contextmenu.js", "JavaScript", 0, 0, 224, 0, 0, 0, 0, 0, 0, 0, 20, 1, 245
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/cursorUndo/cursorUndo.js", "JavaScript", 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 5, 1, 133
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/dnd/dnd.css", "CSS", 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 4, 2, 28
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/dnd/dnd.js", "JavaScript", 0, 0, 190, 0, 0, 0, 0, 0, 0, 0, 6, 1, 197
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/dnd/dragAndDropCommand.js", "JavaScript", 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 9, 1, 63
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/documentSymbols/documentSymbols.js", "JavaScript", 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 4, 1, 46
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/documentSymbols/outlineModel.js", "JavaScript", 0, 0, 235, 0, 0, 0, 0, 0, 0, 0, 13, 1, 249
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findController.js", "JavaScript", 0, 0, 869, 0, 0, 0, 0, 0, 0, 0, 23, 1, 893
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findDecorations.js", "JavaScript", 0, 0, 275, 0, 0, 0, 0, 0, 0, 0, 14, 1, 290
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findModel.js", "JavaScript", 0, 0, 448, 0, 0, 0, 0, 0, 0, 0, 30, 1, 479
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findOptionsWidget.js", "JavaScript", 0, 0, 166, 0, 0, 0, 0, 0, 0, 0, 5, 1, 172
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findState.js", "JavaScript", 0, 0, 215, 0, 0, 0, 0, 0, 0, 0, 5, 1, 221
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findWidget.css", "CSS", 0, 0, 0, 0, 176, 0, 0, 0, 0, 0, 12, 37, 225
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/findWidget.js", "JavaScript", 0, 0, 1132, 0, 0, 0, 0, 0, 0, 0, 57, 1, 1190
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/replaceAllCommand.js", "JavaScript", 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 8, 1, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/find/replacePattern.js", "JavaScript", 0, 0, 235, 0, 0, 0, 0, 0, 0, 0, 51, 1, 287
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/folding.css", "CSS", 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 4, 4, 32
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/folding.js", "JavaScript", 0, 0, 948, 0, 0, 0, 0, 0, 0, 0, 22, 1, 971
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/foldingDecorations.js", "JavaScript", 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 4, 1, 70
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/foldingModel.js", "JavaScript", 0, 0, 426, 0, 0, 0, 0, 0, 0, 0, 86, 1, 513
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/foldingRanges.js", "JavaScript", 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 4, 1, 168
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/hiddenRangeModel.js", "JavaScript", 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 9, 1, 144
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/indentRangeProvider.js", "JavaScript", 0, 0, 142, 0, 0, 0, 0, 0, 0, 0, 17, 1, 160
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/intializingRangeProvider.js", "JavaScript", 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 4, 1, 53
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/folding/syntaxRangeProvider.js", "JavaScript", 0, 0, 162, 0, 0, 0, 0, 0, 0, 0, 4, 1, 167
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/fontZoom/fontZoom.js", "JavaScript", 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 4, 1, 50
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/format/format.js", "JavaScript", 0, 0, 380, 0, 0, 0, 0, 0, 0, 0, 15, 1, 396
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/format/formatActions.js", "JavaScript", 0, 0, 244, 0, 0, 0, 0, 0, 0, 0, 21, 1, 266
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/format/formattingEdit.js", "JavaScript", 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 5, 1, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoError/gotoError.js", "JavaScript", 0, 0, 273, 0, 0, 0, 0, 0, 0, 0, 9, 1, 283
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoError/gotoErrorWidget.js", "JavaScript", 0, 0, 331, 0, 0, 0, 0, 0, 0, 0, 10, 1, 342
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoError/markerNavigationService.js", "JavaScript", 0, 0, 184, 0, 0, 0, 0, 0, 0, 0, 6, 1, 191
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoError/media/gotoErrorWidget.css", "CSS", 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 6, 16, 80
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/goToCommands.js", "JavaScript", 0, 0, 680, 0, 0, 0, 0, 0, 0, 0, 28, 1, 709
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/goToSymbol.js", "JavaScript", 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 6, 1, 89
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/clickLinkGesture.js", "JavaScript", 0, 0, 130, 0, 0, 0, 0, 0, 0, 0, 14, 1, 145
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.css", "CSS", 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 1, 9
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.js", "JavaScript", 0, 0, 280, 0, 0, 0, 0, 0, 0, 0, 21, 1, 302
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesController.js", "JavaScript", 0, 0, 369, 0, 0, 0, 0, 0, 0, 0, 22, 1, 392
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesTree.js", "JavaScript", 0, 0, 184, 0, 0, 0, 0, 0, 0, 0, 18, 1, 203
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesWidget.css", "CSS", 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 6, 17, 85
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek/referencesWidget.js", "JavaScript", 0, 0, 469, 0, 0, 0, 0, 0, 0, 0, 25, 1, 495
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/referencesModel.js", "JavaScript", 0, 0, 243, 0, 0, 0, 0, 0, 0, 0, 8, 1, 252
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/symbolNavigation.js", "JavaScript", 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 8, 1, 179
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/colorHoverParticipant.js", "JavaScript", 0, 0, 146, 0, 0, 0, 0, 0, 0, 0, 8, 1, 155
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/getHover.js", "JavaScript", 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 4, 1, 54
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/hover.js", "JavaScript", 0, 0, 286, 0, 0, 0, 0, 0, 0, 0, 12, 1, 299
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/hoverOperation.js", "JavaScript", 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 4, 1, 163
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/hoverTypes.js", "JavaScript", 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 4, 1, 32
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/markdownHoverParticipant.js", "JavaScript", 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 5, 1, 133
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/markerHoverParticipant.js", "JavaScript", 0, 0, 224, 0, 0, 0, 0, 0, 0, 0, 8, 1, 233
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/modesContentHover.js", "JavaScript", 0, 0, 467, 0, 0, 0, 0, 0, 0, 0, 24, 1, 492
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/hover/modesGlyphHover.js", "JavaScript", 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 7, 1, 171
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace/inPlaceReplace.js", "JavaScript", 0, 0, 148, 0, 0, 0, 0, 0, 0, 0, 13, 1, 162
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace/inPlaceReplaceCommand.js", "JavaScript", 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 5, 1, 25
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/indentation/indentUtils.js", "JavaScript", 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 4, 1, 32
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/indentation/indentation.js", "JavaScript", 0, 0, 551, 0, 0, 0, 0, 0, 0, 0, 25, 1, 577
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlayHints/inlayHintsController.js", "JavaScript", 0, 0, 305, 0, 0, 0, 0, 0, 0, 0, 10, 1, 316
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/consts.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostText.css", "CSS", 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 4, 7, 34
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostText.js", "JavaScript", 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 7, 1, 86
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextController.js", "JavaScript", 0, 0, 269, 0, 0, 0, 0, 0, 0, 0, 10, 1, 280
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextModel.js", "JavaScript", 0, 0, 152, 0, 0, 0, 0, 0, 0, 0, 7, 1, 160
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/ghostTextWidget.js", "JavaScript", 0, 0, 320, 0, 0, 0, 0, 0, 0, 0, 7, 1, 328
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText.js", "JavaScript", 0, 0, 136, 0, 0, 0, 0, 0, 0, 0, 32, 1, 169
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant.js", "JavaScript", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 7, 1, 164
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/inlineCompletionsModel.js", "JavaScript", 0, 0, 482, 0, 0, 0, 0, 0, 0, 0, 28, 1, 511
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/suggestWidgetInlineCompletionProvider.js", "JavaScript", 0, 0, 194, 0, 0, 0, 0, 0, 0, 0, 15, 1, 210
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/suggestWidgetPreviewModel.js", "JavaScript", 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 7, 1, 134
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/inlineCompletions/utils.js", "JavaScript", 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 4, 1, 26
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/lineSelection/lineSelection.js", "JavaScript", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 4, 1, 35
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linesOperations/copyLinesCommand.js", "JavaScript", 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 5, 1, 71
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linesOperations/linesOperations.js", "JavaScript", 0, 0, 967, 0, 0, 0, 0, 0, 0, 0, 26, 1, 994
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linesOperations/moveLinesCommand.js", "JavaScript", 0, 0, 298, 0, 0, 0, 0, 0, 0, 0, 38, 1, 337
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linesOperations/sortLinesCommand.js", "JavaScript", 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 10, 1, 84
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/linkedEditing/linkedEditing.js", "JavaScript", 0, 0, 353, 0, 0, 0, 0, 0, 0, 0, 12, 1, 366
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/links/getLinks.js", "JavaScript", 0, 0, 146, 0, 0, 0, 0, 0, 0, 0, 13, 1, 160
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/links/links.css", "CSS", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 4, 2, 14
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/links/links.js", "JavaScript", 0, 0, 368, 0, 0, 0, 0, 0, 0, 0, 12, 1, 381
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/message/messageController.css", "CSS", 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 4, 12, 71
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/message/messageController.js", "JavaScript", 0, 0, 138, 0, 0, 0, 0, 0, 0, 0, 11, 1, 150
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/multicursor/multicursor.js", "JavaScript", 0, 0, 900, 0, 0, 0, 0, 0, 0, 0, 39, 1, 940
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHints.css", "CSS", 0, 0, 0, 0, 86, 0, 0, 0, 0, 0, 4, 21, 111
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHints.js", "JavaScript", 0, 0, 106, 0, 0, 0, 0, 0, 0, 0, 4, 1, 111
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHintsModel.js", "JavaScript", 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 11, 1, 262
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/parameterHintsWidget.js", "JavaScript", 0, 0, 354, 0, 0, 0, 0, 0, 0, 0, 8, 1, 363
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/parameterHints/provideSignatureHelp.js", "JavaScript", 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 4, 1, 66
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/peekView/media/peekViewWidget.css", "CSS", 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 4, 15, 74
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/peekView/peekView.js", "JavaScript", 0, 0, 228, 0, 0, 0, 0, 0, 0, 0, 6, 1, 235
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/quickAccess/commandsQuickAccess.js", "JavaScript", 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 4, 1, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/quickAccess/editorNavigationQuickAccess.js", "JavaScript", 0, 0, 112, 0, 0, 0, 0, 0, 0, 0, 35, 1, 148
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/quickAccess/gotoLineQuickAccess.js", "JavaScript", 0, 0, 112, 0, 0, 0, 0, 0, 0, 0, 15, 1, 128
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/quickAccess/gotoSymbolQuickAccess.js", "JavaScript", 0, 0, 339, 0, 0, 0, 0, 0, 0, 0, 51, 1, 391
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/rename/rename.js", "JavaScript", 0, 0, 341, 0, 0, 0, 0, 0, 0, 0, 12, 1, 354
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/rename/renameInputField.css", "CSS", 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 4, 6, 28
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/rename/renameInputField.js", "JavaScript", 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 7, 1, 181
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/smartSelect/bracketSelections.js", "JavaScript", 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 15, 1, 156
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/smartSelect/smartSelect.js", "JavaScript", 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 21, 1, 276
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/smartSelect/wordSelections.js", "JavaScript", 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 10, 1, 75
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetController2.js", "JavaScript", 0, 0, 210, 0, 0, 0, 0, 0, 0, 0, 26, 1, 237
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetParser.js", "JavaScript", 0, 0, 816, 0, 0, 0, 0, 0, 0, 0, 52, 1, 869
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetSession.css", "CSS", 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 4, 3, 20
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetSession.js", "JavaScript", 0, 0, 425, 0, 0, 0, 0, 0, 0, 0, 86, 1, 512
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/snippet/snippetVariables.js", "JavaScript", 0, 0, 302, 0, 0, 0, 0, 0, 0, 0, 18, 1, 321
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/completionModel.js", "JavaScript", 0, 0, 197, 0, 0, 0, 0, 0, 0, 0, 37, 1, 235
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/media/suggest.css", "CSS", 0, 0, 0, 0, 337, 0, 0, 0, 0, 0, 20, 97, 454
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/resizable.js", "JavaScript", 0, 0, 143, 0, 0, 0, 0, 0, 0, 0, 4, 1, 148
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggest.js", "JavaScript", 0, 0, 294, 0, 0, 0, 0, 0, 0, 0, 27, 1, 322
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestAlternatives.js", "JavaScript", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 7, 1, 93
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestCommitCharacters.js", "JavaScript", 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 7, 1, 49
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestController.js", "JavaScript", 0, 0, 773, 0, 0, 0, 0, 0, 0, 0, 48, 1, 822
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestMemory.js", "JavaScript", 0, 0, 236, 0, 0, 0, 0, 0, 0, 0, 14, 1, 251
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestModel.js", "JavaScript", 0, 0, 572, 0, 0, 0, 0, 0, 0, 0, 69, 1, 642
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestOvertypingCapturer.js", "JavaScript", 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 6, 1, 60
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidget.js", "JavaScript", 0, 0, 777, 0, 0, 0, 0, 0, 0, 0, 33, 1, 811
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetDetails.js", "JavaScript", 0, 0, 367, 0, 0, 0, 0, 0, 0, 0, 11, 1, 379
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetRenderer.js", "JavaScript", 0, 0, 204, 0, 0, 0, 0, 0, 0, 0, 8, 1, 213
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/suggestWidgetStatus.js", "JavaScript", 0, 0, 82, 0, 0, 0, 0, 0, 0, 0, 4, 1, 87
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/wordContextKey.js", "JavaScript", 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 5, 1, 66
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/suggest/wordDistance.js", "JavaScript", 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 5, 1, 73
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/symbolIcons/symbolIcons.js", "JavaScript", 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 4, 1, 309
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.js", "JavaScript", 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 4, 1, 38
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/tokenization/tokenization.js", "JavaScript", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 4, 1, 30
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/bannerController.css", "CSS", 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 4, 16, 86
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/bannerController.js", "JavaScript", 0, 0, 106, 0, 0, 0, 0, 0, 0, 0, 11, 1, 118
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.css", "CSS", 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 4, 2, 10
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.js", "JavaScript", 0, 0, 575, 0, 0, 0, 0, 0, 0, 0, 13, 1, 589
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/unusualLineTerminators/unusualLineTerminators.js", "JavaScript", 0, 0, 99, 0, 0, 0, 0, 0, 0, 0, 9, 1, 109
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/viewportSemanticTokens/viewportSemanticTokens.js", "JavaScript", 0, 0, 112, 0, 0, 0, 0, 0, 0, 0, 4, 1, 117
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/wordHighlighter/wordHighlighter.js", "JavaScript", 0, 0, 519, 0, 0, 0, 0, 0, 0, 0, 38, 1, 558
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/wordOperations/wordOperations.js", "JavaScript", 0, 0, 435, 0, 0, 0, 0, 0, 0, 0, 7, 1, 443
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/wordPartOperations/wordPartOperations.js", "JavaScript", 0, 0, 136, 0, 0, 0, 0, 0, 0, 0, 6, 1, 143
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/zoneWidget/zoneWidget.css", "CSS", 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 4, 3, 18
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/contrib/zoneWidget/zoneWidget.js", "JavaScript", 0, 0, 378, 0, 0, 0, 0, 0, 0, 0, 17, 1, 396
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/edcore.main.js", "JavaScript", 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 4, 1, 16
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.all.js", "JavaScript", 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 6, 1, 56
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.api.d.ts", "TypeScript", 2976, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4449, 359, 7784
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.api.js", "JavaScript", 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 7, 1, 57
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.main.js", "JavaScript", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/editor.worker.js", "JavaScript", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 5, 1, 26
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.css", "CSS", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 4, 1, 10
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp.js", "JavaScript", 0, 0, 302, 0, 0, 0, 0, 0, 0, 0, 6, 1, 309
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/colorizer.js", "JavaScript", 0, 0, 150, 0, 0, 0, 0, 0, 0, 0, 7, 1, 158
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.css", "CSS", 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 4, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard.js", "JavaScript", 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 5, 1, 72
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens/inspectTokens.css", "CSS", 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 4, 8, 41
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens/inspectTokens.js", "JavaScript", 0, 0, 249, 0, 0, 0, 0, 0, 0, 0, 5, 1, 255
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess.js", "JavaScript", 0, 0, 81, 0, 0, 0, 0, 0, 0, 0, 4, 1, 86
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess.js", "JavaScript", 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 4, 1, 63
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess.js", "JavaScript", 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 4, 1, 71
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess.js", "JavaScript", 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 4, 1, 14
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInput.css", "CSS", 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 4, 9, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInputServiceImpl.js", "JavaScript", 0, 0, 114, 0, 0, 0, 0, 0, 0, 0, 7, 1, 122
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch.js", "JavaScript", 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 4, 1, 37
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/simpleServices.js", "JavaScript", 0, 0, 461, 0, 0, 0, 0, 0, 0, 0, 10, 1, 472
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standalone-tokens.css", "CSS", 0, 0, 0, 0, 190, 0, 0, 0, 0, 0, 36, 24, 250
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneCodeEditor.js", "JavaScript", 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 26, 1, 331
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneCodeServiceImpl.js", "JavaScript", 0, 0, 86, 0, 0, 0, 0, 0, 0, 0, 5, 1, 92
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneEditor.js", "JavaScript", 0, 0, 215, 0, 0, 0, 0, 0, 0, 0, 95, 1, 311
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneLanguages.js", "JavaScript", 0, 0, 334, 0, 0, 0, 0, 0, 0, 0, 125, 1, 460
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneServices.js", "JavaScript", 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 9, 1, 190
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/standaloneThemeServiceImpl.js", "JavaScript", 0, 0, 306, 0, 0, 0, 0, 0, 0, 0, 8, 1, 315
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast.js", "JavaScript", 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 5, 1, 32
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchCommon.js", "JavaScript", 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 37, 1, 125
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchCompile.js", "JavaScript", 0, 0, 445, 0, 0, 0, 0, 0, 0, 0, 70, 1, 516
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchLexer.js", "JavaScript", 0, 0, 680, 0, 0, 0, 0, 0, 0, 0, 55, 1, 736
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/monarch/monarchTypes.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/standaloneThemeService.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/editor/standalone/common/themes.js", "JavaScript", 0, 0, 169, 0, 0, 0, 0, 0, 0, 0, 10, 1, 180
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/css/css.worker.js", "JavaScript", 0, 0, 35424, 0, 0, 0, 0, 0, 0, 0, 51, 46, 35521
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/css/cssMode.js", "JavaScript", 0, 0, 1930, 0, 0, 0, 0, 0, 0, 0, 11, 7, 1948
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/css/monaco.contribution.js", "JavaScript", 0, 0, 116, 0, 0, 0, 0, 0, 0, 0, 8, 4, 128
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/html/html.worker.js", "JavaScript", 0, 0, 16029, 0, 0, 0, 0, 0, 0, 0, 41, 36, 16106
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/html/htmlMode.js", "JavaScript", 0, 0, 1838, 0, 0, 0, 0, 0, 0, 0, 11, 7, 1856
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/html/monaco.contribution.js", "JavaScript", 0, 0, 132, 0, 0, 0, 0, 0, 0, 0, 8, 4, 144
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/json/json.worker.js", "JavaScript", 0, 0, 7218, 0, 0, 0, 0, 0, 0, 0, 35, 30, 7283
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/json/jsonMode.js", "JavaScript", 0, 0, 2303, 0, 0, 0, 0, 0, 0, 0, 191, 12, 2506
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/json/monaco.contribution.js", "JavaScript", 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 8, 4, 103
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/typescript/monaco.contribution.js", "JavaScript", 0, 0, 239, 0, 0, 0, 0, 0, 0, 0, 9, 5, 253
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/typescript/ts.worker.js", "JavaScript", 0, 0, 142897, 0, 0, 0, 0, 0, 0, 0, 15166, 2323, 160386
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/language/typescript/tsMode.js", "JavaScript", 0, 0, 1286, 0, 0, 0, 0, 0, 0, 0, 13, 10, 1309
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/nls.js", "JavaScript", 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 4, 1, 21
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/accessibility/browser/accessibilityService.js", "JavaScript", 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 1, 49
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/accessibility/common/accessibility.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 4, 1, 9
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.css", "CSS", 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 4, 12, 65
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/actions/browser/menuEntryActionViewItem.js", "JavaScript", 0, 0, 397, 0, 0, 0, 0, 0, 0, 0, 17, 1, 415
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/actions/common/actions.js", "JavaScript", 0, 0, 204, 0, 0, 0, 0, 0, 0, 0, 12, 1, 217
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/actions/common/menuService.js", "JavaScript", 0, 0, 167, 0, 0, 0, 0, 0, 0, 0, 30, 1, 198
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/browser/contextScopedHistoryWidget.js", "JavaScript", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 4, 1, 90
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/browser/historyWidgetKeybindingHint.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 4, 1, 9
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/clipboard/browser/clipboardService.js", "JavaScript", 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 13, 1, 82
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/clipboard/common/clipboardService.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/commands/common/commands.js", "JavaScript", 0, 0, 81, 0, 0, 0, 0, 0, 0, 0, 7, 1, 89
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/configuration/common/configuration.js", "JavaScript", 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 8, 1, 84
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/configuration/common/configurationModels.js", "JavaScript", 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 9, 1, 376
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/configuration/common/configurationRegistry.js", "JavaScript", 0, 0, 279, 0, 0, 0, 0, 0, 0, 0, 9, 1, 289
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextkey/browser/contextKeyService.js", "JavaScript", 0, 0, 380, 0, 0, 0, 0, 0, 0, 0, 21, 1, 402
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextkey/common/contextkey.js", "JavaScript", 0, 0, 1170, 0, 0, 0, 0, 0, 0, 0, 38, 1, 1209
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextkey/common/contextkeys.js", "JavaScript", 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 4, 1, 16
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.css", "CSS", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 4, 3, 10
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuHandler.js", "JavaScript", 0, 0, 116, 0, 0, 0, 0, 0, 0, 0, 8, 1, 125
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextMenuService.js", "JavaScript", 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 5, 1, 53
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextView.js", "JavaScript", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 4, 1, 8
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/contextview/browser/contextViewService.js", "JavaScript", 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 5, 1, 67
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/dialogs/common/dialogs.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/editor/common/editor.js", "JavaScript", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 8, 1, 14
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/environment/common/environment.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/extensions/common/extensions.js", "JavaScript", 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 19, 1, 32
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/files/common/files.js", "JavaScript", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 3, 1, 10
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/descriptors.js", "JavaScript", 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 4, 1, 12
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/extensions.js", "JavaScript", 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 4, 1, 16
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/graph.js", "JavaScript", 0, 0, 79, 0, 0, 0, 0, 0, 0, 0, 9, 1, 89
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/instantiation.js", "JavaScript", 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 8, 1, 43
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/instantiationService.js", "JavaScript", 0, 0, 263, 0, 0, 0, 0, 0, 0, 0, 21, 1, 285
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/instantiation/common/serviceCollection.js", "JavaScript", 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 4, 1, 24
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/jsonschemas/common/jsonContributionRegistry.js", "JavaScript", 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 4, 1, 31
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/abstractKeybindingService.js", "JavaScript", 0, 0, 210, 0, 0, 0, 0, 0, 0, 0, 15, 1, 226
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/baseResolvedKeybinding.js", "JavaScript", 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 7, 1, 51
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/keybinding.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/keybindingResolver.js", "JavaScript", 0, 0, 223, 0, 0, 0, 0, 0, 0, 0, 26, 1, 250
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/keybindingsRegistry.js", "JavaScript", 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 8, 1, 130
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/resolvedKeybindingItem.js", "JavaScript", 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 6, 1, 35
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/keybinding/common/usLayoutResolvedKeybinding.js", "JavaScript", 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 10, 1, 166
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/label/common/label.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/layout/browser/layoutService.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/list/browser/listService.js", "JavaScript", 0, 0, 877, 0, 0, 0, 0, 0, 0, 0, 10, 1, 888
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/log/common/log.js", "JavaScript", 0, 0, 81, 0, 0, 0, 0, 0, 0, 0, 1, 1, 83
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/markers/common/markerService.js", "JavaScript", 0, 0, 243, 0, 0, 0, 0, 0, 0, 0, 12, 1, 256
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/markers/common/markers.js", "JavaScript", 0, 0, 110, 0, 0, 0, 0, 0, 0, 0, 6, 1, 117
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/notification/common/notification.js", "JavaScript", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/opener/browser/link.js", "JavaScript", 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 4, 1, 95
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/opener/common/opener.js", "JavaScript", 0, 0, 39, 0, 0, 0, 0, 0, 0, 0, 4, 1, 44
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/progress/common/progress.js", "JavaScript", 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/commandsQuickAccess.js", "JavaScript", 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 18, 1, 238
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/helpQuickAccess.js", "JavaScript", 0, 0, 72, 0, 0, 0, 0, 0, 0, 0, 10, 1, 83
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/pickerQuickAccess.js", "JavaScript", 0, 0, 208, 0, 0, 0, 0, 0, 0, 0, 42, 1, 251
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/quickAccess.js", "JavaScript", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 35, 1, 192
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/browser/quickInput.js", "JavaScript", 0, 0, 160, 0, 0, 0, 0, 0, 0, 0, 7, 1, 168
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/common/quickAccess.js", "JavaScript", 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 13, 1, 54
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/quickinput/common/quickInput.js", "JavaScript", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 4, 1, 8
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/registry/common/platform.js", "JavaScript", 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 4, 1, 22
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/severityIcon/common/severityIcon.js", "JavaScript", 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 4, 1, 68
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/storage/common/storage.js", "JavaScript", 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 22, 1, 157
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/telemetry/common/gdprTypings.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/telemetry/common/telemetry.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/browser/iconsStyleSheet.js", "JavaScript", 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 4, 1, 52
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/colorRegistry.js", "JavaScript", 0, 0, 353, 0, 0, 0, 0, 0, 0, 0, 87, 1, 441
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/iconRegistry.js", "JavaScript", 0, 0, 128, 0, 0, 0, 0, 0, 0, 0, 8, 1, 137
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/styler.js", "JavaScript", 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 4, 1, 83
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/theme.js", "JavaScript", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 7, 1, 14
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/theme/common/themeService.js", "JavaScript", 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 10, 1, 124
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/undoRedo/common/undoRedo.js", "JavaScript", 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 4, 1, 41
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/undoRedo/common/undoRedoService.js", "JavaScript", 0, 0, 1048, 0, 0, 0, 0, 0, 0, 0, 49, 1, 1098
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/workspace/common/workspace.js", "JavaScript", 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 1, 63
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/workspace/common/workspaceTrust.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/esm/vs/platform/workspaces/common/workspaces.js", "JavaScript", 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 4, 1, 28
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/base/worker/workerMain.js", "JavaScript", 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 8, 2, 22
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/abap/abap.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/apex/apex.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/azcli/azcli.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/bat/bat.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/bicep/bicep.js", "JavaScript", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 9, 0, 12
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/cameligo/cameligo.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/clojure/clojure.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/coffee/coffee.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/cpp/cpp.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/csharp/csharp.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/csp/csp.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/css/css.js", "JavaScript", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 6, 1, 13
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/dart/dart.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/dockerfile/dockerfile.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/ecl/ecl.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/elixir/elixir.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/flow9/flow9.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/fsharp/fsharp.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/go/go.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/graphql/graphql.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/handlebars/handlebars.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/hcl/hcl.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/html/html.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/ini/ini.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/java/java.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/javascript/javascript.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/julia/julia.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/kotlin/kotlin.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/less/less.js", "JavaScript", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 6, 1, 12
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/lexon/lexon.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/liquid/liquid.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/lua/lua.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/m3/m3.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/markdown/markdown.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/mips/mips.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/msdax/msdax.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/mysql/mysql.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/objective-c/objective-c.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pascal/pascal.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pascaligo/pascaligo.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/perl/perl.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pgsql/pgsql.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/php/php.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pla/pla.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/postiats/postiats.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/powerquery/powerquery.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/powershell/powershell.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/protobuf/protobuf.js", "JavaScript", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 6, 1, 12
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/pug/pug.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/python/python.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/qsharp/qsharp.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/r/r.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/razor/razor.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/redis/redis.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/redshift/redshift.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/restructuredtext/restructuredtext.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/ruby/ruby.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/rust/rust.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/sb/sb.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/scala/scala.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/scheme/scheme.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/scss/scss.js", "JavaScript", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 6, 1, 13
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/shell/shell.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/solidity/solidity.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/sophia/sophia.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/sparql/sparql.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/sql/sql.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/st/st.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/swift/swift.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 9, 1, 14
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/systemverilog/systemverilog.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/tcl/tcl.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/twig/twig.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/typescript/typescript.js", "JavaScript", 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 9, 0, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/vb/vb.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/xml/xml.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/basic-languages/yaml/yaml.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.css", "CSS", 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 5, 0, 6
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.js", "JavaScript", 0, 0, 663, 0, 0, 0, 0, 0, 0, 0, 57, 87, 807
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.de.js", "JavaScript", 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 6, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.es.js", "JavaScript", 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 6, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.fr.js", "JavaScript", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 6, 1, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.it.js", "JavaScript", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 6, 1, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.ja.js", "JavaScript", 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 6, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.js", "JavaScript", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 6, 1, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.ko.js", "JavaScript", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 6, 1, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.ru.js", "JavaScript", 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 6, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.zh-cn.js", "JavaScript", 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 6, 1, 29
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/editor/editor.main.nls.zh-tw.js", "JavaScript", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 6, 1, 27
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/css/cssMode.js", "JavaScript", 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 6, 1, 14
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/css/cssWorker.js", "JavaScript", 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 6, 20, 64
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/html/htmlMode.js", "JavaScript", 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 6, 1, 14
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/html/htmlWorker.js", "JavaScript", 0, 0, 257, 0, 0, 0, 0, 0, 0, 0, 6, 191, 454
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/json/jsonMode.js", "JavaScript", 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 6, 1, 16
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/json/jsonWorker.js", "JavaScript", 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 6, 4, 37
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/typescript/tsMode.js", "JavaScript", 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 6, 4, 21
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/language/typescript/tsWorker.js", "JavaScript", 0, 0, 20714, 0, 0, 0, 0, 0, 0, 0, 11099, 3510, 35323
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/min/vs/loader.js", "JavaScript", 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 6, 1, 11
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/monaco.d.ts", "TypeScript", 2976, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4449, 363, 7788
"/Users/<USER>/Desktop/work/apimange-web/public/monaco-editor/package.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 59
"/Users/<USER>/Desktop/work/apimange-web/scripts/build.js", "JavaScript", 0, 0, 169, 0, 0, 0, 0, 0, 0, 0, 20, 24, 213
"/Users/<USER>/Desktop/work/apimange-web/scripts/start.js", "JavaScript", 0, 0, 133, 0, 0, 0, 0, 0, 0, 0, 17, 17, 167
"/Users/<USER>/Desktop/work/apimange-web/scripts/test.js", "JavaScript", 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 7, 11, 54
"/Users/<USER>/Desktop/work/apimange-web/src/App.less", "Less", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 2, 14
"/Users/<USER>/Desktop/work/apimange-web/src/App.tsx", "TypeScript React", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 3, 10
"/Users/<USER>/Desktop/work/apimange-web/src/components/AlertTips/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 5, 40
"/Users/<USER>/Desktop/work/apimange-web/src/components/AlertTips/styles.less", "Less", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 2, 13
"/Users/<USER>/Desktop/work/apimange-web/src/components/AuthProvider/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 10, 52
"/Users/<USER>/Desktop/work/apimange-web/src/components/AuthResource/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 3, 26
"/Users/<USER>/Desktop/work/apimange-web/src/components/ChooseGateWay/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 3, 16
"/Users/<USER>/Desktop/work/apimange-web/src/components/CustomProTable/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 32, 0, 0, 0, 0, 30, 3, 65
"/Users/<USER>/Desktop/work/apimange-web/src/components/CustomTable/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 3, 9
"/Users/<USER>/Desktop/work/apimange-web/src/components/EchartsRender/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 3, 9, 66
"/Users/<USER>/Desktop/work/apimange-web/src/components/Editor/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 14, 0, 0, 0, 0, 1, 4, 19
"/Users/<USER>/Desktop/work/apimange-web/src/components/HistoryTabs/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 125, 0, 0, 0, 0, 0, 13, 138
"/Users/<USER>/Desktop/work/apimange-web/src/components/HistoryTabs/styles.module.less", "Less", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 1, 1, 22
"/Users/<USER>/Desktop/work/apimange-web/src/components/Layout/BreadcrumbNav/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 7, 65
"/Users/<USER>/Desktop/work/apimange-web/src/components/Layout/BreadcrumbNav/styles.module.less", "Less", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 2, 13
"/Users/<USER>/Desktop/work/apimange-web/src/components/Layout/UpdataPasswordNode/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 116, 0, 0, 0, 0, 0, 13, 129
"/Users/<USER>/Desktop/work/apimange-web/src/components/Layout/index.less", "Less", 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 10, 18, 117
"/Users/<USER>/Desktop/work/apimange-web/src/components/Layout/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 226, 0, 0, 0, 0, 11, 14, 251
"/Users/<USER>/Desktop/work/apimange-web/src/components/Layout/styles.module.less", "Less", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 3, 3, 17
"/Users/<USER>/Desktop/work/apimange-web/src/components/LeftMenu/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 93, 0, 0, 0, 0, 0, 7, 100
"/Users/<USER>/Desktop/work/apimange-web/src/components/ModalCustomTable/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 133, 0, 0, 0, 0, 13, 13, 159
"/Users/<USER>/Desktop/work/apimange-web/src/components/ModifyPassword/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 9, 94
"/Users/<USER>/Desktop/work/apimange-web/src/components/NoticeList/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 127, 0, 0, 0, 0, 0, 9, 136
"/Users/<USER>/Desktop/work/apimange-web/src/components/PageTitle/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 3, 14
"/Users/<USER>/Desktop/work/apimange-web/src/components/PageView/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 3, 19
"/Users/<USER>/Desktop/work/apimange-web/src/components/PageView/styles.module.less", "Less", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 1, 8
"/Users/<USER>/Desktop/work/apimange-web/src/components/RenderTags/index.less", "Less", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 1, 13
"/Users/<USER>/Desktop/work/apimange-web/src/components/RenderTags/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 122, 0, 0, 0, 0, 0, 14, 136
"/Users/<USER>/Desktop/work/apimange-web/src/components/RequireAuth/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 3, 21
"/Users/<USER>/Desktop/work/apimange-web/src/components/RequireData/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Desktop/work/apimange-web/src/components/SearchCard/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 3, 20
"/Users/<USER>/Desktop/work/apimange-web/src/components/SelectApiTable/index.less", "Less", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/src/components/SelectApiTable/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 7, 64
"/Users/<USER>/Desktop/work/apimange-web/src/components/TigBtn/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 4, 25
"/Users/<USER>/Desktop/work/apimange-web/src/components/TigBtn/styles.module.less", "Less", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 3, 18
"/Users/<USER>/Desktop/work/apimange-web/src/components/Title/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 3, 17
"/Users/<USER>/Desktop/work/apimange-web/src/components/Title/styles.module.less", "Less", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 2, 21
"/Users/<USER>/Desktop/work/apimange-web/src/components/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 43, 0, 0, 0, 0, 0, 2, 45
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 2, 13
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useAsyncEffect.tsx", "TypeScript React", 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 1, 3, 32
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useCancleRequest.tsx", "TypeScript React", 0, 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 2, 16
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useHistory.tsx", "TypeScript React", 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 1, 2, 15
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useHistoryCache.tsx", "TypeScript React", 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 22, 5, 49
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useLocationChange.tsx", "TypeScript React", 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 5, 25
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useModal.tsx", "TypeScript React", 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 3, 13
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useMount.tsx", "TypeScript React", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 2, 10
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useNotice.tsx", "TypeScript React", 0, 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 6, 86
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useNoticeCount.tsx", "TypeScript React", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 2, 18
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useQuery.ts", "TypeScript", 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 24
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useTable.tsx", "TypeScript React", 0, 0, 0, 0, 0, 81, 0, 0, 0, 0, 4, 11, 96
"/Users/<USER>/Desktop/work/apimange-web/src/hooks/useUnmount.tsx", "TypeScript React", 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 2, 12
"/Users/<USER>/Desktop/work/apimange-web/src/index.css", "CSS", 0, 0, 0, 0, 86, 0, 0, 0, 0, 0, 36, 20, 142
"/Users/<USER>/Desktop/work/apimange-web/src/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 3, 28
"/Users/<USER>/Desktop/work/apimange-web/src/logo.svg", "XML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Desktop/work/apimange-web/src/page/404/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 2, 14
"/Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/ApiAudit/columns.ts", "TypeScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"/Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/ApiAudit/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 117, 0, 0, 0, 0, 10, 12, 139
"/Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/Subscribe/SubscriptionColumns.tsx", "TypeScript React", 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 2, 57
"/Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/Subscribe/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 117, 0, 0, 0, 0, 10, 12, 139
"/Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/Desktop/work/apimange-web/src/page/AuditManagement/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 4, 34
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/DelayProbability/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 5, 67
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/Empty/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 3, 15
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/Empty/styles.module.less", "Less", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 2, 14
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ErrorCount/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 6, 61
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/InletFlow/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 6, 69
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/OutFlow/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 7, 70
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/PageConainer/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 5, 28
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/PageConainer/styles.module.less", "Less", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 20, 4, 40
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/RequestCount/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 1, 5, 67
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/RequestCount/styles.module.less", "Less", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 1, 4
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/Respond/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 143, 0, 0, 0, 0, 3, 11, 157
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/Respond/styles.module.less", "Less", 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 5, 40
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ServiceUseRatio/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 112, 0, 0, 0, 0, 1, 6, 119
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ServiceUseRatio/styles.module.less", "Less", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 2, 24
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ServiceUseRatioA/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 182, 0, 0, 0, 0, 1, 6, 189
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/components/ServiceUseRatioA/styles.module.less", "Less", 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 11, 79
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/constant.ts", "TypeScript", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 4, 33
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 116, 0, 0, 0, 0, 16, 10, 142
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/option.ts", "TypeScript", 324, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 13, 343
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/styles.module.less", "Less", 0, 0, 0, 91, 0, 0, 0, 0, 0, 0, 1, 9, 101
"/Users/<USER>/Desktop/work/apimange-web/src/page/BigScreen/useDataZoom.ts", "TypeScript", 52, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 59
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/PackageManagement/columns.tsx", "TypeScript React", 0, 0, 0, 0, 0, 127, 0, 0, 0, 0, 8, 2, 137
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/PackageManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 275, 0, 0, 0, 0, 38, 19, 332
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/PackageManagement/styles.module.less", "Less", 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 4, 21
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/AddInterface/index.less", "Less", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 1, 7
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/AddInterface/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 139, 0, 0, 0, 0, 4, 12, 155
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/EditGateWay/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 4, 3, 37
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/NewGateWay/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 73, 0, 0, 0, 0, 0, 6, 79
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/NewPackage/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 267, 0, 0, 0, 0, 4, 19, 290
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/TelescopicCapacity/index.less", "Less", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 1, 10
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/TelescopicCapacity/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 211, 0, 0, 0, 0, 4, 9, 224
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/components/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 2, 14
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 132, 0, 0, 0, 0, 14, 9, 155
"/Users/<USER>/Desktop/work/apimange-web/src/page/GatewayList/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 4, 24
"/Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/BackendService/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 250, 0, 0, 0, 0, 22, 12, 284
"/Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/BackgroundServiceApi/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 309, 0, 0, 0, 0, 20, 18, 347
"/Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/DebugApi/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 453, 0, 0, 0, 0, 27, 30, 510
"/Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/Desktop/work/apimange-web/src/page/InterfaceIntegration/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 4, 44
"/Users/<USER>/Desktop/work/apimange-web/src/page/Login/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 8, 103
"/Users/<USER>/Desktop/work/apimange-web/src/page/Login/styles.module.less", "Less", 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 1, 9, 71
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/DataSource/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 101, 0, 0, 0, 0, 8, 7, 116
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Edit/index.less", "Less", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 3, 27
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Edit/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 149, 0, 0, 0, 0, 7, 14, 170
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/List/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 114, 0, 0, 0, 0, 10, 9, 133
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/MessageConversion/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 200, 0, 0, 0, 0, 24, 12, 236
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/MsgBodyManag/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 192, 0, 0, 0, 0, 19, 13, 224
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/MsgCovertDetail/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 81, 0, 0, 0, 0, 2, 5, 88
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/MsgDetail/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 117, 0, 0, 0, 0, 8, 7, 132
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/PathManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 93, 0, 0, 0, 0, 15, 8, 116
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Project/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 117, 0, 0, 0, 0, 11, 8, 136
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Service/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 140, 0, 0, 0, 0, 30, 13, 183
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/branch.tsx", "TypeScript React", 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 3, 47
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/commonGroupShape.tsx", "TypeScript React", 0, 0, 0, 0, 0, 93, 0, 0, 0, 0, 0, 5, 98
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/commonShape.tsx", "TypeScript React", 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 3, 65
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/connector.tsx", "TypeScript React", 0, 0, 0, 0, 0, 42, 0, 0, 0, 0, 29, 11, 82
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/end.tsx", "TypeScript React", 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 3, 44
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/if.tsx", "TypeScript React", 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 3, 47
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 1, 13
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/join.tsx", "TypeScript React", 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 2, 3, 34
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/label.tsx", "TypeScript React", 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 3, 47
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/start.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 3, 43
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/subTitle.tsx", "TypeScript React", 0, 0, 0, 0, 0, 47, 0, 0, 0, 0, 0, 3, 50
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/switch.tsx", "TypeScript React", 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 3, 47
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/Shape/title.tsx", "TypeScript React", 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 3, 74
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AddConfigService/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 210, 0, 0, 0, 0, 3, 14, 227
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AddDataSource/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 134, 0, 0, 0, 0, 0, 11, 145
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AddService/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 76, 0, 0, 0, 0, 0, 7, 83
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/index.less", "Less", 0, 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 19, 132
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 34, 0, 0, 0, 0, 21, 6, 61
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/useAssignLinkSvg.tsx", "TypeScript React", 0, 0, 0, 0, 0, 741, 0, 0, 0, 0, 40, 38, 819
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/useAssignLinkTable.tsx", "TypeScript React", 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 12, 6, 90
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/useSelectedForm.tsx", "TypeScript React", 0, 0, 0, 0, 0, 273, 0, 0, 0, 0, 46, 15, 334
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/AssignLinkDiagram/utils.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 3, 43
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Activity.tsx", "TypeScript React", 0, 0, 0, 0, 0, 134, 0, 0, 0, 0, 0, 5, 139
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Context.tsx", "TypeScript React", 0, 0, 0, 0, 0, 272, 0, 0, 0, 0, 8, 14, 294
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/General.tsx", "TypeScript React", 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 3, 6, 72
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Modal.tsx", "TypeScript React", 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 3, 4, 48
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Preferences.tsx", "TypeScript React", 0, 0, 0, 0, 0, 52, 0, 0, 0, 0, 8, 6, 66
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Assign.tsx", "TypeScript React", 0, 0, 0, 0, 0, 78, 0, 0, 0, 0, 0, 6, 84
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Branch.tsx", "TypeScript React", 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 16, 6, 79
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Break.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 4, 44
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/CacheDBCall.tsx", "TypeScript React", 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 4, 27
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Catch.tsx", "TypeScript React", 0, 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 5, 71
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/CatchAll.tsx", "TypeScript React", 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 4, 45
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Code.tsx", "TypeScript React", 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 6, 57
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Compensate.tsx", "TypeScript React", 0, 0, 0, 0, 0, 65, 0, 0, 0, 0, 0, 5, 70
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Compensationhandler.tsx", "TypeScript React", 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 4, 45
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Continue.tsx", "TypeScript React", 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 4, 43
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Delay.tsx", "TypeScript React", 0, 0, 0, 0, 0, 64, 0, 0, 0, 0, 1, 9, 74
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Edge.tsx", "TypeScript React", 0, 0, 0, 0, 0, 73, 0, 0, 0, 0, 1, 5, 79
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Empty.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 4, 44
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/End.tsx", "TypeScript React", 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 4, 43
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/ForEach.tsx", "TypeScript React", 0, 0, 0, 0, 0, 69, 0, 0, 0, 0, 0, 6, 75
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/GenCall.tsx", "TypeScript React", 0, 0, 0, 0, 0, 448, 0, 0, 0, 0, 15, 32, 495
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/GenCallTypeEnum.tsx", "TypeScript React", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 3, 6, 25
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/HttpCall.tsx", "TypeScript React", 0, 0, 0, 0, 0, 419, 0, 0, 0, 0, 11, 28, 458
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/If.tsx", "TypeScript React", 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 6, 63
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/JdbcCall.tsx", "TypeScript React", 0, 0, 0, 0, 0, 236, 0, 0, 0, 0, 9, 20, 265
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Join.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 4, 44
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Label.tsx", "TypeScript React", 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 4, 43
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Milestone.tsx", "TypeScript React", 0, 0, 0, 0, 0, 64, 0, 0, 0, 0, 0, 5, 69
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Reply.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 4, 44
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Rule.tsx", "TypeScript React", 0, 0, 0, 0, 0, 77, 0, 0, 0, 0, 0, 7, 84
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Sequence.tsx", "TypeScript React", 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 4, 45
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Sql.tsx", "TypeScript React", 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 5, 56
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Start.tsx", "TypeScript React", 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 3, 42
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Switch.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 3, 43
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Sync.tsx", "TypeScript React", 0, 0, 0, 0, 0, 73, 0, 0, 0, 0, 0, 7, 80
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Throw.tsx", "TypeScript React", 0, 0, 0, 0, 0, 65, 0, 0, 0, 0, 0, 5, 70
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Trace.tsx", "TypeScript React", 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 6, 64
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Transform.tsx", "TypeScript React", 0, 0, 0, 0, 0, 90, 0, 0, 0, 0, 4, 6, 100
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Until.tsx", "TypeScript React", 0, 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 6, 72
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/While.tsx", "TypeScript React", 0, 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 6, 72
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Xpath.tsx", "TypeScript React", 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 5, 77
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/Xslt.tsx", "TypeScript React", 0, 0, 0, 0, 0, 129, 0, 0, 0, 0, 1, 11, 141
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/Type/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 1, 36
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/hooks.tsx", "TypeScript React", 0, 0, 0, 0, 0, 109, 0, 0, 0, 0, 0, 14, 123
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Config/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 64, 0, 0, 0, 0, 3, 5, 72
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/CopyFlow/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 6, 61
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/DragShape/index.less", "Less", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 2, 23
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/DragShape/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 32, 0, 0, 0, 0, 0, 4, 36
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditAssignKeyValue/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 64, 0, 0, 0, 0, 0, 9, 73
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditAssignParams/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 10, 90
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditContext/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 117, 0, 0, 0, 0, 0, 10, 127
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditMsgBody/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 158, 0, 0, 0, 0, 4, 15, 177
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditMsgCovert/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 163, 0, 0, 0, 0, 3, 17, 183
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/EditProject/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 102, 0, 0, 0, 0, 3, 12, 117
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/JsonCovertSchema/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 102, 0, 0, 0, 0, 0, 11, 113
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/PathManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 191, 0, 0, 0, 0, 7, 13, 211
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/SelectMsg/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 86, 0, 0, 0, 0, 0, 8, 94
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/SelectPath/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 32, 0, 0, 0, 0, 0, 4, 36
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/SelectTransform/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 84, 0, 0, 0, 0, 1, 7, 92
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Start/index.less", "Less", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 2, 23
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Start/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 4, 33
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/TestService/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 169, 0, 0, 0, 0, 125, 14, 308
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Tools/index.less", "Less", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/Tools/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 398, 0, 0, 0, 0, 11, 28, 437
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/components/index.ts", "TypeScript", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 48
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/index.ts", "TypeScript", 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 19
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useActivityModel.ts", "TypeScript", 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 7, 99
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useBlankClick.ts", "TypeScript", 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 31
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useDnd.ts", "TypeScript", 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 187
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useDown.ts", "TypeScript", 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 11, 83
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useEdge.ts", "TypeScript", 73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 82
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useEdgeClick.ts", "TypeScript", 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 37
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useEdgeConnected.ts", "TypeScript", 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 53
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useGeneral.ts", "TypeScript", 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 33
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useGraph.ts", "TypeScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useGroupList.ts", "TypeScript", 78, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 11, 95
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useMouseDown.ts", "TypeScript", 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 22
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useMouseMove.ts", "TypeScript", 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6, 64
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useNode.ts", "TypeScript", 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 147
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useNodeClick.ts", "TypeScript", 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 44
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/usePortClick.ts", "TypeScript", 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 63
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/usePreferences.ts", "TypeScript", 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 30
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useProcessInfo.ts", "TypeScript", 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 10, 72
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useTableAssign.tsx", "TypeScript React", 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 5, 72
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/hooks/useTableEdit.tsx", "TypeScript React", 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 1, 8, 57
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/mock.ts", "TypeScript", 929, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 34, 1006
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 86, 0, 0, 0, 0, 0, 4, 90
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/connector.ts", "TypeScript", 283, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 11, 352
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/const.ts", "TypeScript", 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 11, 90
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/edge.ts", "TypeScript", 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 17, 174
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/edgeKeyFeames.less", "Less", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/event.ts", "TypeScript", 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 40
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/groupNode.ts", "TypeScript", 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 16
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/index.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 8
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/node.ts", "TypeScript", 405, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 42, 459
"/Users/<USER>/Desktop/work/apimange-web/src/page/ProcessOrchestration/utils/validator.ts", "TypeScript", 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 19
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/PublishReport/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 195, 0, 0, 0, 0, 42, 13, 250
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/PublishReport/styles.module.less", "Less", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/ReportDetails/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 99, 0, 0, 0, 0, 0, 5, 104
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/StatisticsDetails/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 111, 0, 0, 0, 0, 1, 4, 116
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/StatisticsReport/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 158, 0, 0, 0, 0, 10, 9, 177
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/SubscribeReport/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 195, 0, 0, 0, 0, 42, 12, 249
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/SubscribeReport/styles.module.less", "Less", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/components/charts/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 4, 37
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/components/charts/options.ts", "TypeScript", 212, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 5, 223
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/components/charts/styles.module.less", "Less", 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 1, 5
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/components/queryFilter/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 6, 57
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/Desktop/work/apimange-web/src/page/ReportForm/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 4, 58
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/AccessStrategyDetail/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 136, 0, 0, 0, 0, 11, 11, 158
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/AuthStrategyDetail/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 136, 0, 0, 0, 0, 11, 11, 158
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/FlowControl/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 218, 0, 0, 0, 0, 25, 13, 256
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/FlowControlDetails/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 135, 0, 0, 0, 0, 11, 12, 158
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceGroup/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 73, 0, 0, 0, 0, 1, 5, 79
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceGroupDetails/columns.tsx", "TypeScript React", 0, 0, 0, 0, 0, 124, 0, 0, 0, 0, 3, 2, 129
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceGroupDetails/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 158, 0, 0, 0, 0, 4, 11, 173
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceGroupManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 206, 0, 0, 0, 0, 4, 10, 220
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/InterfaceManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 175, 0, 0, 0, 0, 7, 15, 197
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/ProxyCache/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 218, 0, 0, 0, 0, 28, 13, 259
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/ProxyCacheDetails/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 184, 0, 0, 0, 0, 11, 12, 207
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/AccessStrategy.tsx", "TypeScript React", 0, 0, 0, 0, 0, 183, 0, 0, 0, 0, 7, 16, 206
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/AuthStrategy.tsx", "TypeScript React", 0, 0, 0, 0, 0, 184, 0, 0, 0, 0, 7, 17, 208
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/columns.ts", "TypeScript", 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 1, 33
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 285, 0, 0, 0, 0, 28, 17, 330
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/SafelyControl/index1.tsx", "TypeScript React", 0, 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 3, 52
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/ServiceMonitoring/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 179, 0, 0, 0, 0, 3, 7, 189
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/VersionManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 168, 0, 0, 0, 0, 17, 12, 197
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddAccessStrategy/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 220, 0, 0, 0, 0, 2, 17, 239
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddApi/index.less", "Less", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 1, 10
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddApi/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 264, 0, 0, 0, 0, 25, 18, 307
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddAuthStrategy/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 238, 0, 0, 0, 0, 4, 20, 262
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddFlowControl/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 359, 0, 0, 0, 0, 0, 18, 377
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddInputParameter/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 115, 0, 0, 0, 0, 0, 11, 126
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/AddResInformation/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 9, 72
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ApiSubCert/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 2, 6
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/Audit/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 86, 0, 0, 0, 0, 0, 7, 93
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/EditBackendService/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 136, 0, 0, 0, 0, 8, 12, 156
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/EditInterfaceGroup/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 105, 0, 0, 0, 0, 0, 11, 116
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/EditProxyCacheStrategy/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 194, 0, 0, 0, 0, 3, 13, 210
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/EditVersion/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 357, 0, 0, 0, 0, 15, 22, 394
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ImportExcel/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 103, 0, 0, 0, 0, 12, 9, 124
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ImportSwagger/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 96, 0, 0, 0, 0, 12, 9, 117
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/InterfaceHeader/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 116, 0, 0, 0, 0, 18, 12, 146
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/InterfaceSetting/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 211, 0, 0, 0, 0, 7, 3, 221
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/PublishGateWay/index.less", "Less", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 1, 10
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/PublishGateWay/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 127, 0, 0, 0, 0, 3, 9, 139
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/PublishInterface/index.less", "Less", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 3, 19
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/PublishInterface/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 1063, 0, 0, 0, 0, 83, 61, 1207
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ServiceAddress/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/ServiceMonitoringTable/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 208, 0, 0, 0, 0, 19, 8, 235
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/components/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 2, 40
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 115, 0, 0, 0, 0, 0, 4, 119
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceRelease/utils/interfaceTable.ts", "TypeScript", 75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 77
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/CraDetails/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 128, 0, 0, 0, 0, 6, 11, 145
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/CraManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 217, 0, 0, 0, 0, 13, 13, 243
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/SubscriptionList/apiColumns.tsx", "TypeScript React", 0, 0, 0, 0, 0, 118, 0, 0, 0, 0, 0, 2, 120
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/SubscriptionList/columns.ts", "TypeScript", 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 56
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/SubscriptionList/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 209, 0, 0, 0, 0, 36, 12, 257
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/Audit/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 86, 0, 0, 0, 0, 0, 7, 93
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/CraStepTwoForm/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 109, 0, 0, 0, 0, 0, 7, 116
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/CreateSub/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 72, 0, 0, 0, 0, 0, 6, 78
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/EditCra/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 147, 0, 0, 0, 0, 11, 11, 169
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/Subscription/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 141, 0, 0, 0, 0, 3, 6, 150
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/SubscriptionColumns/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 154, 0, 0, 0, 0, 14, 3, 171
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/components/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 2, 8
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/Desktop/work/apimange-web/src/page/ServiceSubscription/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 4, 42
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/AuditManagement/AuditInterface.tsx", "TypeScript React", 0, 0, 0, 0, 0, 144, 0, 0, 0, 0, 11, 15, 170
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/AuditManagement/SubscriptionReview.tsx", "TypeScript React", 0, 0, 0, 0, 0, 105, 0, 0, 0, 0, 14, 13, 132
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/AuditManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 3, 43
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/HospitalManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 114, 0, 0, 0, 0, 11, 8, 133
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/LogDetail/ProcessCall.tsx", "TypeScript React", 0, 0, 0, 0, 0, 244, 0, 0, 0, 0, 8, 25, 277
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/LogDetail/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 3, 36
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/LogManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 278, 0, 0, 0, 0, 5, 9, 292
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/LogManagement/styles.module.less", "Less", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 1, 17
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/Menu/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 156, 0, 0, 0, 0, 17, 11, 184
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/Role/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 117, 0, 0, 0, 0, 14, 10, 141
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/UserGroupManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 117, 0, 0, 0, 0, 14, 10, 141
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/UserManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 14, 10, 205
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditHospital/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 122, 0, 0, 0, 0, 7, 11, 140
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditMenu/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 285, 0, 0, 0, 0, 9, 17, 311
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditRole/PowerModal.tsx", "TypeScript React", 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 8, 79
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditRole/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 227, 0, 0, 0, 0, 10, 17, 254
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditUser/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 175, 0, 0, 0, 0, 10, 15, 200
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/EditUserGroup/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 129, 0, 0, 0, 0, 7, 14, 150
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/LogDetail/ProcessCall.tsx", "TypeScript React", 0, 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 5, 38
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/LogDetail/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 5, 51
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/components/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 2, 9
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/Desktop/work/apimange-web/src/page/SystemManagement/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 78, 0, 0, 0, 0, 7, 4, 89
"/Users/<USER>/Desktop/work/apimange-web/src/page/index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 3, 12
"/Users/<USER>/Desktop/work/apimange-web/src/page/route.tsx", "TypeScript React", 0, 0, 0, 0, 0, 74, 0, 0, 0, 0, 12, 9, 95
"/Users/<USER>/Desktop/work/apimange-web/src/service/bigScreen.ts", "TypeScript", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 12
"/Users/<USER>/Desktop/work/apimange-web/src/service/getway-list.ts", "TypeScript", 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 22
"/Users/<USER>/Desktop/work/apimange-web/src/service/index.ts", "TypeScript", 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8
"/Users/<USER>/Desktop/work/apimange-web/src/service/interface-integration.ts", "TypeScript", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 38
"/Users/<USER>/Desktop/work/apimange-web/src/service/process-orchestration.ts", "TypeScript", 77, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 80
"/Users/<USER>/Desktop/work/apimange-web/src/service/report-form.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"/Users/<USER>/Desktop/work/apimange-web/src/service/service-release.ts", "TypeScript", 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 181
"/Users/<USER>/Desktop/work/apimange-web/src/service/service-subscription.ts", "TypeScript", 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 41
"/Users/<USER>/Desktop/work/apimange-web/src/service/system-management.ts", "TypeScript", 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 84
"/Users/<USER>/Desktop/work/apimange-web/src/setupTests.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"/Users/<USER>/Desktop/work/apimange-web/src/store/global.ts", "TypeScript", 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 72
"/Users/<USER>/Desktop/work/apimange-web/src/store/historyTab.ts", "TypeScript", 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 25
"/Users/<USER>/Desktop/work/apimange-web/src/store/index.ts", "TypeScript", 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 40
"/Users/<USER>/Desktop/work/apimange-web/src/store/processOrchestration.ts", "TypeScript", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 5, 107
"/Users/<USER>/Desktop/work/apimange-web/src/utils/enum.ts", "TypeScript", 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 15, 113
"/Users/<USER>/Desktop/work/apimange-web/src/utils/host.ts", "TypeScript", 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6, 27
"/Users/<USER>/Desktop/work/apimange-web/src/utils/jsonToSchema.ts", "TypeScript", 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 104
"/Users/<USER>/Desktop/work/apimange-web/src/utils/reg.ts", "TypeScript", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 5
"/Users/<USER>/Desktop/work/apimange-web/src/utils/request.ts", "TypeScript", 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 9, 114
"/Users/<USER>/Desktop/work/apimange-web/src/utils/resource.ts", "TypeScript", 171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 2, 183
"/Users/<USER>/Desktop/work/apimange-web/src/utils/utils.ts", "TypeScript", 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7, 76
"/Users/<USER>/Desktop/work/apimange-web/tsconfig.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 3, 1, 19
"/Users/<USER>/Desktop/work/apimange-web/typings.d.ts", "TypeScript", 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7, 24
"Total", "-", 10914, 1, 780163, 803, 6453, 27017, 15, 11217, 1010, 13, 85494, 19195, 942295