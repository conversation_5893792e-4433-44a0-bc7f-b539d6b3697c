# Summary

Date : 2022-09-27 18:11:25

Directory /Users/<USER>/Desktop/work/apimange-web

Total : 1386 files,  837606 codes, 85494 comments, 19195 blanks, all 942295 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| JavaScript | 951 | 780,163 | 73,761 | 13,946 | 867,870 |
| TypeScript React | 255 | 27,017 | 1,538 | 2,071 | 30,626 |
| JSON | 5 | 11,217 | 0 | 2 | 11,219 |
| TypeScript | 61 | 10,914 | 9,171 | 1,105 | 21,190 |
| CSS | 73 | 6,453 | 984 | 1,499 | 8,936 |
| Markdown | 3 | 1,010 | 0 | 444 | 1,454 |
| Less | 35 | 803 | 37 | 122 | 962 |
| JSON with Comments | 1 | 15 | 3 | 1 | 19 |
| HTML | 1 | 13 | 0 | 4 | 17 |
| XML | 1 | 1 | 0 | 1 | 2 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 1,386 | 837,606 | 85,494 | 19,195 | 942,295 |
| config | 12 | 1,008 | 405 | 115 | 1,528 |
| config/jest | 3 | 65 | 7 | 14 | 86 |
| mock | 2 | 46 | 1 | 9 | 56 |
| public | 1,013 | 803,102 | 83,158 | 16,426 | 902,686 |
| public/monaco-editor | 1,011 | 803,064 | 83,158 | 16,421 | 902,643 |
| public/monaco-editor/dev | 100 | 406,701 | 32,382 | 6,419 | 445,502 |
| public/monaco-editor/dev/vs | 98 | 395,788 | 32,382 | 6,419 | 434,589 |
| public/monaco-editor/dev/vs/base | 1 | 11,039 | 2,334 | 44 | 13,417 |
| public/monaco-editor/dev/vs/base/worker | 1 | 11,039 | 2,334 | 44 | 13,417 |
| public/monaco-editor/dev/vs/basic-languages | 76 | 25,809 | 1,251 | 161 | 27,221 |
| public/monaco-editor/dev/vs/basic-languages/abap | 1 | 1,317 | 13 | 2 | 1,332 |
| public/monaco-editor/dev/vs/basic-languages/apex | 1 | 300 | 36 | 1 | 337 |
| public/monaco-editor/dev/vs/basic-languages/azcli | 1 | 83 | 7 | 2 | 92 |
| public/monaco-editor/dev/vs/basic-languages/bat | 1 | 104 | 7 | 2 | 113 |
| public/monaco-editor/dev/vs/basic-languages/bicep | 1 | 97 | 35 | 1 | 133 |
| public/monaco-editor/dev/vs/basic-languages/cameligo | 1 | 177 | 7 | 2 | 186 |
| public/monaco-editor/dev/vs/basic-languages/clojure | 1 | 763 | 7 | 2 | 772 |
| public/monaco-editor/dev/vs/basic-languages/coffee | 1 | 237 | 7 | 2 | 246 |
| public/monaco-editor/dev/vs/basic-languages/cpp | 1 | 363 | 40 | 2 | 405 |
| public/monaco-editor/dev/vs/basic-languages/csharp | 1 | 296 | 40 | 2 | 338 |
| public/monaco-editor/dev/vs/basic-languages/csp | 1 | 66 | 7 | 2 | 75 |
| public/monaco-editor/dev/vs/basic-languages/css | 1 | 193 | 7 | 2 | 202 |
| public/monaco-editor/dev/vs/basic-languages/dart | 1 | 219 | 68 | 1 | 288 |
| public/monaco-editor/dev/vs/basic-languages/dockerfile | 1 | 141 | 7 | 2 | 150 |
| public/monaco-editor/dev/vs/basic-languages/ecl | 1 | 448 | 23 | 1 | 472 |
| public/monaco-editor/dev/vs/basic-languages/elixir | 1 | 476 | 7 | 2 | 485 |
| public/monaco-editor/dev/vs/basic-languages/flow9 | 1 | 135 | 23 | 1 | 159 |
| public/monaco-editor/dev/vs/basic-languages/fsharp | 1 | 219 | 7 | 2 | 228 |
| public/monaco-editor/dev/vs/basic-languages/go | 1 | 195 | 30 | 1 | 226 |
| public/monaco-editor/dev/vs/basic-languages/graphql | 1 | 150 | 7 | 2 | 159 |
| public/monaco-editor/dev/vs/basic-languages/handlebars | 1 | 420 | 10 | 5 | 435 |
| public/monaco-editor/dev/vs/basic-languages/hcl | 1 | 159 | 35 | 1 | 195 |
| public/monaco-editor/dev/vs/basic-languages/html | 1 | 296 | 10 | 5 | 311 |
| public/monaco-editor/dev/vs/basic-languages/ini | 1 | 77 | 7 | 2 | 86 |
| public/monaco-editor/dev/vs/basic-languages/java | 1 | 205 | 33 | 1 | 239 |
| public/monaco-editor/dev/vs/basic-languages/javascript | 1 | 285 | 146 | 4 | 435 |
| public/monaco-editor/dev/vs/basic-languages/julia | 1 | 505 | 7 | 2 | 514 |
| public/monaco-editor/dev/vs/basic-languages/kotlin | 1 | 231 | 31 | 1 | 263 |
| public/monaco-editor/dev/vs/basic-languages/less | 1 | 176 | 7 | 2 | 185 |
| public/monaco-editor/dev/vs/basic-languages/lexon | 1 | 160 | 7 | 2 | 169 |
| public/monaco-editor/dev/vs/basic-languages/liquid | 1 | 267 | 10 | 5 | 282 |
| public/monaco-editor/dev/vs/basic-languages/lua | 1 | 166 | 7 | 2 | 175 |
| public/monaco-editor/dev/vs/basic-languages/m3 | 1 | 219 | 7 | 2 | 228 |
| public/monaco-editor/dev/vs/basic-languages/markdown | 1 | 211 | 7 | 2 | 220 |
| public/monaco-editor/dev/vs/basic-languages/mips | 1 | 203 | 7 | 2 | 212 |
| public/monaco-editor/dev/vs/basic-languages/msdax | 1 | 386 | 7 | 2 | 395 |
| public/monaco-editor/dev/vs/basic-languages/mysql | 1 | 884 | 7 | 2 | 893 |
| public/monaco-editor/dev/vs/basic-languages/objective-c | 1 | 197 | 7 | 2 | 206 |
| public/monaco-editor/dev/vs/basic-languages/pascal | 1 | 254 | 7 | 2 | 263 |
| public/monaco-editor/dev/vs/basic-languages/pascaligo | 1 | 167 | 7 | 2 | 176 |
| public/monaco-editor/dev/vs/basic-languages/perl | 1 | 608 | 7 | 2 | 617 |
| public/monaco-editor/dev/vs/basic-languages/pgsql | 1 | 852 | 7 | 2 | 861 |
| public/monaco-editor/dev/vs/basic-languages/php | 1 | 480 | 7 | 2 | 489 |
| public/monaco-editor/dev/vs/basic-languages/pla | 1 | 140 | 7 | 2 | 149 |
| public/monaco-editor/dev/vs/basic-languages/postiats | 1 | 548 | 12 | 2 | 562 |
| public/monaco-editor/dev/vs/basic-languages/powerquery | 1 | 899 | 7 | 2 | 908 |
| public/monaco-editor/dev/vs/basic-languages/powershell | 1 | 242 | 7 | 2 | 251 |
| public/monaco-editor/dev/vs/basic-languages/protobuf | 1 | 404 | 34 | 2 | 440 |
| public/monaco-editor/dev/vs/basic-languages/pug | 1 | 402 | 7 | 2 | 411 |
| public/monaco-editor/dev/vs/basic-languages/python | 1 | 284 | 10 | 5 | 299 |
| public/monaco-editor/dev/vs/basic-languages/qsharp | 1 | 290 | 7 | 2 | 299 |
| public/monaco-editor/dev/vs/basic-languages/r | 1 | 254 | 7 | 2 | 263 |
| public/monaco-editor/dev/vs/basic-languages/razor | 1 | 541 | 10 | 5 | 556 |
| public/monaco-editor/dev/vs/basic-languages/redis | 1 | 307 | 7 | 2 | 316 |
| public/monaco-editor/dev/vs/basic-languages/redshift | 1 | 815 | 7 | 2 | 824 |
| public/monaco-editor/dev/vs/basic-languages/restructuredtext | 1 | 171 | 7 | 2 | 180 |
| public/monaco-editor/dev/vs/basic-languages/ruby | 1 | 453 | 7 | 2 | 462 |
| public/monaco-editor/dev/vs/basic-languages/rust | 1 | 312 | 42 | 1 | 355 |
| public/monaco-editor/dev/vs/basic-languages/sb | 1 | 119 | 7 | 2 | 128 |
| public/monaco-editor/dev/vs/basic-languages/scala | 1 | 360 | 9 | 2 | 371 |
| public/monaco-editor/dev/vs/basic-languages/scheme | 1 | 123 | 7 | 2 | 132 |
| public/monaco-editor/dev/vs/basic-languages/scss | 1 | 251 | 7 | 2 | 260 |
| public/monaco-editor/dev/vs/basic-languages/shell | 1 | 230 | 7 | 2 | 239 |
| public/monaco-editor/dev/vs/basic-languages/solidity | 1 | 1,340 | 28 | 1 | 1,369 |
| public/monaco-editor/dev/vs/basic-languages/sophia | 1 | 179 | 28 | 1 | 208 |
| public/monaco-editor/dev/vs/basic-languages/sparql | 1 | 202 | 7 | 2 | 211 |
| public/monaco-editor/dev/vs/basic-languages/sql | 1 | 827 | 7 | 2 | 836 |
| public/monaco-editor/dev/vs/basic-languages/st | 1 | 420 | 10 | 2 | 432 |
| public/monaco-editor/dev/vs/basic-languages/swift | 1 | 261 | 10 | 2 | 273 |
| public/monaco-editor/dev/vs/basic-languages/systemverilog | 1 | 517 | 56 | 1 | 574 |
| public/monaco-editor/dev/vs/basic-languages/tcl | 1 | 241 | 7 | 2 | 250 |
| public/monaco-editor/dev/vs/basic-languages/twig | 1 | 332 | 7 | 2 | 341 |
| public/monaco-editor/dev/vs/basic-languages/typescript | 1 | 285 | 80 | 4 | 369 |
| public/monaco-editor/dev/vs/basic-languages/vb | 1 | 373 | 7 | 2 | 382 |
| public/monaco-editor/dev/vs/basic-languages/xml | 1 | 123 | 10 | 5 | 138 |
| public/monaco-editor/dev/vs/basic-languages/yaml | 1 | 177 | 7 | 2 | 186 |
| public/monaco-editor/dev/vs/editor | 12 | 148,154 | 13,036 | 3,742 | 164,932 |
| public/monaco-editor/dev/vs/language | 8 | 209,067 | 15,523 | 2,470 | 227,060 |
| public/monaco-editor/dev/vs/language/css | 2 | 37,391 | 63 | 53 | 37,507 |
| public/monaco-editor/dev/vs/language/html | 2 | 17,904 | 53 | 43 | 18,000 |
| public/monaco-editor/dev/vs/language/json | 2 | 9,558 | 227 | 42 | 9,827 |
| public/monaco-editor/dev/vs/language/typescript | 2 | 144,214 | 15,180 | 2,332 | 161,726 |
| public/monaco-editor/esm | 809 | 370,078 | 34,546 | 5,303 | 409,927 |
| public/monaco-editor/esm/vs | 807 | 369,520 | 34,541 | 5,291 | 409,352 |
| public/monaco-editor/esm/vs/base | 160 | 32,040 | 4,228 | 1,257 | 37,525 |
| public/monaco-editor/esm/vs/base/browser | 90 | 16,489 | 1,614 | 617 | 18,720 |
| public/monaco-editor/esm/vs/base/browser/dompurify | 1 | 772 | 376 | 237 | 1,385 |
| public/monaco-editor/esm/vs/base/browser/ui | 74 | 13,434 | 967 | 365 | 14,766 |
| public/monaco-editor/esm/vs/base/browser/ui/actionbar | 3 | 723 | 43 | 21 | 787 |
| public/monaco-editor/esm/vs/base/browser/ui/aria | 2 | 79 | 17 | 2 | 98 |
| public/monaco-editor/esm/vs/base/browser/ui/button | 2 | 186 | 5 | 12 | 203 |
| public/monaco-editor/esm/vs/base/browser/ui/checkbox | 2 | 140 | 9 | 9 | 158 |
| public/monaco-editor/esm/vs/base/browser/ui/codicons | 3 | 49 | 15 | 11 | 75 |
| public/monaco-editor/esm/vs/base/browser/ui/codicons/codicon | 2 | 39 | 11 | 10 | 60 |
| public/monaco-editor/esm/vs/base/browser/ui/contextview | 2 | 267 | 24 | 10 | 301 |
| public/monaco-editor/esm/vs/base/browser/ui/countBadge | 2 | 72 | 8 | 4 | 84 |
| public/monaco-editor/esm/vs/base/browser/ui/dropdown | 3 | 254 | 13 | 10 | 277 |
| public/monaco-editor/esm/vs/base/browser/ui/findinput | 4 | 629 | 24 | 11 | 664 |
| public/monaco-editor/esm/vs/base/browser/ui/highlightedlabel | 1 | 86 | 26 | 1 | 113 |
| public/monaco-editor/esm/vs/base/browser/ui/hover | 2 | 175 | 11 | 29 | 215 |
| public/monaco-editor/esm/vs/base/browser/ui/iconLabel | 5 | 476 | 32 | 24 | 532 |
| public/monaco-editor/esm/vs/base/browser/ui/inputbox | 2 | 602 | 21 | 20 | 643 |
| public/monaco-editor/esm/vs/base/browser/ui/keybindingLabel | 2 | 151 | 8 | 8 | 167 |
| public/monaco-editor/esm/vs/base/browser/ui/list | 8 | 2,730 | 168 | 32 | 2,930 |
| public/monaco-editor/esm/vs/base/browser/ui/menu | 1 | 1,021 | 57 | 63 | 1,141 |
| public/monaco-editor/esm/vs/base/browser/ui/mouseCursor | 2 | 9 | 9 | 4 | 22 |
| public/monaco-editor/esm/vs/base/browser/ui/progressbar | 2 | 117 | 29 | 9 | 155 |
| public/monaco-editor/esm/vs/base/browser/ui/sash | 2 | 467 | 76 | 25 | 568 |
| public/monaco-editor/esm/vs/base/browser/ui/scrollbar | 9 | 1,133 | 141 | 14 | 1,288 |
| public/monaco-editor/esm/vs/base/browser/ui/scrollbar/media | 1 | 41 | 7 | 6 | 54 |
| public/monaco-editor/esm/vs/base/browser/ui/splitview | 2 | 693 | 113 | 14 | 820 |
| public/monaco-editor/esm/vs/base/browser/ui/table | 3 | 206 | 17 | 11 | 234 |
| public/monaco-editor/esm/vs/base/browser/ui/tree | 9 | 3,132 | 97 | 20 | 3,249 |
| public/monaco-editor/esm/vs/base/browser/ui/tree/media | 1 | 53 | 5 | 12 | 70 |
| public/monaco-editor/esm/vs/base/common | 62 | 13,094 | 2,470 | 581 | 16,145 |
| public/monaco-editor/esm/vs/base/common/diff | 2 | 666 | 265 | 2 | 933 |
| public/monaco-editor/esm/vs/base/common/marked | 1 | 2,055 | 419 | 520 | 2,994 |
| public/monaco-editor/esm/vs/base/common/worker | 1 | 407 | 34 | 1 | 442 |
| public/monaco-editor/esm/vs/base/parts | 7 | 2,381 | 113 | 58 | 2,552 |
| public/monaco-editor/esm/vs/base/parts/quickinput | 6 | 2,214 | 90 | 57 | 2,361 |
| public/monaco-editor/esm/vs/base/parts/quickinput/browser | 5 | 2,200 | 76 | 56 | 2,332 |
| public/monaco-editor/esm/vs/base/parts/quickinput/browser/media | 1 | 230 | 10 | 52 | 292 |
| public/monaco-editor/esm/vs/base/parts/quickinput/common | 1 | 14 | 14 | 1 | 29 |
| public/monaco-editor/esm/vs/base/parts/storage | 1 | 167 | 23 | 1 | 191 |
| public/monaco-editor/esm/vs/base/parts/storage/common | 1 | 167 | 23 | 1 | 191 |
| public/monaco-editor/esm/vs/base/worker | 1 | 76 | 31 | 1 | 108 |
| public/monaco-editor/esm/vs/basic-languages | 154 | 25,825 | 1,645 | 310 | 27,780 |
| public/monaco-editor/esm/vs/basic-languages/abap | 2 | 1,318 | 20 | 4 | 1,342 |
| public/monaco-editor/esm/vs/basic-languages/apex | 2 | 302 | 43 | 3 | 348 |
| public/monaco-editor/esm/vs/basic-languages/azcli | 2 | 84 | 14 | 4 | 102 |
| public/monaco-editor/esm/vs/basic-languages/bat | 2 | 105 | 14 | 4 | 123 |
| public/monaco-editor/esm/vs/basic-languages/bicep | 2 | 98 | 42 | 3 | 143 |
| public/monaco-editor/esm/vs/basic-languages/cameligo | 2 | 178 | 14 | 4 | 196 |
| public/monaco-editor/esm/vs/basic-languages/clojure | 2 | 764 | 14 | 4 | 782 |
| public/monaco-editor/esm/vs/basic-languages/coffee | 2 | 239 | 14 | 4 | 257 |
| public/monaco-editor/esm/vs/basic-languages/cpp | 2 | 378 | 47 | 4 | 429 |
| public/monaco-editor/esm/vs/basic-languages/csharp | 2 | 297 | 47 | 4 | 348 |
| public/monaco-editor/esm/vs/basic-languages/csp | 2 | 67 | 14 | 4 | 85 |
| public/monaco-editor/esm/vs/basic-languages/css | 2 | 195 | 14 | 4 | 213 |
| public/monaco-editor/esm/vs/basic-languages/dart | 2 | 221 | 75 | 3 | 299 |
| public/monaco-editor/esm/vs/basic-languages/dockerfile | 2 | 143 | 14 | 4 | 161 |
| public/monaco-editor/esm/vs/basic-languages/ecl | 2 | 449 | 30 | 3 | 482 |
| public/monaco-editor/esm/vs/basic-languages/elixir | 2 | 477 | 14 | 4 | 495 |
| public/monaco-editor/esm/vs/basic-languages/flow9 | 2 | 136 | 30 | 3 | 169 |
| public/monaco-editor/esm/vs/basic-languages/fsharp | 2 | 220 | 14 | 4 | 238 |
| public/monaco-editor/esm/vs/basic-languages/go | 2 | 196 | 37 | 3 | 236 |
| public/monaco-editor/esm/vs/basic-languages/graphql | 2 | 152 | 14 | 4 | 170 |
| public/monaco-editor/esm/vs/basic-languages/handlebars | 2 | 412 | 15 | 6 | 433 |
| public/monaco-editor/esm/vs/basic-languages/hcl | 2 | 160 | 42 | 3 | 205 |
| public/monaco-editor/esm/vs/basic-languages/html | 2 | 288 | 15 | 6 | 309 |
| public/monaco-editor/esm/vs/basic-languages/ini | 2 | 79 | 14 | 4 | 97 |
| public/monaco-editor/esm/vs/basic-languages/java | 2 | 207 | 40 | 3 | 250 |
| public/monaco-editor/esm/vs/basic-languages/javascript | 2 | 87 | 14 | 4 | 105 |
| public/monaco-editor/esm/vs/basic-languages/julia | 2 | 506 | 14 | 4 | 524 |
| public/monaco-editor/esm/vs/basic-languages/kotlin | 2 | 233 | 38 | 3 | 274 |
| public/monaco-editor/esm/vs/basic-languages/less | 2 | 178 | 14 | 4 | 196 |
| public/monaco-editor/esm/vs/basic-languages/lexon | 2 | 161 | 14 | 4 | 179 |
| public/monaco-editor/esm/vs/basic-languages/liquid | 2 | 259 | 15 | 6 | 280 |
| public/monaco-editor/esm/vs/basic-languages/lua | 2 | 167 | 14 | 4 | 185 |
| public/monaco-editor/esm/vs/basic-languages/m3 | 2 | 220 | 14 | 4 | 238 |
| public/monaco-editor/esm/vs/basic-languages/markdown | 2 | 212 | 14 | 4 | 230 |
| public/monaco-editor/esm/vs/basic-languages/mips | 2 | 205 | 14 | 4 | 223 |
| public/monaco-editor/esm/vs/basic-languages/msdax | 2 | 387 | 14 | 4 | 405 |
| public/monaco-editor/esm/vs/basic-languages/mysql | 2 | 885 | 14 | 4 | 903 |
| public/monaco-editor/esm/vs/basic-languages/objective-c | 2 | 198 | 14 | 4 | 216 |
| public/monaco-editor/esm/vs/basic-languages/pascal | 2 | 256 | 14 | 4 | 274 |
| public/monaco-editor/esm/vs/basic-languages/pascaligo | 2 | 168 | 14 | 4 | 186 |
| public/monaco-editor/esm/vs/basic-languages/perl | 2 | 609 | 14 | 4 | 627 |
| public/monaco-editor/esm/vs/basic-languages/pgsql | 2 | 853 | 14 | 4 | 871 |
| public/monaco-editor/esm/vs/basic-languages/php | 2 | 482 | 14 | 4 | 500 |
| public/monaco-editor/esm/vs/basic-languages/pla | 2 | 140 | 14 | 4 | 158 |
| public/monaco-editor/esm/vs/basic-languages/postiats | 2 | 549 | 19 | 4 | 572 |
| public/monaco-editor/esm/vs/basic-languages/powerquery | 2 | 900 | 14 | 4 | 918 |
| public/monaco-editor/esm/vs/basic-languages/powershell | 2 | 243 | 14 | 4 | 261 |
| public/monaco-editor/esm/vs/basic-languages/protobuf | 2 | 405 | 41 | 4 | 450 |
| public/monaco-editor/esm/vs/basic-languages/pug | 2 | 403 | 14 | 4 | 421 |
| public/monaco-editor/esm/vs/basic-languages/python | 2 | 276 | 15 | 6 | 297 |
| public/monaco-editor/esm/vs/basic-languages/qsharp | 2 | 291 | 14 | 4 | 309 |
| public/monaco-editor/esm/vs/basic-languages/r | 2 | 255 | 14 | 4 | 273 |
| public/monaco-editor/esm/vs/basic-languages/razor | 2 | 533 | 15 | 6 | 554 |
| public/monaco-editor/esm/vs/basic-languages/redis | 2 | 308 | 14 | 4 | 326 |
| public/monaco-editor/esm/vs/basic-languages/redshift | 2 | 816 | 14 | 4 | 834 |
| public/monaco-editor/esm/vs/basic-languages/restructuredtext | 2 | 172 | 14 | 4 | 190 |
| public/monaco-editor/esm/vs/basic-languages/ruby | 2 | 455 | 14 | 4 | 473 |
| public/monaco-editor/esm/vs/basic-languages/rust | 2 | 313 | 49 | 3 | 365 |
| public/monaco-editor/esm/vs/basic-languages/sb | 2 | 120 | 14 | 4 | 138 |
| public/monaco-editor/esm/vs/basic-languages/scala | 2 | 362 | 16 | 4 | 382 |
| public/monaco-editor/esm/vs/basic-languages/scheme | 2 | 124 | 14 | 4 | 142 |
| public/monaco-editor/esm/vs/basic-languages/scss | 2 | 253 | 14 | 4 | 271 |
| public/monaco-editor/esm/vs/basic-languages/shell | 2 | 231 | 14 | 4 | 249 |
| public/monaco-editor/esm/vs/basic-languages/solidity | 2 | 1,341 | 35 | 3 | 1,379 |
| public/monaco-editor/esm/vs/basic-languages/sophia | 2 | 180 | 35 | 3 | 218 |
| public/monaco-editor/esm/vs/basic-languages/sparql | 2 | 203 | 14 | 4 | 221 |
| public/monaco-editor/esm/vs/basic-languages/sql | 2 | 828 | 14 | 4 | 846 |
| public/monaco-editor/esm/vs/basic-languages/st | 2 | 421 | 17 | 4 | 442 |
| public/monaco-editor/esm/vs/basic-languages/swift | 2 | 263 | 17 | 4 | 284 |
| public/monaco-editor/esm/vs/basic-languages/systemverilog | 2 | 532 | 63 | 3 | 598 |
| public/monaco-editor/esm/vs/basic-languages/tcl | 2 | 242 | 14 | 4 | 260 |
| public/monaco-editor/esm/vs/basic-languages/twig | 2 | 334 | 14 | 4 | 352 |
| public/monaco-editor/esm/vs/basic-languages/typescript | 2 | 277 | 85 | 5 | 367 |
| public/monaco-editor/esm/vs/basic-languages/vb | 2 | 374 | 14 | 4 | 392 |
| public/monaco-editor/esm/vs/basic-languages/xml | 2 | 130 | 15 | 6 | 151 |
| public/monaco-editor/esm/vs/basic-languages/yaml | 2 | 179 | 14 | 4 | 197 |
| public/monaco-editor/esm/vs/editor | 406 | 92,830 | 12,357 | 1,148 | 106,335 |
| public/monaco-editor/esm/vs/editor/browser | 81 | 18,456 | 1,419 | 146 | 20,021 |
| public/monaco-editor/esm/vs/editor/browser/config | 3 | 430 | 30 | 3 | 463 |
| public/monaco-editor/esm/vs/editor/browser/controller | 8 | 4,064 | 344 | 9 | 4,417 |
| public/monaco-editor/esm/vs/editor/browser/core | 3 | 303 | 30 | 3 | 336 |
| public/monaco-editor/esm/vs/editor/browser/services | 6 | 405 | 45 | 6 | 456 |
| public/monaco-editor/esm/vs/editor/browser/view | 8 | 1,668 | 123 | 8 | 1,799 |
| public/monaco-editor/esm/vs/editor/browser/viewParts | 41 | 6,345 | 630 | 74 | 7,049 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/contentWidgets | 1 | 413 | 32 | 1 | 446 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/currentLineHighlight | 2 | 190 | 11 | 5 | 206 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/decorations | 2 | 185 | 21 | 2 | 208 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/editorScrollbar | 1 | 169 | 12 | 3 | 184 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/glyphMargin | 2 | 159 | 14 | 4 | 177 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/indentGuides | 2 | 224 | 12 | 3 | 239 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/lineNumbers | 2 | 172 | 12 | 5 | 189 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/lines | 4 | 1,138 | 172 | 11 | 1,321 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/linesDecorations | 2 | 97 | 14 | 2 | 113 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/margin | 1 | 55 | 7 | 1 | 63 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/marginDecorations | 2 | 79 | 14 | 2 | 95 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/minimap | 6 | 1,583 | 177 | 8 | 1,768 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/overlayWidgets | 2 | 102 | 12 | 1 | 115 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/overviewRuler | 2 | 462 | 22 | 2 | 486 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/rulers | 2 | 79 | 12 | 2 | 93 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/scrollDecoration | 2 | 74 | 11 | 2 | 87 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/selections | 2 | 325 | 31 | 5 | 361 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/viewCursors | 3 | 521 | 33 | 14 | 568 |
| public/monaco-editor/esm/vs/editor/browser/viewParts/viewZones | 1 | 318 | 11 | 1 | 330 |
| public/monaco-editor/esm/vs/editor/browser/widget | 9 | 4,577 | 153 | 40 | 4,770 |
| public/monaco-editor/esm/vs/editor/browser/widget/media | 3 | 108 | 26 | 34 | 168 |
| public/monaco-editor/esm/vs/editor/common | 125 | 31,644 | 3,672 | 125 | 35,441 |
| public/monaco-editor/esm/vs/editor/common/commands | 4 | 364 | 45 | 4 | 413 |
| public/monaco-editor/esm/vs/editor/common/config | 4 | 3,033 | 236 | 4 | 3,273 |
| public/monaco-editor/esm/vs/editor/common/controller | 14 | 4,155 | 363 | 14 | 4,532 |
| public/monaco-editor/esm/vs/editor/common/core | 9 | 897 | 266 | 9 | 1,172 |
| public/monaco-editor/esm/vs/editor/common/diff | 1 | 378 | 20 | 1 | 399 |
| public/monaco-editor/esm/vs/editor/common/model | 29 | 10,362 | 1,070 | 29 | 11,461 |
| public/monaco-editor/esm/vs/editor/common/model/bracketPairs | 13 | 2,237 | 319 | 13 | 2,569 |
| public/monaco-editor/esm/vs/editor/common/model/bracketPairs/bracketPairsTree | 10 | 1,519 | 255 | 10 | 1,784 |
| public/monaco-editor/esm/vs/editor/common/model/pieceTreeTextBuffer | 4 | 2,243 | 152 | 4 | 2,399 |
| public/monaco-editor/esm/vs/editor/common/modes | 18 | 2,667 | 327 | 18 | 3,012 |
| public/monaco-editor/esm/vs/editor/common/modes/supports | 7 | 850 | 150 | 7 | 1,007 |
| public/monaco-editor/esm/vs/editor/common/services | 17 | 2,746 | 209 | 17 | 2,972 |
| public/monaco-editor/esm/vs/editor/common/standalone | 2 | 566 | 332 | 2 | 900 |
| public/monaco-editor/esm/vs/editor/common/view | 5 | 492 | 31 | 5 | 528 |
| public/monaco-editor/esm/vs/editor/common/viewLayout | 5 | 1,922 | 269 | 5 | 2,196 |
| public/monaco-editor/esm/vs/editor/common/viewModel | 11 | 3,579 | 291 | 11 | 3,881 |
| public/monaco-editor/esm/vs/editor/contrib | 165 | 34,915 | 2,230 | 445 | 37,590 |
| public/monaco-editor/esm/vs/editor/contrib/anchorSelect | 2 | 177 | 8 | 3 | 188 |
| public/monaco-editor/esm/vs/editor/contrib/bracketMatching | 2 | 311 | 20 | 3 | 334 |
| public/monaco-editor/esm/vs/editor/contrib/caretOperations | 3 | 145 | 15 | 3 | 163 |
| public/monaco-editor/esm/vs/editor/contrib/clipboard | 1 | 219 | 21 | 1 | 241 |
| public/monaco-editor/esm/vs/editor/contrib/codeAction | 9 | 1,446 | 75 | 11 | 1,532 |
| public/monaco-editor/esm/vs/editor/contrib/codelens | 5 | 926 | 56 | 14 | 996 |
| public/monaco-editor/esm/vs/editor/contrib/colorPicker | 6 | 724 | 31 | 29 | 784 |
| public/monaco-editor/esm/vs/editor/contrib/comment | 3 | 542 | 70 | 3 | 615 |
| public/monaco-editor/esm/vs/editor/contrib/contextmenu | 1 | 224 | 20 | 1 | 245 |
| public/monaco-editor/esm/vs/editor/contrib/cursorUndo | 1 | 127 | 5 | 1 | 133 |
| public/monaco-editor/esm/vs/editor/contrib/dnd | 3 | 265 | 19 | 4 | 288 |
| public/monaco-editor/esm/vs/editor/contrib/documentSymbols | 2 | 276 | 17 | 2 | 295 |
| public/monaco-editor/esm/vs/editor/contrib/find | 9 | 3,559 | 205 | 45 | 3,809 |
| public/monaco-editor/esm/vs/editor/contrib/folding | 9 | 2,112 | 154 | 12 | 2,278 |
| public/monaco-editor/esm/vs/editor/contrib/fontZoom | 1 | 45 | 4 | 1 | 50 |
| public/monaco-editor/esm/vs/editor/contrib/format | 3 | 670 | 41 | 3 | 714 |
| public/monaco-editor/esm/vs/editor/contrib/gotoError | 4 | 846 | 31 | 19 | 896 |
| public/monaco-editor/esm/vs/editor/contrib/gotoError/media | 1 | 58 | 6 | 16 | 80 |
| public/monaco-editor/esm/vs/editor/contrib/gotoSymbol | 11 | 2,673 | 160 | 27 | 2,860 |
| public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/link | 3 | 414 | 39 | 3 | 456 |
| public/monaco-editor/esm/vs/editor/contrib/gotoSymbol/peek | 4 | 1,084 | 71 | 20 | 1,175 |
| public/monaco-editor/esm/vs/editor/contrib/hover | 9 | 1,647 | 76 | 9 | 1,732 |
| public/monaco-editor/esm/vs/editor/contrib/inPlaceReplace | 2 | 167 | 18 | 2 | 187 |
| public/monaco-editor/esm/vs/editor/contrib/indentation | 2 | 578 | 29 | 2 | 609 |
| public/monaco-editor/esm/vs/editor/contrib/inlayHints | 1 | 305 | 10 | 1 | 316 |
| public/monaco-editor/esm/vs/editor/contrib/inlineCompletions | 12 | 1,958 | 132 | 18 | 2,108 |
| public/monaco-editor/esm/vs/editor/contrib/lineSelection | 1 | 30 | 4 | 1 | 35 |
| public/monaco-editor/esm/vs/editor/contrib/linesOperations | 4 | 1,403 | 79 | 4 | 1,486 |
| public/monaco-editor/esm/vs/editor/contrib/linkedEditing | 1 | 353 | 12 | 1 | 366 |
| public/monaco-editor/esm/vs/editor/contrib/links | 3 | 522 | 29 | 4 | 555 |
| public/monaco-editor/esm/vs/editor/contrib/message | 2 | 193 | 15 | 13 | 221 |
| public/monaco-editor/esm/vs/editor/contrib/multicursor | 1 | 900 | 39 | 1 | 940 |
| public/monaco-editor/esm/vs/editor/contrib/parameterHints | 5 | 857 | 31 | 25 | 913 |
| public/monaco-editor/esm/vs/editor/contrib/peekView | 2 | 283 | 10 | 16 | 309 |
| public/monaco-editor/esm/vs/editor/contrib/peekView/media | 1 | 55 | 4 | 15 | 74 |
| public/monaco-editor/esm/vs/editor/contrib/quickAccess | 4 | 585 | 105 | 4 | 694 |
| public/monaco-editor/esm/vs/editor/contrib/rename | 3 | 532 | 23 | 8 | 563 |
| public/monaco-editor/esm/vs/editor/contrib/smartSelect | 3 | 458 | 46 | 3 | 507 |
| public/monaco-editor/esm/vs/editor/contrib/snippet | 5 | 1,766 | 186 | 7 | 1,959 |
| public/monaco-editor/esm/vs/editor/contrib/suggest | 16 | 4,288 | 305 | 112 | 4,705 |
| public/monaco-editor/esm/vs/editor/contrib/suggest/media | 1 | 337 | 20 | 97 | 454 |
| public/monaco-editor/esm/vs/editor/contrib/symbolIcons | 1 | 304 | 4 | 1 | 309 |
| public/monaco-editor/esm/vs/editor/contrib/toggleTabFocusMode | 1 | 33 | 4 | 1 | 38 |
| public/monaco-editor/esm/vs/editor/contrib/tokenization | 1 | 25 | 4 | 1 | 30 |
| public/monaco-editor/esm/vs/editor/contrib/unicodeHighlighter | 4 | 751 | 32 | 20 | 803 |
| public/monaco-editor/esm/vs/editor/contrib/unusualLineTerminators | 1 | 99 | 9 | 1 | 109 |
| public/monaco-editor/esm/vs/editor/contrib/viewportSemanticTokens | 1 | 112 | 4 | 1 | 117 |
| public/monaco-editor/esm/vs/editor/contrib/wordHighlighter | 1 | 519 | 38 | 1 | 558 |
| public/monaco-editor/esm/vs/editor/contrib/wordOperations | 1 | 435 | 7 | 1 | 443 |
| public/monaco-editor/esm/vs/editor/contrib/wordPartOperations | 1 | 136 | 6 | 1 | 143 |
| public/monaco-editor/esm/vs/editor/contrib/zoneWidget | 2 | 389 | 21 | 4 | 414 |
| public/monaco-editor/esm/vs/editor/standalone | 29 | 4,704 | 565 | 68 | 5,337 |
| public/monaco-editor/esm/vs/editor/standalone/browser | 23 | 3,320 | 385 | 62 | 3,767 |
| public/monaco-editor/esm/vs/editor/standalone/browser/accessibilityHelp | 2 | 307 | 10 | 2 | 319 |
| public/monaco-editor/esm/vs/editor/standalone/browser/iPadShowKeyboard | 2 | 84 | 9 | 3 | 96 |
| public/monaco-editor/esm/vs/editor/standalone/browser/inspectTokens | 2 | 278 | 9 | 9 | 296 |
| public/monaco-editor/esm/vs/editor/standalone/browser/quickAccess | 4 | 214 | 16 | 4 | 234 |
| public/monaco-editor/esm/vs/editor/standalone/browser/quickInput | 2 | 153 | 11 | 10 | 174 |
| public/monaco-editor/esm/vs/editor/standalone/browser/referenceSearch | 1 | 32 | 4 | 1 | 37 |
| public/monaco-editor/esm/vs/editor/standalone/browser/toggleHighContrast | 1 | 26 | 5 | 1 | 32 |
| public/monaco-editor/esm/vs/editor/standalone/common | 6 | 1,384 | 180 | 6 | 1,570 |
| public/monaco-editor/esm/vs/editor/standalone/common/monarch | 4 | 1,213 | 166 | 4 | 1,383 |
| public/monaco-editor/esm/vs/language | 12 | 209,503 | 15,552 | 2,488 | 227,543 |
| public/monaco-editor/esm/vs/language/css | 3 | 37,470 | 70 | 57 | 37,597 |
| public/monaco-editor/esm/vs/language/html | 3 | 17,999 | 60 | 47 | 18,106 |
| public/monaco-editor/esm/vs/language/json | 3 | 9,612 | 234 | 46 | 9,892 |
| public/monaco-editor/esm/vs/language/typescript | 3 | 144,422 | 15,188 | 2,338 | 161,948 |
| public/monaco-editor/esm/vs/platform | 74 | 9,306 | 755 | 87 | 10,148 |
| public/monaco-editor/esm/vs/platform/accessibility | 2 | 52 | 4 | 2 | 58 |
| public/monaco-editor/esm/vs/platform/accessibility/browser | 1 | 48 | 0 | 1 | 49 |
| public/monaco-editor/esm/vs/platform/accessibility/common | 1 | 4 | 4 | 1 | 9 |
| public/monaco-editor/esm/vs/platform/actions | 4 | 817 | 63 | 15 | 895 |
| public/monaco-editor/esm/vs/platform/actions/browser | 2 | 446 | 21 | 13 | 480 |
| public/monaco-editor/esm/vs/platform/actions/common | 2 | 371 | 42 | 2 | 415 |
| public/monaco-editor/esm/vs/platform/browser | 2 | 89 | 8 | 2 | 99 |
| public/monaco-editor/esm/vs/platform/clipboard | 2 | 70 | 13 | 2 | 85 |
| public/monaco-editor/esm/vs/platform/clipboard/browser | 1 | 68 | 13 | 1 | 82 |
| public/monaco-editor/esm/vs/platform/clipboard/common | 1 | 2 | 0 | 1 | 3 |
| public/monaco-editor/esm/vs/platform/commands | 1 | 81 | 7 | 1 | 89 |
| public/monaco-editor/esm/vs/platform/commands/common | 1 | 81 | 7 | 1 | 89 |
| public/monaco-editor/esm/vs/platform/configuration | 3 | 720 | 26 | 3 | 749 |
| public/monaco-editor/esm/vs/platform/configuration/common | 3 | 720 | 26 | 3 | 749 |
| public/monaco-editor/esm/vs/platform/contextkey | 3 | 1,561 | 63 | 3 | 1,627 |
| public/monaco-editor/esm/vs/platform/contextkey/browser | 1 | 380 | 21 | 1 | 402 |
| public/monaco-editor/esm/vs/platform/contextkey/common | 2 | 1,181 | 42 | 2 | 1,225 |
| public/monaco-editor/esm/vs/platform/contextview | 5 | 230 | 26 | 7 | 263 |
| public/monaco-editor/esm/vs/platform/contextview/browser | 5 | 230 | 26 | 7 | 263 |
| public/monaco-editor/esm/vs/platform/dialogs | 1 | 2 | 0 | 1 | 3 |
| public/monaco-editor/esm/vs/platform/dialogs/common | 1 | 2 | 0 | 1 | 3 |
| public/monaco-editor/esm/vs/platform/editor | 1 | 5 | 8 | 1 | 14 |
| public/monaco-editor/esm/vs/platform/editor/common | 1 | 5 | 8 | 1 | 14 |
| public/monaco-editor/esm/vs/platform/environment | 1 | 2 | 0 | 1 | 3 |
| public/monaco-editor/esm/vs/platform/environment/common | 1 | 2 | 0 | 1 | 3 |
| public/monaco-editor/esm/vs/platform/extensions | 1 | 12 | 19 | 1 | 32 |
| public/monaco-editor/esm/vs/platform/extensions/common | 1 | 12 | 19 | 1 | 32 |
| public/monaco-editor/esm/vs/platform/files | 1 | 6 | 3 | 1 | 10 |
| public/monaco-editor/esm/vs/platform/files/common | 1 | 6 | 3 | 1 | 10 |
| public/monaco-editor/esm/vs/platform/instantiation | 6 | 413 | 50 | 6 | 469 |
| public/monaco-editor/esm/vs/platform/instantiation/common | 6 | 413 | 50 | 6 | 469 |
| public/monaco-editor/esm/vs/platform/jsonschemas | 1 | 26 | 4 | 1 | 31 |
| public/monaco-editor/esm/vs/platform/jsonschemas/common | 1 | 26 | 4 | 1 | 31 |
| public/monaco-editor/esm/vs/platform/keybinding | 7 | 782 | 76 | 7 | 865 |
| public/monaco-editor/esm/vs/platform/keybinding/common | 7 | 782 | 76 | 7 | 865 |
| public/monaco-editor/esm/vs/platform/label | 1 | 2 | 0 | 1 | 3 |
| public/monaco-editor/esm/vs/platform/label/common | 1 | 2 | 0 | 1 | 3 |
| public/monaco-editor/esm/vs/platform/layout | 1 | 2 | 4 | 1 | 7 |
| public/monaco-editor/esm/vs/platform/layout/browser | 1 | 2 | 4 | 1 | 7 |
| public/monaco-editor/esm/vs/platform/list | 1 | 877 | 10 | 1 | 888 |
| public/monaco-editor/esm/vs/platform/list/browser | 1 | 877 | 10 | 1 | 888 |
| public/monaco-editor/esm/vs/platform/log | 1 | 81 | 1 | 1 | 83 |
| public/monaco-editor/esm/vs/platform/log/common | 1 | 81 | 1 | 1 | 83 |
| public/monaco-editor/esm/vs/platform/markers | 2 | 353 | 18 | 2 | 373 |
| public/monaco-editor/esm/vs/platform/markers/common | 2 | 353 | 18 | 2 | 373 |
| public/monaco-editor/esm/vs/platform/notification | 1 | 6 | 0 | 1 | 7 |
| public/monaco-editor/esm/vs/platform/notification/common | 1 | 6 | 0 | 1 | 7 |
| public/monaco-editor/esm/vs/platform/opener | 2 | 129 | 8 | 2 | 139 |
| public/monaco-editor/esm/vs/platform/opener/browser | 1 | 90 | 4 | 1 | 95 |
| public/monaco-editor/esm/vs/platform/opener/common | 1 | 39 | 4 | 1 | 44 |
| public/monaco-editor/esm/vs/platform/progress | 1 | 17 | 0 | 1 | 18 |
| public/monaco-editor/esm/vs/platform/progress/common | 1 | 17 | 0 | 1 | 18 |
| public/monaco-editor/esm/vs/platform/quickinput | 7 | 858 | 129 | 7 | 994 |
| public/monaco-editor/esm/vs/platform/quickinput/browser | 5 | 815 | 112 | 5 | 932 |
| public/monaco-editor/esm/vs/platform/quickinput/common | 2 | 43 | 17 | 2 | 62 |
| public/monaco-editor/esm/vs/platform/registry | 1 | 17 | 4 | 1 | 22 |
| public/monaco-editor/esm/vs/platform/registry/common | 1 | 17 | 4 | 1 | 22 |
| public/monaco-editor/esm/vs/platform/severityIcon | 1 | 63 | 4 | 1 | 68 |
| public/monaco-editor/esm/vs/platform/severityIcon/common | 1 | 63 | 4 | 1 | 68 |
| public/monaco-editor/esm/vs/platform/storage | 1 | 134 | 22 | 1 | 157 |
| public/monaco-editor/esm/vs/platform/storage/common | 1 | 134 | 22 | 1 | 157 |
| public/monaco-editor/esm/vs/platform/telemetry | 2 | 3 | 4 | 2 | 9 |
| public/monaco-editor/esm/vs/platform/telemetry/common | 2 | 3 | 4 | 2 | 9 |
| public/monaco-editor/esm/vs/platform/theme | 6 | 725 | 120 | 6 | 851 |
| public/monaco-editor/esm/vs/platform/theme/browser | 1 | 47 | 4 | 1 | 52 |
| public/monaco-editor/esm/vs/platform/theme/common | 5 | 678 | 116 | 5 | 799 |
| public/monaco-editor/esm/vs/platform/undoRedo | 2 | 1,084 | 53 | 2 | 1,139 |
| public/monaco-editor/esm/vs/platform/undoRedo/common | 2 | 1,084 | 53 | 2 | 1,139 |
| public/monaco-editor/esm/vs/platform/workspace | 2 | 64 | 4 | 2 | 70 |
| public/monaco-editor/esm/vs/platform/workspace/common | 2 | 64 | 4 | 2 | 70 |
| public/monaco-editor/esm/vs/platform/workspaces | 1 | 23 | 4 | 1 | 28 |
| public/monaco-editor/esm/vs/platform/workspaces/common | 1 | 23 | 4 | 1 | 28 |
| public/monaco-editor/min | 98 | 22,241 | 11,781 | 3,893 | 37,915 |
| public/monaco-editor/min/vs | 98 | 22,241 | 11,781 | 3,893 | 37,915 |
| public/monaco-editor/min/vs/base | 1 | 12 | 8 | 2 | 22 |
| public/monaco-editor/min/vs/base/worker | 1 | 12 | 8 | 2 | 22 |
| public/monaco-editor/min/vs/basic-languages | 76 | 281 | 504 | 61 | 846 |
| public/monaco-editor/min/vs/basic-languages/abap | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/apex | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/azcli | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/bat | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/bicep | 1 | 3 | 9 | 0 | 12 |
| public/monaco-editor/min/vs/basic-languages/cameligo | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/clojure | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/coffee | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/cpp | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/csharp | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/csp | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/css | 1 | 6 | 6 | 1 | 13 |
| public/monaco-editor/min/vs/basic-languages/dart | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/dockerfile | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/ecl | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/elixir | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/flow9 | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/fsharp | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/go | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/graphql | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/handlebars | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/hcl | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/html | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/ini | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/java | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/javascript | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/julia | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/kotlin | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/less | 1 | 5 | 6 | 1 | 12 |
| public/monaco-editor/min/vs/basic-languages/lexon | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/liquid | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/lua | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/m3 | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/markdown | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/mips | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/msdax | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/mysql | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/objective-c | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/pascal | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/pascaligo | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/perl | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/pgsql | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/php | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/pla | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/postiats | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/powerquery | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/powershell | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/protobuf | 1 | 5 | 6 | 1 | 12 |
| public/monaco-editor/min/vs/basic-languages/pug | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/python | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/qsharp | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/r | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/razor | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/redis | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/redshift | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/restructuredtext | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/ruby | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/rust | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/sb | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/scala | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/scheme | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/scss | 1 | 6 | 6 | 1 | 13 |
| public/monaco-editor/min/vs/basic-languages/shell | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/solidity | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/sophia | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/sparql | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/sql | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/st | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/swift | 1 | 4 | 9 | 1 | 14 |
| public/monaco-editor/min/vs/basic-languages/systemverilog | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/tcl | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/twig | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/typescript | 1 | 2 | 9 | 0 | 11 |
| public/monaco-editor/min/vs/basic-languages/vb | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/xml | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/basic-languages/yaml | 1 | 4 | 6 | 1 | 11 |
| public/monaco-editor/min/vs/editor | 12 | 874 | 122 | 97 | 1,093 |
| public/monaco-editor/min/vs/language | 8 | 21,070 | 11,141 | 3,732 | 35,943 |
| public/monaco-editor/min/vs/language/css | 2 | 45 | 12 | 21 | 78 |
| public/monaco-editor/min/vs/language/html | 2 | 264 | 12 | 192 | 468 |
| public/monaco-editor/min/vs/language/json | 2 | 36 | 12 | 5 | 53 |
| public/monaco-editor/min/vs/language/typescript | 2 | 20,725 | 11,105 | 3,514 | 35,344 |
| scripts | 3 | 338 | 44 | 52 | 434 |
| src | 350 | 32,840 | 1,882 | 2,581 | 37,303 |
| src/components | 36 | 1,705 | 72 | 202 | 1,979 |
| src/components/AlertTips | 2 | 46 | 0 | 7 | 53 |
| src/components/AuthProvider | 1 | 42 | 0 | 10 | 52 |
| src/components/AuthResource | 1 | 23 | 0 | 3 | 26 |
| src/components/ChooseGateWay | 1 | 13 | 0 | 3 | 16 |
| src/components/CustomProTable | 1 | 32 | 30 | 3 | 65 |
| src/components/CustomTable | 1 | 6 | 0 | 3 | 9 |
| src/components/EchartsRender | 1 | 54 | 3 | 9 | 66 |
| src/components/Editor | 1 | 14 | 1 | 4 | 19 |
| src/components/HistoryTabs | 2 | 145 | 1 | 14 | 160 |
| src/components/Layout | 6 | 511 | 24 | 57 | 592 |
| src/components/Layout/BreadcrumbNav | 2 | 69 | 0 | 9 | 78 |
| src/components/Layout/UpdataPasswordNode | 1 | 116 | 0 | 13 | 129 |
| src/components/LeftMenu | 1 | 93 | 0 | 7 | 100 |
| src/components/ModalCustomTable | 1 | 133 | 13 | 13 | 159 |
| src/components/ModifyPassword | 1 | 85 | 0 | 9 | 94 |
| src/components/NoticeList | 1 | 127 | 0 | 9 | 136 |
| src/components/PageTitle | 1 | 11 | 0 | 3 | 14 |
| src/components/PageView | 2 | 23 | 0 | 4 | 27 |
| src/components/RenderTags | 2 | 134 | 0 | 15 | 149 |
| src/components/RequireAuth | 1 | 18 | 0 | 3 | 21 |
| src/components/RequireData | 1 | 3 | 0 | 1 | 4 |
| src/components/SearchCard | 1 | 17 | 0 | 3 | 20 |
| src/components/SelectApiTable | 2 | 63 | 0 | 8 | 71 |
| src/components/TigBtn | 2 | 36 | 0 | 7 | 43 |
| src/components/Title | 2 | 33 | 0 | 5 | 38 |
| src/hooks | 13 | 331 | 28 | 50 | 409 |
| src/page | 275 | 29,491 | 1,675 | 2,205 | 33,371 |
| src/page/404 | 1 | 12 | 0 | 2 | 14 |
| src/page/AuditManagement | 6 | 324 | 20 | 33 | 377 |
| src/page/AuditManagement/ApiAudit | 2 | 117 | 10 | 13 | 140 |
| src/page/AuditManagement/Subscribe | 2 | 172 | 10 | 14 | 196 |
| src/page/BigScreen | 21 | 1,531 | 62 | 128 | 1,721 |
| src/page/BigScreen/components | 16 | 931 | 27 | 85 | 1,043 |
| src/page/BigScreen/components/DelayProbability | 1 | 62 | 0 | 5 | 67 |
| src/page/BigScreen/components/Empty | 2 | 24 | 0 | 5 | 29 |
| src/page/BigScreen/components/ErrorCount | 1 | 55 | 0 | 6 | 61 |
| src/page/BigScreen/components/InletFlow | 1 | 63 | 0 | 6 | 69 |
| src/page/BigScreen/components/OutFlow | 1 | 63 | 0 | 7 | 70 |
| src/page/BigScreen/components/PageConainer | 2 | 39 | 20 | 9 | 68 |
| src/page/BigScreen/components/RequestCount | 2 | 63 | 2 | 6 | 71 |
| src/page/BigScreen/components/Respond | 2 | 178 | 3 | 16 | 197 |
| src/page/BigScreen/components/ServiceUseRatio | 2 | 134 | 1 | 8 | 143 |
| src/page/BigScreen/components/ServiceUseRatioA | 2 | 250 | 1 | 17 | 268 |
| src/page/GatewayList | 13 | 1,318 | 76 | 91 | 1,485 |
| src/page/GatewayList/PackageManagement | 3 | 419 | 46 | 25 | 490 |
| src/page/GatewayList/components | 8 | 747 | 16 | 53 | 816 |
| src/page/GatewayList/components/AddInterface | 2 | 145 | 4 | 13 | 162 |
| src/page/GatewayList/components/EditGateWay | 1 | 30 | 4 | 3 | 37 |
| src/page/GatewayList/components/NewGateWay | 1 | 73 | 0 | 6 | 79 |
| src/page/GatewayList/components/NewPackage | 1 | 267 | 4 | 19 | 290 |
| src/page/GatewayList/components/TelescopicCapacity | 2 | 220 | 4 | 10 | 234 |
| src/page/InterfaceIntegration | 5 | 1,057 | 69 | 66 | 1,192 |
| src/page/InterfaceIntegration/BackendService | 1 | 250 | 22 | 12 | 284 |
| src/page/InterfaceIntegration/BackgroundServiceApi | 1 | 309 | 20 | 18 | 347 |
| src/page/InterfaceIntegration/DebugApi | 1 | 453 | 27 | 30 | 510 |
| src/page/Login | 2 | 156 | 1 | 17 | 174 |
| src/page/ProcessOrchestration | 130 | 12,509 | 694 | 1,009 | 14,212 |
| src/page/ProcessOrchestration/DataSource | 1 | 101 | 8 | 7 | 116 |
| src/page/ProcessOrchestration/Edit | 2 | 173 | 7 | 17 | 197 |
| src/page/ProcessOrchestration/List | 1 | 114 | 10 | 9 | 133 |
| src/page/ProcessOrchestration/MessageConversion | 1 | 200 | 24 | 12 | 236 |
| src/page/ProcessOrchestration/MsgBodyManag | 1 | 192 | 19 | 13 | 224 |
| src/page/ProcessOrchestration/MsgCovertDetail | 1 | 81 | 2 | 5 | 88 |
| src/page/ProcessOrchestration/MsgDetail | 1 | 117 | 8 | 7 | 132 |
| src/page/ProcessOrchestration/PathManagement | 1 | 93 | 15 | 8 | 116 |
| src/page/ProcessOrchestration/Project | 1 | 117 | 11 | 8 | 136 |
| src/page/ProcessOrchestration/Service | 1 | 140 | 30 | 13 | 183 |
| src/page/ProcessOrchestration/Shape | 13 | 613 | 31 | 47 | 691 |
| src/page/ProcessOrchestration/components | 74 | 7,428 | 362 | 612 | 8,402 |
| src/page/ProcessOrchestration/components/AddConfigService | 1 | 210 | 3 | 14 | 227 |
| src/page/ProcessOrchestration/components/AddDataSource | 1 | 134 | 0 | 11 | 145 |
| src/page/ProcessOrchestration/components/AddService | 1 | 76 | 0 | 7 | 83 |
| src/page/ProcessOrchestration/components/AssignLinkDiagram | 6 | 1,273 | 119 | 87 | 1,479 |
| src/page/ProcessOrchestration/components/Config | 45 | 3,782 | 86 | 314 | 4,182 |
| src/page/ProcessOrchestration/components/Config/Type | 38 | 3,047 | 61 | 260 | 3,368 |
| src/page/ProcessOrchestration/components/CopyFlow | 1 | 55 | 0 | 6 | 61 |
| src/page/ProcessOrchestration/components/DragShape | 2 | 53 | 0 | 6 | 59 |
| src/page/ProcessOrchestration/components/EditAssignKeyValue | 1 | 64 | 0 | 9 | 73 |
| src/page/ProcessOrchestration/components/EditAssignParams | 1 | 80 | 0 | 10 | 90 |
| src/page/ProcessOrchestration/components/EditContext | 1 | 117 | 0 | 10 | 127 |
| src/page/ProcessOrchestration/components/EditMsgBody | 1 | 158 | 4 | 15 | 177 |
| src/page/ProcessOrchestration/components/EditMsgCovert | 1 | 163 | 3 | 17 | 183 |
| src/page/ProcessOrchestration/components/EditProject | 1 | 102 | 3 | 12 | 117 |
| src/page/ProcessOrchestration/components/JsonCovertSchema | 1 | 102 | 0 | 11 | 113 |
| src/page/ProcessOrchestration/components/PathManagement | 1 | 191 | 7 | 13 | 211 |
| src/page/ProcessOrchestration/components/SelectMsg | 1 | 86 | 0 | 8 | 94 |
| src/page/ProcessOrchestration/components/SelectPath | 1 | 32 | 0 | 4 | 36 |
| src/page/ProcessOrchestration/components/SelectTransform | 1 | 84 | 1 | 7 | 92 |
| src/page/ProcessOrchestration/components/Start | 2 | 50 | 0 | 6 | 56 |
| src/page/ProcessOrchestration/components/TestService | 1 | 169 | 125 | 14 | 308 |
| src/page/ProcessOrchestration/components/Tools | 2 | 401 | 11 | 29 | 441 |
| src/page/ProcessOrchestration/hooks | 20 | 1,136 | 39 | 116 | 1,291 |
| src/page/ProcessOrchestration/utils | 9 | 984 | 85 | 95 | 1,164 |
| src/page/ReportForm | 13 | 1,127 | 101 | 67 | 1,295 |
| src/page/ReportForm/PublishReport | 2 | 200 | 42 | 14 | 256 |
| src/page/ReportForm/ReportDetails | 1 | 99 | 0 | 5 | 104 |
| src/page/ReportForm/StatisticsDetails | 1 | 111 | 1 | 4 | 116 |
| src/page/ReportForm/StatisticsReport | 1 | 158 | 10 | 9 | 177 |
| src/page/ReportForm/SubscribeReport | 2 | 200 | 42 | 13 | 255 |
| src/page/ReportForm/components | 4 | 300 | 6 | 16 | 322 |
| src/page/ReportForm/components/charts | 3 | 249 | 6 | 10 | 265 |
| src/page/ReportForm/components/queryFilter | 1 | 51 | 0 | 6 | 57 |
| src/page/ServiceRelease | 45 | 7,166 | 399 | 475 | 8,040 |
| src/page/ServiceRelease/AccessStrategyDetail | 1 | 136 | 11 | 11 | 158 |
| src/page/ServiceRelease/AuthStrategyDetail | 1 | 136 | 11 | 11 | 158 |
| src/page/ServiceRelease/FlowControl | 1 | 218 | 25 | 13 | 256 |
| src/page/ServiceRelease/FlowControlDetails | 1 | 135 | 11 | 12 | 158 |
| src/page/ServiceRelease/InterfaceGroup | 1 | 73 | 1 | 5 | 79 |
| src/page/ServiceRelease/InterfaceGroupDetails | 2 | 282 | 7 | 13 | 302 |
| src/page/ServiceRelease/InterfaceGroupManagement | 1 | 206 | 4 | 10 | 220 |
| src/page/ServiceRelease/InterfaceManagement | 1 | 175 | 7 | 15 | 197 |
| src/page/ServiceRelease/ProxyCache | 1 | 218 | 28 | 13 | 259 |
| src/page/ServiceRelease/ProxyCacheDetails | 1 | 184 | 11 | 12 | 207 |
| src/page/ServiceRelease/SafelyControl | 5 | 723 | 52 | 54 | 829 |
| src/page/ServiceRelease/ServiceMonitoring | 1 | 179 | 3 | 7 | 189 |
| src/page/ServiceRelease/VersionManagement | 1 | 168 | 17 | 12 | 197 |
| src/page/ServiceRelease/components | 24 | 4,138 | 211 | 279 | 4,628 |
| src/page/ServiceRelease/components/AddAccessStrategy | 1 | 220 | 2 | 17 | 239 |
| src/page/ServiceRelease/components/AddApi | 2 | 273 | 25 | 19 | 317 |
| src/page/ServiceRelease/components/AddAuthStrategy | 1 | 238 | 4 | 20 | 262 |
| src/page/ServiceRelease/components/AddFlowControl | 1 | 359 | 0 | 18 | 377 |
| src/page/ServiceRelease/components/AddInputParameter | 1 | 115 | 0 | 11 | 126 |
| src/page/ServiceRelease/components/AddResInformation | 1 | 63 | 0 | 9 | 72 |
| src/page/ServiceRelease/components/ApiSubCert | 1 | 4 | 0 | 2 | 6 |
| src/page/ServiceRelease/components/Audit | 1 | 86 | 0 | 7 | 93 |
| src/page/ServiceRelease/components/EditBackendService | 1 | 136 | 8 | 12 | 156 |
| src/page/ServiceRelease/components/EditInterfaceGroup | 1 | 105 | 0 | 11 | 116 |
| src/page/ServiceRelease/components/EditProxyCacheStrategy | 1 | 194 | 3 | 13 | 210 |
| src/page/ServiceRelease/components/EditVersion | 1 | 357 | 15 | 22 | 394 |
| src/page/ServiceRelease/components/ImportExcel | 1 | 103 | 12 | 9 | 124 |
| src/page/ServiceRelease/components/ImportSwagger | 1 | 96 | 12 | 9 | 117 |
| src/page/ServiceRelease/components/InterfaceHeader | 1 | 116 | 18 | 12 | 146 |
| src/page/ServiceRelease/components/InterfaceSetting | 1 | 211 | 7 | 3 | 221 |
| src/page/ServiceRelease/components/PublishGateWay | 2 | 136 | 3 | 10 | 149 |
| src/page/ServiceRelease/components/PublishInterface | 2 | 1,079 | 83 | 64 | 1,226 |
| src/page/ServiceRelease/components/ServiceAddress | 1 | 1 | 0 | 1 | 2 |
| src/page/ServiceRelease/components/ServiceMonitoringTable | 1 | 208 | 19 | 8 | 235 |
| src/page/ServiceRelease/utils | 1 | 75 | 0 | 2 | 77 |
| src/page/ServiceSubscription | 14 | 1,485 | 83 | 87 | 1,655 |
| src/page/ServiceSubscription/CraDetails | 1 | 128 | 6 | 11 | 145 |
| src/page/ServiceSubscription/CraManagement | 1 | 217 | 13 | 13 | 243 |
| src/page/ServiceSubscription/SubscriptionList | 3 | 382 | 36 | 15 | 433 |
| src/page/ServiceSubscription/components | 7 | 715 | 28 | 42 | 785 |
| src/page/ServiceSubscription/components/Audit | 1 | 86 | 0 | 7 | 93 |
| src/page/ServiceSubscription/components/CraStepTwoForm | 1 | 109 | 0 | 7 | 116 |
| src/page/ServiceSubscription/components/CreateSub | 1 | 72 | 0 | 6 | 78 |
| src/page/ServiceSubscription/components/EditCra | 1 | 147 | 11 | 11 | 169 |
| src/page/ServiceSubscription/components/Subscription | 1 | 141 | 3 | 6 | 150 |
| src/page/ServiceSubscription/components/SubscriptionColumns | 1 | 154 | 14 | 3 | 171 |
| src/page/SystemManagement | 23 | 2,723 | 158 | 218 | 3,099 |
| src/page/SystemManagement/AuditManagement | 3 | 289 | 25 | 31 | 345 |
| src/page/SystemManagement/HospitalManagement | 1 | 114 | 11 | 8 | 133 |
| src/page/SystemManagement/LogDetail | 2 | 277 | 8 | 28 | 313 |
| src/page/SystemManagement/LogManagement | 2 | 294 | 5 | 10 | 309 |
| src/page/SystemManagement/Menu | 1 | 156 | 17 | 11 | 184 |
| src/page/SystemManagement/Role | 1 | 117 | 14 | 10 | 141 |
| src/page/SystemManagement/UserGroupManagement | 1 | 117 | 14 | 10 | 141 |
| src/page/SystemManagement/UserManagement | 1 | 181 | 14 | 10 | 205 |
| src/page/SystemManagement/components | 9 | 1,095 | 43 | 94 | 1,232 |
| src/page/SystemManagement/components/EditHospital | 1 | 122 | 7 | 11 | 140 |
| src/page/SystemManagement/components/EditMenu | 1 | 285 | 9 | 17 | 311 |
| src/page/SystemManagement/components/EditRole | 2 | 298 | 10 | 25 | 333 |
| src/page/SystemManagement/components/EditUser | 1 | 175 | 10 | 15 | 200 |
| src/page/SystemManagement/components/EditUserGroup | 1 | 129 | 7 | 14 | 150 |
| src/page/SystemManagement/components/LogDetail | 2 | 79 | 0 | 10 | 89 |
| src/service | 9 | 450 | 1 | 25 | 476 |
| src/store | 4 | 214 | 12 | 18 | 244 |
| src/utils | 7 | 517 | 54 | 51 | 622 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)