/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.31.1(5a1b4999493d49c857497ad481d73a737439f305)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/editor/editor.main.nls.ko", {
	"vs/base/browser/ui/actionbar/actionViewItems": [
		"{0}({1})",
	],
	"vs/base/browser/ui/findinput/findInput": [
		"입력",
	],
	"vs/base/browser/ui/findinput/findInputCheckboxes": [
		"대/소문자 구분",
		"단어 단위로",
		"정규식 사용",
	],
	"vs/base/browser/ui/findinput/replaceInput": [
		"입력",
		"대/소문자 보존",
	],
	"vs/base/browser/ui/iconLabel/iconLabelHover": [
		"로드 중...",
	],
	"vs/base/browser/ui/inputbox/inputBox": [
		"오류: {0}",
		"경고: {0}",
		"정보: {0}",
		"기록용",
	],
	"vs/base/browser/ui/keybindingLabel/keybindingLabel": [
		"바인딩 안 됨",
	],
	"vs/base/browser/ui/menu/menu": [
		"{0}({1})",
	],
	"vs/base/browser/ui/tree/abstractTree": [
		"지우기",
		"형식을 기준으로 필터링 사용 안 함",
		"형식을 기준으로 필터링 사용",
		"찾은 요소 없음",
		"{1}개 요소 중 {0}개 일치",
	],
	"vs/base/common/actions": [
		"(비어 있음)",
	],
	"vs/base/common/errorMessage": [
		"{0}: {1}",
		"시스템 오류가 발생했습니다({0}).",
		"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.",
		"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.",
		"{0}(총 {1}개의 오류)",
		"알 수 없는 오류가 발생했습니다. 자세한 내용은 로그를 참조하세요.",
	],
	"vs/base/common/keybindingLabels": [
		"Ctrl",
		"<Shift>",
		"<Alt>",
		"Windows",
		"Ctrl",
		"<Shift>",
		"<Alt>",
		"슈퍼",
		"제어",
		"<Shift>",
		"옵션",
		"명령",
		"제어",
		"<Shift>",
		"<Alt>",
		"Windows",
		"제어",
		"<Shift>",
		"<Alt>",
		"슈퍼",
	],
	"vs/base/parts/quickinput/browser/quickInput": [
		"뒤로",
		"입력을 확인하려면 \'Enter\' 키를 누르고, 취소하려면 \'Esc\' 키를 누르세요.",
		"{0} / {1}",
		"결과의 범위를 축소하려면 입력하세요.",
		"{0}개 결과",
		"{0} 선택됨",
		"확인",
		"사용자 지정",
		"뒤로({0})",
		"뒤로",
	],
	"vs/base/parts/quickinput/browser/quickInputList": [
		"빠른 입력",
	],
	"vs/editor/browser/controller/coreCommands": [
		"더 긴 줄로 이동하는 경우에도 끝에 고정",
		"더 긴 줄로 이동하는 경우에도 끝에 고정",
		"보조 커서가 제거됨",
	],
	"vs/editor/browser/controller/textAreaHandler": [
		"편집기",
		"현재 편집기에 액세스할 수 없습니다. 옵션을 보려면 {0}을(를) 누릅니다.",
	],
	"vs/editor/browser/core/keybindingCancellation": [
		"편집기에서 취소 가능한 작업(예: \'참조 피킹\')을 실행하는지 여부",
	],
	"vs/editor/browser/editorExtensions": [
		"실행 취소(&&U)",
		"실행 취소",
		"다시 실행(&&R)",
		"다시 실행",
		"모두 선택(&&S)",
		"모두 선택",
	],
	"vs/editor/browser/widget/codeEditorWidget": [
		"커서 수는 {0}(으)로 제한되었습니다.",
	],
	"vs/editor/browser/widget/diffEditorWidget": [
		"diff 편집기의 삽입에 대한 줄 데코레이션입니다.",
		"diff 편집기의 제거에 대한 줄 데코레이션입니다.",
		"파일 1개가 너무 커서 파일을 비교할 수 없습니다.",
	],
	"vs/editor/browser/widget/diffReview": [
		"Diff 검토에서 \'삽입\'의 아이콘입니다.",
		"Diff 검토에서 \'제거\'의 아이콘입니다.",
		"Diff 검토에서 \'닫기\'의 아이콘입니다.",
		"닫기",
		"변경된 줄 없음",
		"선 1개 변경됨",
		"줄 {0}개 변경됨",
		"차이 {0}/{1}: 원래 줄 {2}, {3}, 수정된 줄 {4}, {5}",
		"비어 있음",
		"{0} 변경되지 않은 줄 {1}",
		"{0} 원래 줄 {1} 수정된 줄 {2}",
		"+ {0} 수정된 줄 {1}",
		"- {0} 원래 줄 {1}",
		"다음 다른 항목으로 이동",
		"다음 다른 항목으로 이동",
	],
	"vs/editor/browser/widget/inlineDiffMargin": [
		"삭제된 줄 복사",
		"삭제된 줄 복사",
		"변경된 줄 복사",
		"변경된 줄 복사",
		"삭제된 줄 복사({0})",
		"변경된 줄({0}) 복사",
		"이 변경 내용 되돌리기",
		"삭제된 줄 복사({0})",
		"변경된 줄({0}) 복사",
	],
	"vs/editor/common/config/commonEditorConfig": [
		"편집기",
		"탭 한 개에 해당하는 공백 수입니다. `#editor.detectIndentation#`이 켜져 있는 경우 이 설정은 파일 콘텐츠에 따라 재정의됩니다.",
		"\'탭\' 키를 누를 때 공백을 삽입합니다. `#editor.detectIndentation#`이 켜져 있는 경우 이 설정은 파일 콘텐츠에 따라 재정의됩니다.",
		"파일을 열 때 파일 콘텐츠를 기반으로 `#editor.tabSize#`와 `#editor.insertSpaces#`가 자동으로 검색되는지 여부를 제어합니다.",
		"끝에 자동 삽입된 공백을 제거합니다.",
		"큰 파일에 대한 특수 처리로, 메모리를 많이 사용하는 특정 기능을 사용하지 않도록 설정합니다.",
		"문서 내 단어를 기반으로 완성을 계산할지 여부를 제어합니다.",
		"활성 문서에서만 단어를 제안합니다.",
		"같은 언어의 모든 열린 문서에서 단어를 제안합니다.",
		"모든 열린 문서에서 단어를 제안합니다.",
		"단어 기반 완성이 컴퓨팅되는 문서에서 제어합니다.",
		"모든 색 테마에 대해 의미 체계 강조 표시를 사용합니다.",
		"모든 색 테마에 대해 의미 체계 강조 표시를 사용하지 않습니다.",
		"의미 체계 강조 표시는 현재 색 테마의 `semanticHighlighting` 설정에 따라 구성됩니다.",
		"semanticHighlighting이 지원하는 언어에 대해 표시되는지 여부를 제어합니다.",
		"해당 콘텐츠를 두 번 클릭하거나 \'Esc\' 키를 누르더라도 Peek 편집기를 열린 상태로 유지합니다.",
		"이 길이를 초과하는 줄은 성능상의 이유로 토큰화되지 않습니다.",
		"들여쓰기를 늘리거나 줄이는 대괄호 기호를 정의합니다.",
		"여는 대괄호 문자 또는 문자열 시퀀스입니다.",
		"닫는 대괄호 문자 또는 문자열 시퀀스입니다.",
		"대괄호 쌍 색 지정을 사용하는 경우 중첩 수준에 따라 색이 지정된 대괄호 쌍을 정의합니다.",
		"여는 대괄호 문자 또는 문자열 시퀀스입니다.",
		"닫는 대괄호 문자 또는 문자열 시퀀스입니다.",
		"diff 계산이 취소된 후 밀리초 단위로 시간을 제한합니다. 제한 시간이 없는 경우 0을 사용합니다.",
		"차이를 계산할 최대 파일 크기(MB)입니다. 제한이 없으면 0을 사용합니다.",
		"diff 편집기에서 diff를 나란히 표시할지 인라인으로 표시할지를 제어합니다.",
		"사용하도록 설정하면 Diff 편집기가 선행 또는 후행 공백의 변경 내용을 무시합니다.",
		"diff 편집기에서 추가/제거된 변경 내용에 대해 +/- 표시기를 표시하는지 여부를 제어합니다.",
		"편집기에서 CodeLens를 표시할 것인지 여부를 제어합니다.",
		"줄이 바뀌지 않습니다.",
		"뷰포트 너비에서 줄이 바뀝니다.",
		"`#editor.wordWrap#` 설정에 따라 줄이 바뀝니다.",
	],
	"vs/editor/common/config/editorOptions": [
		"편집기가 스크린 리더가 연결되면 플랫폼 API를 사용하여 감지합니다.",
		"편집기가 화면 읽기 프로그램과 함께 사용되도록 영구적으로 최적화되며, 자동 줄 바꿈이 사용하지 않도록 설정됩니다.",
		"편집기가 스크린 리더 사용을 위해 최적화되지 않습니다.",
		"편집기를 화면 읽기 프로그램에 최적화된 모드로 실행할지 여부를 제어합니다. 사용하도록 설정하면 자동 줄 바꿈이 사용하지 않도록 설정됩니다.",
		"주석을 달 때 공백 문자를 삽입할지 여부를 제어합니다.",
		"빈 줄을 줄 주석에 대한 토글, 추가 또는 제거 작업으로 무시해야 하는지를 제어합니다.",
		"선택 영역 없이 현재 줄 복사 여부를 제어합니다.",
		"입력하는 동안 일치 항목을 찾기 위한 커서 이동 여부를 제어합니다.",
		"편집기 선택 영역에서 검색 문자열을 시드하지 마세요.",
		"커서 위치의 단어를 포함하여 항상 편집기 선택 영역에서 검색 문자열을 시드합니다.",
		"편집기 선택 영역에서만 검색 문자열을 시드하세요.",
		"편집기 선택에서 Find Widget의 검색 문자열을 시딩할지 여부를 제어합니다.",
		"선택 영역에서 찾기를 자동으로 켜지 않습니다(기본값).",
		"선택 영역에서 찾기를 항상 자동으로 켭니다.",
		"여러 줄의 콘텐츠를 선택하면 선택 항목에서 찾기가 자동으로 켜집니다.",
		"선택 영역에서 찾기를 자동으로 설정하는 조건을 제어합니다.",
		"macOS에서 Find Widget이 공유 클립보드 찾기를 읽을지 수정할지 제어합니다.",
		"위젯 찾기에서 편집기 맨 위에 줄을 추가해야 하는지 여부를 제어합니다. true인 경우 위젯 찾기가 표시되면 첫 번째 줄 위로 스크롤할 수 있습니다.",
		"더 이상 일치하는 항목이 없을 때 검색을 처음이나 끝에서 자동으로 다시 시작할지 여부를 제어합니다.",
		"글꼴 합자(\'calt\' 및 \'liga\' 글꼴 기능)를 사용하거나 사용하지 않도록 설정합니다. \'font-feature-settings\' CSS 속성의 세분화된 제어를 위해 문자열로 변경합니다.",
		"명시적 \'font-feature-settings\' CSS 속성입니다. 합자를 켜거나 꺼야 하는 경우에만 부울을 대신 전달할 수 있습니다.",
		"글꼴 합자 또는 글꼴 기능을 구성합니다. CSS \'font-feature-settings\' 속성의 값에 대해 합자 또는 문자열을 사용하거나 사용하지 않도록 설정하기 위한 부울일 수 있습니다.",
		"글꼴 크기(픽셀)를 제어합니다.",
		"\"표준\" 및 \"굵게\" 키워드 또는 1~1000 사이의 숫자만 허용됩니다.",
		"글꼴 두께를 제어합니다. \"표준\" 및 \"굵게\" 키워드 또는 1~1000 사이의 숫자를 허용합니다.",
		"결과 Peek 뷰 표시(기본)",
		"기본 결과로 이동하여 Peek 보기를 표시합니다.",
		"기본 결과로 이동하고 다른 항목에 대해 peek 없는 탐색을 사용하도록 설정",
		"이 설정은 더 이상 사용되지 않습니다. 대신 \'editor.editor.gotoLocation.multipleDefinitions\' 또는 \'editor.editor.gotoLocation.multipleImplementations\'와 같은 별도의 설정을 사용하세요.",
		"여러 대상 위치가 있는 경우 \'정의로 이동\' 명령 동작을 제어합니다.",
		"여러 대상 위치가 있는 경우 \'유형 정의로 이동\' 명령 동작을 제어합니다.",
		"여러 대상 위치가 있는 경우 \'Go to Declaration\' 명령 동작을 제어합니다.",
		"여러 대상 위치가 있는 경우 \'구현으로 이동\' 명령 동작을 제어합니다.",
		"여러 대상 위치가 있는 경우 \'참조로 이동\' 명령 동작을 제어합니다.",
		"\'정의로 이동\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.",
		"\'형식 정의로 이동\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.",
		"\'선언으로 이동\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.",
		"\'구현으로 이동\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.",
		"\'참조로 이동\'의 결과가 현재 위치일 때 실행되는 대체 명령 ID입니다.",
		"호버 표시 여부를 제어합니다.",
		"호버가 표시되기 전까지의 지연 시간(밀리초)을 제어합니다.",
		"마우스를 해당 항목 위로 이동할 때 호버를 계속 표시할지 여부를 제어합니다.",
		"공백이 있는 경우 선 위에 마우스를 가져가는 것을 표시하는 것을 선호합니다.",
		"편집기에서 코드 동작 전구를 사용하도록 설정합니다.",
		"편집기에서 인레이 힌트를 사용하도록 설정합니다.",
		"편집기에서 인레이 힌트의 글꼴 크기를 제어합니다. 기본값인 `#editor.fontSize#`의 90%는 구성된 값이 `5`보다 작거나 편집기 글꼴 크기보다 큰 경우 사용됩니다.",
		"편집기에서 인레이 힌트의 글꼴 모음을 제어합니다. 공백으로 설정하면 `#editor.fontFamily#`가 사용됩니다.",
		"선 높이를 제어합니다. \r\n - 0을 사용하여 글꼴 크기에서 줄 높이를 자동으로 계산합니다.\r\n - 0에서 8 사이의 값은 글꼴 크기의 승수로 사용됩니다.\r\n - 8보다 크거나 같은 값이 유효 값으로 사용됩니다.",
		"미니맵 표시 여부를 제어합니다.",
		"미니맵의 크기는 편집기 내용과 동일하며 스크롤할 수 있습니다.",
		"편집기의 높이를 맞추기 위해 필요에 따라 미니맵이 확장되거나 축소됩니다(스크롤 없음).",
		"미니맵을 편집기보다 작게 유지할 수 있도록 필요에 따라 미니맵이 축소됩니다(스크롤 없음).",
		"미니맵의 크기를 제어합니다.",
		"미니맵을 렌더링할 측면을 제어합니다.",
		"미니맵 슬라이더가 표시되는 시기를 제어합니다.",
		"미니맵에 그려진 콘텐츠의 배율: 1, 2 또는 3.",
		"줄의 실제 문자(색 블록 아님)를 렌더링합니다.",
		"최대 특정 수의 열을 렌더링하도록 미니맵의 너비를 제한합니다.",
		"편집기의 위쪽 가장자리와 첫 번째 줄 사이의 공백을 제어합니다.",
		"편집기의 아래쪽 가장자리와 마지막 줄 사이의 공백을 제어합니다.",
		"입력과 동시에 매개변수 문서와 유형 정보를 표시하는 팝업을 사용하도록 설정합니다.",
		"매개변수 힌트 메뉴의 주기 혹은 목록의 끝에 도달하였을때 종료할 것인지 여부를 결정합니다.",
		"문자열 내에서 빠른 제안을 사용합니다.",
		"주석 내에서 빠른 제안을 사용합니다.",
		"문자열 및 주석 외부에서 빠른 제안을 사용합니다.",
		"입력하는 동안 제안을 자동으로 표시할지 여부를 제어합니다.",
		"줄 번호는 렌더링되지 않습니다.",
		"줄 번호는 절대값으로 렌더링 됩니다.",
		"줄 번호는 커서 위치에서 줄 간격 거리로 렌더링 됩니다.",
		"줄 번호는 매 10 줄마다 렌더링이 이루어집니다.",
		"줄 번호의 표시 여부를 제어합니다.",
		"이 편집기 눈금자에서 렌더링할 고정 폭 문자 수입니다.",
		"이 편집기 눈금자의 색입니다.",
		"특정 수의 고정 폭 문자 뒤에 세로 눈금자를 렌더링합니다. 여러 눈금자의 경우 여러 값을 사용합니다. 배열이 비어 있는 경우 눈금자가 그려지지 않습니다.",
		"세로 스크롤 막대는 필요한 경우에만 표시됩니다.",
		"세로 스크롤 막대가 항상 표시됩니다.",
		"세로 스크롤 막대를 항상 숨깁니다.",
		"세로 스크롤 막대의 표시 유형을 제어합니다.",
		"가로 스크롤 막대는 필요한 경우에만 표시됩니다.",
		"가로 스크롤 막대가 항상 표시됩니다.",
		"가로 스크롤 막대를 항상 숨깁니다.",
		"가로 스크롤 막대의 표시 유형을 제어합니다.",
		"세로 스크롤 막대의 너비입니다.",
		"가로 스크롤 막대의 높이입니다.",
		"클릭이 페이지별로 스크롤되는지 또는 클릭 위치로 이동할지 여부를 제어합니다.",
		"기본이 아닌 모든 ASCII 문자를 강조 표시할지 여부를 제어합니다. U+0020과 U+007E 사이의 문자, 탭, 줄 바꿈 및 캐리지 리턴만 기본 ASCII로 간주됩니다.",
		"공백만 예약하거나 너비가 전혀 없는 문자를 강조 표시할지 여부를 제어합니다.",
		"현재 사용자 로캘에서 공통되는 문자를 제외한 기본 ASCII 문자와 혼동할 수 있는 문자를 강조 표시할지 여부를 제어합니다.",
		"주석의 문자도 유니코드 강조 표시를 받아야 하는지 여부를 제어합니다.",
		"강조 표시되지 않는 허용된 문자를 정의합니다.",
		"편집기에서 인라인 제안을 자동으로 표시할지 여부를 제어합니다.",
		"대괄호 쌍 색 지정이 활성화되었는지 여부를 제어합니다. 대괄호 강조 색상을 재정의하려면 \'workbench.colorCustomizations\'를 사용하세요.",
		"대괄호 쌍 가이드를 사용하도록 설정합니다.",
		"활성 대괄호 쌍에 대해서만 대괄호 쌍 가이드를 사용하도록 설정합니다.",
		"대괄호 쌍 가이드를 비활성화합니다.",
		"대괄호 쌍 안내션의 사용 여부를 제어합니다.",
		"수직 대괄호 쌍 가이드에 추가하여 수평 가이드를 사용하도록 설정합니다.",
		"활성 대괄호 쌍에 대해서만 수평 가이드를 사용하도록 설정합니다.",
		"수평 대괄호 쌍 가이드를 비활성화합니다.",
		"가로 대괄호 쌍 안내선의 사용 여부를 제어합니다.",
		"대괄호 쌍 안내션의 사용 여부를 제어합니다.",
		"편집기에서 들여쓰기 가이드를 렌더링할지를 제어합니다.",
		"편집기에서 활성 들여쓰기 가이드를 강조 표시할지 여부를 제어합니다.",
		"커서의 텍스트 오른쪽을 덮어 쓰지않고 제안을 삽입합니다.",
		"제안을 삽입하고 커서의 오른쪽 텍스트를 덮어씁니다.",
		"완료를 수락할 때 단어를 덮어쓸지 여부를 제어합니다. 이것은 이 기능을 선택하는 확장에 따라 다릅니다.",
		"제안 필터링 및 정렬에서 작은 오타를 설명하는지 여부를 제어합니다.",
		"정렬할 때 커서 근처에 표시되는 단어를 우선할지를 제어합니다.",
		"저장된 제안 사항 선택 항목을 여러 작업 영역 및 창에서 공유할 것인지 여부를 제어합니다(`#editor.suggestSelection#` 필요).",
		"활성 코드 조각이 빠른 제안을 방지하는지 여부를 제어합니다.",
		"제안의 아이콘을 표시할지 여부를 제어합니다.",
		"제안 위젯 하단의 상태 표시줄 가시성을 제어합니다.",
		"편집기에서 제안 결과를 미리볼지 여부를 제어합니다.",
		"제안 세부 정보가 레이블과 함께 인라인에 표시되는지 아니면 세부 정보 위젯에만 표시되는지를 제어합니다.",
		"이 설정은 더 이상 사용되지 않습니다. 이제 제안 위젯의 크기를 조정할 수 있습니다.",
		"이 설정은 더 이상 사용되지 않습니다. 대신 \'editor.suggest.showKeywords\'또는 \'editor.suggest.showSnippets\'와 같은 별도의 설정을 사용하세요.",
		"사용하도록 설정되면 IntelliSense에 `메서드` 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'함수\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'생성자\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'사용되지 않음\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'필드\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'변수\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'클래스\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'구조\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'인터페이스\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'모듈\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'속성\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'이벤트\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 `연산자` 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'단위\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'값\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'상수\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'열거형\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 `enumMember` 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'키워드\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'텍스트\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'색\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 `파일` 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'참조\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'사용자 지정 색\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'폴더\' 제안이 표시됩니다.",
		"사용하도록 설정된 경우 IntelliSense에 \'typeParameter\' 제안이 표시됩니다.",
		"사용하도록 설정되면 IntelliSense에 \'코드 조각\' 제안이 표시됩니다.",
		"IntelliSense를 사용하도록 설정하면 `user`-제안이 표시됩니다.",
		"IntelliSense를 사용하도록 설정한 경우 `issues`-제안을 표시합니다.",
		"선행 및 후행 공백을 항상 선택해야 하는지 여부입니다.",
		"커밋 문자에 대한 제안을 허용할지를 제어합니다. 예를 들어 JavaScript에서는 세미콜론(\';\')이 제안을 허용하고 해당 문자를 입력하는 커밋 문자일 수 있습니다.",
		"텍스트를 변경할 때 `Enter` 키를 사용한 제안만 허용합니다.",
		"\'Tab\' 키 외에 \'Enter\' 키에 대한 제안도 허용할지를 제어합니다. 새 줄을 삽입하는 동작과 제안을 허용하는 동작 간의 모호함을 없앨 수 있습니다.",
		"화면 읽기 프로그램에서 한 번에 읽을 수 있는 편집기 줄 수를 제어합니다. 화면 읽기 프로그램을 검색하면 기본값이 500으로 자동 설정됩니다. 경고: 기본값보다 큰 수의 경우 성능에 영향을 미칩니다.",
		"편집기 콘텐츠",
		"언어 구성을 사용하여 대괄호를 자동으로 닫을 경우를 결정합니다.",
		"커서가 공백의 왼쪽에 있는 경우에만 대괄호를 자동으로 닫습니다.",
		"사용자가 여는 괄호를 추가한 후 편집기에서 괄호를 자동으로 닫을지 여부를 제어합니다.",
		"인접한 닫는 따옴표 또는 대괄호가 자동으로 삽입된 경우에만 제거합니다.",
		"삭제할 때 편집기에서 인접한 닫는 따옴표 또는 대괄호를 제거해야 할지를 제어합니다.",
		"닫기 따옴표 또는 대괄호가 자동으로 삽입된 경우에만 해당 항목 위에 입력합니다.",
		"편집자가 닫는 따옴표 또는 대괄호 위에 입력할지 여부를 제어합니다.",
		"언어 구성을 사용하여 따옴표를 자동으로 닫을 경우를 결정합니다.",
		"커서가 공백의 왼쪽에 있는 경우에만 따옴표를 자동으로 닫습니다.",
		"사용자가 여는 따옴표를 추가한 후 편집기에서 따옴표를 자동으로 닫을지 여부를 제어합니다.",
		"편집기는 들여쓰기를 자동으로 삽입하지 않습니다.",
		"편집기는 현재 줄의 들여쓰기를 유지합니다.",
		"편집기는 현재 줄의 들여쓰기를 유지하고 언어 정의 대괄호를 사용합니다.",
		"편집기는 현재 줄의 들여쓰기를 유지하고 언어 정의 대괄호를 존중하며 언어별로 정의된 특별 EnterRules를 호출합니다.",
		"편집기는 현재 줄의 들여쓰기를 유지하고, 언어 정의 대괄호를 존중하고, 언어에 의해 정의된 특별 EnterRules를 호출하고, 언어에 의해 정의된 들여쓰기 규칙을 존중합니다.",
		"사용자가 줄을 입력, 붙여넣기, 이동 또는 들여쓰기 할 때 편집기에서 들여쓰기를 자동으로 조정하도록 할지 여부를 제어합니다.",
		"언어 구성을 사용하여 선택 항목을 자동으로 둘러쌀 경우를 결정합니다.",
		"대괄호가 아닌 따옴표로 둘러쌉니다.",
		"따옴표가 아닌 대괄호로 둘러쌉니다.",
		"따옴표 또는 대괄호 입력 시 편집기가 자동으로 선택 영역을 둘러쌀지 여부를 제어합니다.",
		"들여쓰기에 공백을 사용할 때 탭 문자의 선택 동작을 에뮬레이트합니다. 선택 영역이 탭 정지에 고정됩니다.",
		"편집기에서 CodeLens를 표시할 것인지 여부를 제어합니다.",
		"CodeLens의 글꼴 패밀리를 제어합니다.",
		"CodeLens의 글꼴 크기(픽셀)를 제어합니다. \'0\'으로 설정하면 `#editor.fontSize#`의 90%가 사용됩니다.",
		"편집기에서 인라인 색 데코레이터 및 색 선택을 렌더링할지를 제어합니다.",
		"마우스와 키로 선택한 영역에서 열을 선택하도록 설정합니다.",
		"구문 강조 표시를 클립보드로 복사할지 여부를 제어합니다.",
		"커서 애니메이션 스타일을 제어합니다.",
		"매끄러운 캐럿 애니메이션의 사용 여부를 제어합니다.",
		"커서 스타일을 제어합니다.",
		"커서 주위에 표시되는 선행 및 후행 줄의 최소 수를 제어합니다. 일부 다른 편집기에서는 \'scrollOff\' 또는 \'scrollOffset\'이라고 합니다.",
		"\'cursorSurroundingLines\'는 키보드 나 API를 통해 트리거될 때만 적용됩니다.",
		"`cursorSurroundingLines`는 항상 적용됩니다.",
		"\'cursorSurroundingLines\'를 적용해야 하는 경우를 제어합니다.",
		"`#editor.cursorStyle#` 설정이 \'line\'으로 설정되어 있을 때 커서의 넓이를 제어합니다.",
		"편집기에서 끌어서 놓기로 선택 영역을 이동할 수 있는지 여부를 제어합니다.",
		"\'Alt\' 키를 누를 때 스크롤 속도 승수입니다.",
		"편집기에 코드 접기가 사용하도록 설정되는지 여부를 제어합니다.",
		"사용 가능한 경우 언어별 접기 전략을 사용합니다. 그렇지 않은 경우 들여쓰기 기반 전략을 사용합니다.",
		"들여쓰기 기반 접기 전략을 사용합니다.",
		"접기 범위를 계산하기 위한 전략을 제어합니다.",
		"편집기에서 접힌 범위를 강조 표시할지 여부를 제어합니다.",
		"편집기에서 가져오기 범위를 자동으로 축소할지 여부를 제어합니다.",
		"접힌 줄이 줄을 펼친 후 빈 콘텐츠를 클릭할지 여부를 제어합니다.",
		"글꼴 패밀리를 제어합니다.",
		"붙여넣은 콘텐츠의 서식을 편집기에서 자동으로 지정할지 여부를 제어합니다. 포맷터를 사용할 수 있어야 하며 포맷터가 문서에서 범위의 서식을 지정할 수 있어야 합니다.",
		"입력 후 편집기에서 자동으로 줄의 서식을 지정할지 여부를 제어합니다.",
		"편집기에서 세로 문자 모양 여백을 렌더링할지 여부를 제어합니다. 문자 모양 여백은 주로 디버깅에 사용됩니다.",
		"커서가 개요 눈금자에서 가려져야 하는지 여부를 제어합니다.",
		"문자 간격(픽셀)을 제어합니다.",
		"편집기에서 연결된 편집이 사용하도록 설정되었는지를 제어합니다. 언어에 따라 관련 기호(예: HTML 태그)가 편집 중에 업데이트됩니다.",
		"편집기에서 링크를 감지하고 클릭할 수 있게 만들지 여부를 제어합니다.",
		"일치하는 대괄호를 강조 표시합니다.",
		"마우스 휠 스크롤 이벤트의 `deltaX` 및 `deltaY`에서 사용할 승수입니다.",
		"마우스 휠을 사용할 때 \'Ctrl\' 키를 누르고 있으면 편집기의 글꼴을 확대/축소합니다.",
		"여러 커서가 겹치는 경우 커서를 병합합니다.",
		"Windows와 Linux의 \'Control\'을 macOS의 \'Command\'로 매핑합니다.",
		"Windows와 Linux의 \'Alt\'를 macOS의 \'Option\'으로 매핑합니다.",
		"마우스로 여러 커서를 추가할 때 사용할 수정자입니다. [정의로 이동] 및 [링크 열기] 마우스 제스처가 멀티커서 수정자와 충돌하지 않도록 조정됩니다. [자세한 정보](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).",
		"각 커서는 텍스트 한 줄을 붙여넣습니다.",
		"각 커서는 전체 텍스트를 붙여넣습니다.",
		"붙여넣은 텍스트의 줄 수가 커서 수와 일치하는 경우 붙여넣기를 제어합니다.",
		"편집기에서 의미 체계 기호 항목을 강조 표시할지 여부를 제어합니다.",
		"개요 눈금자 주위에 테두리를 그릴지 여부를 제어합니다.",
		"Peek를 여는 동안 트리에 포커스",
		"미리 보기를 열 때 편집기에 포커스",
		"미리 보기 위젯에서 인라인 편집기에 포커스를 둘지 또는 트리에 포커스를 둘지를 제어합니다.",
		"이동 정의 마우스 제스처가 항상 미리 보기 위젯을 열지 여부를 제어합니다.",
		"빠른 제안을 표시하기 전까지의 지연 시간(밀리초)을 제어합니다.",
		"편집기가 유형에 따라 자동으로 이름을 바꿀지 여부를 제어합니다.",
		"사용되지 않습니다. 대신 `editor.linkedEditing`을 사용하세요.",
		"편집기에서 제어 문자를 렌더링할지를 제어합니다.",
		"파일이 줄 바꿈으로 끝나면 마지막 줄 번호를 렌더링합니다.",
		"제본용 여백과 현재 줄을 모두 강조 표시합니다.",
		"편집기가 현재 줄 강조 표시를 렌더링하는 방식을 제어합니다.",
		"편집기에 포커스가 있는 경우에만 편집기에서 현재 줄 강조 표시를 렌더링해야 하는지 제어합니다.",
		"단어 사이의 공백 하나를 제외한 공백 문자를 렌더링합니다.",
		"선택한 텍스트에서만 공백 문자를 렌더링합니다.",
		"후행 공백 문자만 렌더링합니다.",
		"편집기에서 공백 문자를 렌더링할 방법을 제어합니다.",
		"선택 항목의 모서리를 둥글게 할지 여부를 제어합니다.",
		"편집기에서 가로로 스크롤되는 범위를 벗어나는 추가 문자의 수를 제어합니다.",
		"편집기에서 마지막 줄 이후로 스크롤할지 여부를 제어합니다.",
		"세로와 가로로 동시에 스크롤할 때에만 주축을 따라서 스크롤합니다. 트랙패드에서 세로로 스크롤할 때 가로 드리프트를 방지합니다.",
		"Linux 주 클립보드의 지원 여부를 제어합니다.",
		"편집기가 선택 항목과 유사한 일치 항목을 강조 표시해야하는지 여부를 제어합니다.",
		"접기 컨트롤을 항상 표시합니다.",
		"마우스가 여백 위에 있을 때에만 접기 컨트롤을 표시합니다.",
		"여백의 접기 컨트롤이 표시되는 시기를 제어합니다.",
		"사용하지 않는 코드의 페이드 아웃을 제어합니다.",
		"취소선 사용되지 않는 변수를 제어합니다.",
		"다른 제안 위에 조각 제안을 표시합니다.",
		"다른 제안 아래에 조각 제안을 표시합니다.",
		"다른 제안과 함께 조각 제안을 표시합니다.",
		"코드 조각 제안을 표시하지 않습니다.",
		"코드 조각이 다른 추천과 함께 표시되는지 여부 및 정렬 방법을 제어합니다.",
		"편집기에서 애니메이션을 사용하여 스크롤할지 여부를 제어합니다.",
		"제안 위젯의 글꼴 크기입니다. \'0\'으로 설정하면 \'#editor.fontSize#\'의 값이 사용됩니다.",
		"제안 위젯의 줄 높이입니다. \'0\'으로 설정하면 `#editor.lineHeight#`의 값이 사용됩니다. 최솟값은 8입니다.",
		"트리거 문자를 입력할 때 제안을 자동으로 표시할지 여부를 제어합니다.",
		"항상 첫 번째 제안을 선택합니다.",
		"`log`가 최근에 완료되었으므로 추가 입력에서 제안을 선택하지 않은 경우 최근 제안을 선택하세요(예: `console.| -> console.log`).",
		"해당 제안을 완료한 이전 접두사에 따라 제안을 선택합니다(예: `co -> console` 및 `con -> const`).",
		"제안 목록을 표시할 때 제한이 미리 선택되는 방식을 제어합니다.",
		"탭 완료는 탭을 누를 때 가장 일치하는 제안을 삽입합니다.",
		"탭 완성을 사용하지 않도록 설정합니다.",
		"접두사가 일치하는 경우 코드 조각을 탭 완료합니다. \'quickSuggestions\'를 사용하지 않을 때 가장 잘 작동합니다.",
		"탭 완성을 사용하도록 설정합니다.",
		"비정상적인 줄 종결자가 자동으로 제거됩니다.",
		"비정상적인 줄 종결자가 무시됩니다.",
		"제거할 비정상적인 줄 종결자 프롬프트입니다.",
		"문제를 일으킬 수 있는 비정상적인 줄 종결자를 제거합니다.",
		"탭 정지 뒤에 공백을 삽입 및 삭제합니다.",
		"단어 관련 탐색 또는 작업을 수행할 때 단어 구분 기호로 사용할 문자입니다.",
		"줄이 바뀌지 않습니다.",
		"뷰포트 너비에서 줄이 바뀝니다.",
		"`#editor.wordWrapColumn#`에서 줄이 바뀝니다.",
		"뷰포트의 최소값 및 `#editor.wordWrapColumn#`에서 줄이 바뀝니다.",
		"줄 바꿈 여부를 제어합니다.",
		"`#editor.wordWrap#`이 `wordWrapColumn` 또는 \'bounded\'인 경우 편집기의 열 줄 바꿈을 제어합니다.",
		"들여쓰기가 없습니다. 줄 바꿈 행이 열 1에서 시작됩니다.",
		"줄 바꿈 행의 들여쓰기가 부모와 동일합니다.",
		"줄 바꿈 행이 부모 쪽으로 +1만큼 들여쓰기됩니다.",
		"줄 바꿈 행이 부모 쪽으로 +2만큼 들여쓰기됩니다.",
		"줄 바꿈 행의 들여쓰기를 제어합니다.",
		"모든 문자가 동일한 너비라고 가정합니다. 이 알고리즘은 고정 폭 글꼴과 문자 모양의 너비가 같은 특정 스크립트(예: 라틴 문자)에 적절히 작동하는 빠른 알고리즘입니다.",
		"래핑 점 계산을 브라우저에 위임합니다. 이 알고리즘은 매우 느려서 대용량 파일의 경우 중단될 수 있지만 모든 경우에 적절히 작동합니다.",
		"래핑 점을 계산하는 알고리즘을 제어합니다.",
	],
	"vs/editor/common/editorContextKeys": [
		"편집기 텍스트에 포커스가 있는지 여부(커서가 깜박임)",
		"편집기 또는 편집기 위젯에 포커스가 있는지 여부(예: 포커스가 찾기 위젯에 있음)",
		"편집기 또는 서식 있는 텍스트 입력에 포커스가 있는지 여부(커서가 깜박임)",
		"편집기가 읽기 전용인지 여부",
		"컨텍스트가 diff 편집기인지 여부",
		"\'editor.columnSelection\'을 사용하도록 설정되어 있는지 여부",
		"편집기에 선택된 텍스트가 있는지 여부",
		"편집기에 여러 개의 선택 항목이 있는지 여부",
		"\'Tab\' 키를 누르면 편집기 밖으로 포커스가 이동하는지 여부",
		"편집기 호버가 표시되는지 여부",
		"편집기가 더 큰 편집기(예: 전자 필기장)에 속해 있는지 여부",
		"편집기의 언어 식별자",
		"편집기에 완성 항목 공급자가 있는지 여부",
		"편집기에 코드 작업 공급자가 있는지 여부",
		"편집기에 CodeLens 공급자가 있는지 여부",
		"편집기에 정의 공급자가 있는지 여부",
		"편집기에 선언 공급자가 있는지 여부",
		"편집기에 구현 공급자가 있는지 여부",
		"편집기에 형식 정의 공급자가 있는지 여부",
		"편집기에 호버 공급자가 있는지 여부",
		"편집기에 문서 강조 표시 공급자가 있는지 여부",
		"편집기에 문서 기호 공급자가 있는지 여부",
		"편집기에 참조 공급자가 있는지 여부",
		"편집기에 이름 바꾸기 공급자가 있는지 여부",
		"편집기에 시그니처 도움말 공급자가 있는지 여부",
		"편집기에 인라인 힌트 공급자가 있는지 여부",
		"편집기에 문서 서식 공급자가 있는지 여부",
		"편집기에 문서 선택 서식 공급자가 있는지 여부",
		"편집기에 여러 개의 문서 서식 공급자가 있는지 여부",
		"편집기에 여러 개의 문서 선택 서식 공급자가 있는지 여부",
	],
	"vs/editor/common/model/editStack": [
		"입력하는 중",
	],
	"vs/editor/common/modes/modesRegistry": [
		"일반 텍스트",
	],
	"vs/editor/common/standaloneStrings": [
		"없음 선택",
		"줄 {0}, 열 {1}({2} 선택됨)입니다.",
		"행 {0}, 열 {1}",
		"{0} 선택 항목({1}자 선택됨)",
		"{0} 선택 항목",
		"이제 \'accessibilitySupport\' 설정을 \'on\'으로 변경합니다.",
		"지금 편집기 접근성 문서 페이지를 여세요.",
		"차이 편집기의 읽기 전용 창에서.",
		"diff 편집기 창에서.",
		" 읽기 전용 코드 편집기에서",
		" 코드 편집기에서",
		"화면 판독기 사용에 최적화되도록 편집기를 구성하려면 지금 Command+E를 누르세요.",
		"화면 판독기에 사용할 수 있도록 편집기를 최적화하려면 지금 Ctrl+E를 누르세요.",
		"에디터를 화면 판독기와 함께 사용하기에 적합하도록 구성했습니다.",
		"편집기는 화면 판독기 사용을 위해 절대로 최적화되지 않도록 구성됩니다. 현재로서는 그렇지 않습니다.",
		"현재 편집기에서 <Tab> 키를 누르면 포커스가 다음 포커스 가능한 요소로 이동합니다. {0}을(를) 눌러서 이 동작을 설정/해제합니다.",
		"현재 편집기에서 <Tab> 키를 누르면 포커스가 다음 포커스 가능한 요소로 이동합니다. {0} 명령은 현재 키 바인딩으로 트리거할 수 없습니다.",
		"현재 편집기에서 <Tab> 키를 누르면 탭 문자가 삽입됩니다. {0}을(를) 눌러서 이 동작을 설정/해제합니다.",
		"현재 편집기에서 <Tab> 키를 누르면 탭 문자가 삽입됩니다. {0} 명령은 현재 키 바인딩으로 트리거할 수 없습니다.",
		"Command+H를 눌러 편집기 접근성과 관련된 자세한 정보가 있는 브라우저 창을 여세요.",
		"Ctrl+H를 눌러 편집기 접근성과 관련된 자세한 정보가 있는 브라우저 창을 엽니다.",
		"이 도구 설명을 해제하고 Esc 키 또는 Shift+Esc를 눌러서 편집기로 돌아갈 수 있습니다.",
		"접근성 도움말 표시",
		"개발자: 검사 토큰",
		"줄/열로 이동...",
		"빠른 액세스 공급자 모두 표시",
		"명령 팔레트",
		"명령 표시 및 실행",
		"기호로 가서...",
		"범주별 기호로 이동...",
		"편집기 콘텐츠",
		"접근성 옵션은 Alt+F1을 눌러여 합니다.",
		"고대비 테마로 전환",
		"{1} 파일에서 편집을 {0}개 했습니다.",
	],
	"vs/editor/common/view/editorColorRegistry": [
		"커서 위치의 줄 강조 표시에 대한 배경색입니다.",
		"커서 위치의 줄 테두리에 대한 배경색입니다.",
		"빠른 열기 및 찾기 기능 등을 통해 강조 표시된 영역의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"강조 영역 주변의 테두리에 대한 배경색입니다",
		"강조 표시된 기호(예: 정의로 이동 또는 다음/이전 기호로 이동)의 배경색입니다. 이 색상은 기본 장식을 숨기지 않도록 불투명하지 않아야 합니다.",
		"강조 표시된 기호 주위의 테두리 배경색입니다.",
		"편집기 커서 색입니다.",
		"편집기 커서의 배경색입니다. 블록 커서와 겹치는 글자의 색상을 사용자 정의할 수 있습니다.",
		"편집기의 공백 문자 색입니다.",
		"편집기 들여쓰기 안내선 색입니다.",
		"활성 편집기 들여쓰기 안내선 색입니다.",
		"편집기 줄 번호 색입니다.",
		"편집기 활성 영역 줄번호 색상",
		"ID는 사용되지 않습니다. 대신 \'editorLineNumber.activeForeground\'를 사용하세요.",
		"편집기 활성 영역 줄번호 색상",
		"편집기 눈금의 색상입니다.",
		"편집기 코드 렌즈의 전경색입니다.",
		"일치하는 괄호 뒤의 배경색",
		"일치하는 브래킷 박스의 색상",
		"개요 눈금 경계의 색상입니다.",
		"편집기 개요 눈금자의 배경색입니다. 미니맵이 사용하도록 설정되어 편집기의 오른쪽에 배치된 경우에만 사용됩니다.",
		"편집기 거터의 배경색입니다. 거터에는 글리프 여백과 행 수가 있습니다.",
		"편집기의 불필요한(사용하지 않는) 소스 코드 테두리 색입니다.",
		"편집기의 불필요한(사용하지 않는) 소스 코드 불투명도입니다. 예를 들어 \"#000000c0\"은 75% 불투명도로 코드를 렌더링합니다. 고대비 테마의 경우 페이드 아웃하지 않고 \'editorUnnecessaryCode.border\' 테마 색을 사용하여 불필요한 코드에 밑줄을 그으세요.",
		"편집기에서 고스트 텍스트의 테두리 색입니다.",
		"편집기에서 고스트 텍스트의 전경색입니다.",
		"편집기에서 고스트 텍스트의 배경색입니다.",
		"범위의 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"오류의 개요 눈금자 마커 색입니다.",
		"경고의 개요 눈금자 마커 색입니다.",
		"정보의 개요 눈금자 마커 색입니다.",
		"대괄호의 전경색(1)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.",
		"대괄호의 전경색(2)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.",
		"대괄호의 전경색(3)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.",
		"대괄호의 전경색(4)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.",
		"대괄호의 전경색(5)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.",
		"대괄호의 전경색(6)입니다. 대괄호 쌍 색 지정을 사용하도록 설정해야 합니다.",
		"예기치 않은 대괄호의 전경색입니다.",
		"비활성 대괄호 쌍 안내선의 배경색입니다(1). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"비활성 대괄호 쌍 안내선의 배경색입니다(2). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"비활성 대괄호 쌍 안내선의 배경색입니다(3). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"비활성 대괄호 쌍 안내선의 배경색입니다(4). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"비활성 대괄호 쌍 안내선의 배경색입니다(5). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"비활성 대괄호 쌍 안내선의 배경색입니다(6). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"활성 대괄호 쌍 안내선의 배경색입니다(1). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"활성 대괄호 쌍 안내선의 배경색입니다(2). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"활성 대괄호 쌍 안내선의 배경색입니다(3). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"활성 대괄호 쌍 안내선의 배경색입니다(4). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"활성 대괄호 쌍 안내선의 배경색입니다(5). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"활성 대괄호 쌍 안내선의 배경색입니다(6). 대괄호 쌍 안내선을 사용하도록 설정해야 합니다.",
		"유니코드 문자를 강조 표시하는 데 사용되는 테두리 색입니다.",
	],
	"vs/editor/contrib/anchorSelect/anchorSelect": [
		"선택 앵커 지점",
		"{0}에 설정된 앵커: {1}",
		"선택 앵커 지점 설정",
		"선택 앵커 지점으로 이동",
		"앵커에서 커서로 선택",
		"선택 앵커 지점 취소",
	],
	"vs/editor/contrib/bracketMatching/bracketMatching": [
		"괄호에 해당하는 영역을 표시자에 채색하여 표시합니다.",
		"대괄호로 이동",
		"괄호까지 선택",
		"대괄호로 이동(&&B)",
	],
	"vs/editor/contrib/caretOperations/caretOperations": [
		"선택한 텍스트를 왼쪽으로 이동",
		"선택한 텍스트를 오른쪽으로 이동",
	],
	"vs/editor/contrib/caretOperations/transpose": [
		"문자 바꾸기",
	],
	"vs/editor/contrib/clipboard/clipboard": [
		"잘라내기(&&T)",
		"잘라내기",
		"잘라내기",
		"잘라내기",
		"복사(&&C)",
		"복사",
		"복사",
		"복사",
		"다음으로 복사",
		"다음으로 복사",
		"붙여넣기(&&P)",
		"붙여넣기",
		"붙여넣기",
		"붙여넣기",
		"구문을 강조 표시하여 복사",
	],
	"vs/editor/contrib/codeAction/codeActionCommands": [
		"실행할 코드 작업의 종류입니다.",
		"반환된 작업이 적용되는 경우를 제어합니다.",
		"항상 반환된 첫 번째 코드 작업을 적용합니다.",
		"첫 번째 반환된 코드 작업을 적용합니다(이 작업만 있는 경우).",
		"반환된 코드 작업을 적용하지 마세요.",
		"기본 코드 작업만 반환되도록 할지 여부를 제어합니다.",
		"코드 작업을 적용하는 중 알 수 없는 오류가 발생했습니다.",
		"빠른 수정...",
		"사용 가능한 코드 동작이 없습니다.",
		"\'{0}\'에 대한 기본 코드 작업을 사용할 수 없음",
		"\'{0}\'에 대한 코드 작업을 사용할 수 없음",
		"사용할 수 있는 기본 코드 작업 없음",
		"사용 가능한 코드 동작이 없습니다.",
		"리팩터링...",
		"\'{0}\'에 대한 기본 리팩터링 없음",
		"\'{0}\'에 대한 리팩터링 없음",
		"기본 설정 리팩터링을 사용할 수 없음",
		"사용 가능한 리펙터링이 없습니다.",
		"소스 작업...",
		"\'{0}\'에 대한 기본 소스 작업을 사용할 수 없음",
		"\'{0}\'에 대한 소스 작업을 사용할 수 없음",
		"사용할 수 있는 기본 원본 작업 없음",
		"사용 가능한 소스 작업이 없습니다.",
		"가져오기 구성",
		"사용 가능한 가져오기 구성 작업이 없습니다.",
		"모두 수정",
		"모든 작업 수정 사용 불가",
		"자동 수정...",
		"사용할 수 있는 자동 수정 없음",
	],
	"vs/editor/contrib/codeAction/lightBulbWidget": [
		"코드 작업 표시. 기본 설정 빠른 수정 사용 가능({0})",
		"코드 작업 표시({0})",
		"코드 작업 표시",
	],
	"vs/editor/contrib/codelens/codelensController": [
		"현재 줄에 대한 코드 렌즈 명령 표시",
	],
	"vs/editor/contrib/colorPicker/colorPickerWidget": [
		"색 옵션을 토글하려면 클릭하세요(rgb/hsl/hex).",
	],
	"vs/editor/contrib/comment/comment": [
		"줄 주석 설정/해제",
		"줄 주석 설정/해제(&&T)",
		"줄 주석 추가",
		"줄 주석 제거",
		"블록 주석 설정/해제",
		"블록 주석 설정/해제(&&B)",
	],
	"vs/editor/contrib/contextmenu/contextmenu": [
		"편집기 상황에 맞는 메뉴 표시",
	],
	"vs/editor/contrib/cursorUndo/cursorUndo": [
		"커서 실행 취소",
		"커서 다시 실행",
	],
	"vs/editor/contrib/find/findController": [
		"찾기",
		"찾기(&&F)",
		"\"정규식 사용\" 플래그를 재정의합니다.\r\n플래그는 미래를 위해 저장되지 않습니다.\r\n0: 아무것도 하지 않음\r\n1: True\r\n2: False",
		"\"전체 단어 일치\" 플래그를 재정의합니다.\r\n플래그는 미래를 위해 저장되지 않습니다.\r\n0: 아무것도 하지 않음\r\n1: True\r\n2: False",
		"\"Math Case\" 플래그를 재정의합니다.\r\n플래그는 미래를 위해 저장되지 않습니다.\r\n0: 아무것도 하지 않음\r\n1: True\r\n2: False",
		"\"케이스 보존\" 플래그를 재정의합니다.\r\n플래그는 미래를 위해 저장되지 않습니다.\r\n0: 아무것도 하지 않음\r\n1: True\r\n2: False",
		"인수로 찾기",
		"선택 영역에서 찾기",
		"다음 찾기",
		"이전 찾기",
		"다음 선택 찾기",
		"이전 선택 찾기",
		"바꾸기",
		"바꾸기(&&R)",
	],
	"vs/editor/contrib/find/findWidget": [
		"편집기 찾기 위젯에서 \'선택 영역에서 찾기\'의 아이콘입니다.",
		"편집기 찾기 위젯이 축소되었음을 나타내는 아이콘입니다.",
		"편집기 찾기 위젯이 확장되었음을 나타내는 아이콘입니다.",
		"편집기 찾기 위젯에서 \'바꾸기\'의 아이콘입니다.",
		"편집기 찾기 위젯에서 \'모두 바꾸기\'의 아이콘입니다.",
		"편집기 찾기 위젯에서 \'이전 찾기\'의 아이콘입니다.",
		"편집기 찾기 위젯에서 \'다음 찾기\'의 아이콘입니다.",
		"찾기",
		"찾기",
		"이전 검색 결과",
		"다음 검색 결과",
		"선택 항목에서 찾기",
		"닫기",
		"바꾸기",
		"바꾸기",
		"바꾸기",
		"모두 바꾸기",
		"바꾸기 설정/해제",
		"처음 {0}개의 결과가 강조 표시되지만 모든 찾기 작업은 전체 텍스트에 대해 수행됩니다.",
		"{1}의 {0}",
		"결과 없음",
		"{0}개 찾음",
		"\'{1}\'에 대한 {0}을(를) 찾음",
		"{2}에서 \'{1}\'에 대한 {0}을(를) 찾음",
		"\'{1}\'에 대한 {0}을(를) 찾음",
		"Ctrl+Enter를 누르면 이제 모든 항목을 바꾸지 않고 줄 바꿈을 삽입합니다. editor.action.replaceAll의 키 바인딩을 수정하여 이 동작을 재정의할 수 있습니다.",
	],
	"vs/editor/contrib/folding/folding": [
		"펼치기",
		"재귀적으로 펼치기",
		"접기",
		"접기 전환",
		"재귀적으로 접기",
		"모든 블록 코멘트를 접기",
		"모든 영역 접기",
		"모든 영역 펼치기",
		"선택한 영역을 제외한 모든 영역 접기",
		"선택한 영역을 제외한 모든 영역 펼치기",
		"모두 접기",
		"모두 펼치기",
		"부모 폴딩으로 이동",
		"이전 접기 범위로 이동",
		"다음 접기 범위로 이동",
		"수준 {0} 접기",
		"접힌 범위의 배경색입니다. 색은 기본 장식을 숨기지 않기 위해 불투명해서는 안 됩니다.",
		"편집기 여백의 접기 컨트롤 색입니다.",
	],
	"vs/editor/contrib/folding/foldingDecorations": [
		"편집기 문자 모양 여백에서 확장된 범위의 아이콘입니다.",
		"편집기 문자 모양 여백에서 축소된 범위의 아이콘입니다.",
	],
	"vs/editor/contrib/fontZoom/fontZoom": [
		"편집기 글꼴 확대",
		"편집기 글꼴 축소",
		"편집기 글꼴 확대/축소 다시 설정",
	],
	"vs/editor/contrib/format/format": [
		"줄 {0}에서 1개 서식 편집을 수행했습니다.",
		"줄 {1}에서 {0}개 서식 편집을 수행했습니다.",
		"줄 {0}과(와) {1} 사이에서 1개 서식 편집을 수행했습니다.",
		"줄 {1}과(와) {2} 사이에서 {0}개 서식 편집을 수행했습니다.",
	],
	"vs/editor/contrib/format/formatActions": [
		"문서 서식",
		"선택 영역 서식",
	],
	"vs/editor/contrib/gotoError/gotoError": [
		"다음 문제로 이동 (오류, 경고, 정보)",
		"다음 마커로 이동의 아이콘입니다.",
		"이전 문제로 이동 (오류, 경고, 정보)",
		"이전 마커로 이동의 아이콘입니다.",
		"파일의 다음 문제로 이동 (오류, 경고, 정보)",
		"다음 문제(&&P)",
		"파일의 이전 문제로 이동 (오류, 경고, 정보)",
		"이전 문제(&&P)",
	],
	"vs/editor/contrib/gotoError/gotoErrorWidget": [
		"오류",
		"경고",
		"정보",
		"힌트",
		"{1}의 {0}입니다. ",
		"문제 {1}개 중 {0}개",
		"문제 {1}개 중 {0}개",
		"편집기 표식 탐색 위젯 오류 색입니다.",
		"편집기 마커 탐색 위젯 오류 제목 배경.",
		"편집기 표식 탐색 위젯 경고 색입니다.",
		"편집기 마커 탐색 위젯 경고 제목 배경.",
		"편집기 표식 탐색 위젯 정보 색입니다.",
		"편집기 마커 탐색 위젯 정보 제목 배경.",
		"편집기 표식 탐색 위젯 배경입니다.",
	],
	"vs/editor/contrib/gotoSymbol/goToCommands": [
		"피킹",
		"정의",
		"\'{0}\'에 대한 정의를 찾을 수 없습니다.",
		"정의를 찾을 수 없음",
		"정의로 이동",
		"측면에서 정의 열기",
		"정의 피킹",
		"선언",
		"\'{0}\'에 대한 선언을 찾을 수 없음",
		"선언을 찾을 수 없음",
		"선언으로 이동",
		"\'{0}\'에 대한 선언을 찾을 수 없음",
		"선언을 찾을 수 없음",
		"선언 미리 보기",
		"형식 정의",
		"\'{0}\'에 대한 형식 정의를 찾을 수 없습니다.",
		"형식 정의를 찾을 수 없습니다.",
		"형식 정의로 이동",
		"형식 정의 미리 보기",
		"구현",
		"\'{0}\'에 대한 구현을 찾을 수 없습니다.",
		"구현을 찾을 수 없습니다.",
		"구현으로 이동",
		"피킹 구현",
		"\'{0}\'에 대한 참조가 없습니다.",
		"참조가 없습니다.",
		"참조로 이동",
		"참조",
		"참조 미리 보기",
		"참조",
		"임의의 기호로 이동",
		"위치",
		"\'{0}\'에 대한 검색 결과가 없음",
		"참조",
		"정의로 이동(&&D)",
		"선언으로 이동(&&D)",
		"형식 정의로 이동(&&T)",
		"구현으로 이동(&&I)",
		"참조로 이동(&&R)",
	],
	"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition": [
		"{0}개 정의를 표시하려면 클릭하세요.",
	],
	"vs/editor/contrib/gotoSymbol/peek/referencesController": [
		"\'참조 피킹\' 또는 \'정의 피킹\'과 같이 참조 피킹이 표시되는지 여부",
		"로드 중...",
		"{0}({1})",
	],
	"vs/editor/contrib/gotoSymbol/peek/referencesTree": [
		"참조 {0}개",
		"참조 {0}개",
		"참조",
	],
	"vs/editor/contrib/gotoSymbol/peek/referencesWidget": [
		"미리 보기를 사용할 수 없음",
		"결과 없음",
		"참조",
	],
	"vs/editor/contrib/gotoSymbol/referencesModel": [
		"{2}열, {1}줄, {0}의 기호",
		"열 {2}, {3}의 줄 {1}에 있는 {0}의 기호",
		"{0}의 기호 1개, 전체 경로 {1}",
		"{1}의 기호 {0}개, 전체 경로 {2}",
		"결과 없음",
		"{0}에서 기호 1개를 찾았습니다.",
		"{1}에서 기호 {0}개를 찾았습니다.",
		"{1}개 파일에서 기호 {0}개를 찾았습니다.",
	],
	"vs/editor/contrib/gotoSymbol/symbolNavigation": [
		"키보드만으로 탐색할 수 있는 기호 위치가 있는지 여부",
		"{1}의 {0} 기호, 다음의 경우 {2}",
		"{1}의 기호 {0}",
	],
	"vs/editor/contrib/hover/hover": [
		"가리키기 표시",
		"정의 미리 보기 가리킨 항목 표시",
	],
	"vs/editor/contrib/hover/markdownHoverParticipant": [
		"로드 중...",
		"성능상의 이유로 긴 줄의 경우 토큰화를 건너뜁니다. 이 항목은 \'editor.maxTokenizationLineLength\'를 통해 구성할 수 있습니다.",
	],
	"vs/editor/contrib/hover/markerHoverParticipant": [
		"문제 보기",
		"빠른 수정을 사용할 수 없음",
		"빠른 수정을 확인하는 중...",
		"빠른 수정을 사용할 수 없음",
		"빠른 수정...",
	],
	"vs/editor/contrib/inPlaceReplace/inPlaceReplace": [
		"이전 값으로 바꾸기",
		"다음 값으로 바꾸기",
	],
	"vs/editor/contrib/indentation/indentation": [
		"들여쓰기를 공백으로 변환",
		"들여쓰기를 탭으로 변환",
		"구성된 탭 크기",
		"현재 파일의 탭 크기 선택",
		"탭을 사용한 들여쓰기",
		"공백을 사용한 들여쓰기",
		"콘텐츠에서 들여쓰기 감지",
		"줄 다시 들여쓰기",
		"선택한 줄 다시 들여쓰기",
	],
	"vs/editor/contrib/inlineCompletions/ghostTextController": [
		"인라인 제안 표시 여부",
		"인라인 제안이 공백으로 시작하는지 여부",
		"인라인 제안이 탭에 의해 삽입되는 것보다 작은 공백으로 시작하는지 여부",
		"다음 인라인 제안 표시",
		"이전 인라인 제안 표시",
		"인라인 제안 트리거",
	],
	"vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant": [
		"다음",
		"이전",
		"수락",
		"제안:",
	],
	"vs/editor/contrib/lineSelection/lineSelection": [
		"선 선택 영역 확장",
	],
	"vs/editor/contrib/linesOperations/linesOperations": [
		"위에 줄 복사",
		"위에 줄 복사(&&C)",
		"아래에 줄 복사",
		"아래에 줄 복사(&&P)",
		"중복된 선택 영역",
		"중복된 선택 영역(&&D)",
		"줄 위로 이동",
		"줄 위로 이동(&&V)",
		"줄 아래로 이동",
		"줄 아래로 이동(&&L)",
		"줄을 오름차순 정렬",
		"줄을 내림차순으로 정렬",
		"중복 라인 삭제",
		"후행 공백 자르기",
		"줄 삭제",
		"줄 들여쓰기",
		"줄 내어쓰기",
		"위에 줄 삽입",
		"아래에 줄 삽입",
		"왼쪽 모두 삭제",
		"우측에 있는 항목 삭제",
		"줄 연결",
		"커서 주위 문자 바꾸기",
		"대문자로 변환",
		"소문자로 변환",
		"단어의 첫 글자를 대문자로 변환",
		"스네이크 표기법으로 변환",
	],
	"vs/editor/contrib/linkedEditing/linkedEditing": [
		"연결된 편집 시작",
		"형식의 편집기에서 자동으로 이름을 바꿀 때의 배경색입니다.",
	],
	"vs/editor/contrib/links/links": [
		"명령 실행",
		"링크로 이동",
		"Cmd+클릭",
		"Ctrl+클릭",
		"Option+클릭",
		"Alt+클릭",
		"명령 {0} 실행",
		"{0} 형식이 올바르지 않으므로 이 링크를 열지 못했습니다",
		"대상이 없으므로 이 링크를 열지 못했습니다.",
		"링크 열기",
	],
	"vs/editor/contrib/message/messageController": [
		"편집기에서 현재 인라인 메시지를 표시하는지 여부",
		"읽기 전용 편집기에서는 편집할 수 없습니다.",
	],
	"vs/editor/contrib/multicursor/multicursor": [
		"커서가 추가됨: {0}",
		"커서가 추가됨: {0}",
		"위에 커서 추가",
		"위에 커서 추가(&&A)",
		"아래에 커서 추가",
		"아래에 커서 추가(&&D)",
		"줄 끝에 커서 추가",
		"줄 끝에 커서 추가(&&U)",
		"맨 아래에 커서 추가",
		"맨 위에 커서 추가",
		"다음 일치 항목 찾기에 선택 항목 추가",
		"다음 항목 추가(&&N)",
		"이전 일치 항목 찾기에 선택 항목 추가",
		"이전 항목 추가(&&R)",
		"다음 일치 항목 찾기로 마지막 선택 항목 이동",
		"마지막 선택 항목을 이전 일치 항목 찾기로 이동",
		"일치 항목 찾기의 모든 항목 선택",
		"모든 항목 선택(&&O)",
		"모든 항목 변경",
	],
	"vs/editor/contrib/parameterHints/parameterHints": [
		"매개 변수 힌트 트리거",
	],
	"vs/editor/contrib/parameterHints/parameterHintsWidget": [
		"다음 매개 변수 힌트 표시의 아이콘입니다.",
		"이전 매개 변수 힌트 표시의 아이콘입니다.",
		"{0}, 힌트",
		"매개 변수 힌트에 있는 활성 항목의 전경색입니다.",
	],
	"vs/editor/contrib/peekView/peekView": [
		"현재 코드 편집기가 피킹 내부에 포함되었는지 여부",
		"닫기",
		"Peek 뷰 제목 영역의 배경색입니다.",
		"Peek 뷰 제목 색입니다.",
		"Peek 뷰 제목 정보 색입니다.",
		"Peek 뷰 테두리 및 화살표 색입니다.",
		"Peek 뷰 결과 목록의 배경색입니다.",
		"Peek 뷰 결과 목록에서 라인 노드의 전경색입니다.",
		"Peek 뷰 결과 목록에서 파일 노드의 전경색입니다.",
		"Peek 뷰 결과 목록에서 선택된 항목의 배경색입니다.",
		"Peek 뷰 결과 목록에서 선택된 항목의 전경색입니다.",
		"Peek 뷰 편집기의 배경색입니다.",
		"Peek 뷰 편집기의 거터 배경색입니다.",
		"Peek 뷰 결과 목록의 일치 항목 강조 표시 색입니다.",
		"Peek 뷰 편집기의 일치 항목 강조 표시 색입니다.",
		"Peek 뷰 편집기의 일치 항목 강조 표시 테두리입니다.",
	],
	"vs/editor/contrib/quickAccess/gotoLineQuickAccess": [
		"우선 텍스트 편집기를 열고 줄로 이동합니다.",
		"줄 {0} 및 문자 {1}(으)로 이동합니다.",
		"{0} 줄로 이동합니다.",
		"현재 줄: {0}, 문자: {1} 이동할 줄 1~{2} 사이의 번호를 입력합니다.",
		"현재 줄: {0}, 문자: {1}. 이동할 줄 번호를 입력합니다.",
	],
	"vs/editor/contrib/quickAccess/gotoSymbolQuickAccess": [
		"기호로 이동하려면 먼저 기호 정보가 있는 텍스트 편집기를 엽니다.",
		"활성 상태의 텍스트 편집기는 기호 정보를 제공하지 않습니다.",
		"일치하는 편집기 기호 없음",
		"편집기 기호 없음",
		"측면에서 열기",
		"하단에 열기",
		"기호({0})",
		"속성({0})",
		"메서드({0})",
		"함수({0})",
		"생성자({0})",
		"변수({0})",
		"클래스({0})",
		"구조체({0})",
		"이벤트({0})",
		"연산자({0})",
		"인터페이스({0})",
		"네임스페이스({0})",
		"패키지({0})",
		"형식 매개 변수({0})",
		"모듈({0})",
		"속성({0})",
		"열거형({0})",
		"열거형 멤버({0})",
		"문자열({0})",
		"파일({0})",
		"배열({0})",
		"숫자({0})",
		"부울({0})",
		"개체({0})",
		"키({0})",
		"필드({0})",
		"상수({0})",
	],
	"vs/editor/contrib/rename/rename": [
		"결과가 없습니다.",
		"위치 이름을 바꾸는 중 알 수 없는 오류가 발생했습니다.",
		"\'{0}\'의 이름을 바꾸는 중",
		"{0} 이름 바꾸기",
		"\'{0}\'을(를) \'{1}\'(으)로 이름을 변경했습니다. 요약: {2}",
		"이름 바꾸기를 통해 편집 내용을 적용하지 못했습니다.",
		"이름 바꾸기를 통해 편집 내용을 계산하지 못했습니다.",
		"기호 이름 바꾸기",
		"이름을 바꾸기 전에 변경 내용을 미리 볼 수 있는 기능 사용/사용 안 함",
	],
	"vs/editor/contrib/rename/renameInputField": [
		"입력 이름 바꾸기 위젯이 표시되는지 여부",
		"입력 이름을 바꾸세요. 새 이름을 입력한 다음 [Enter] 키를 눌러 커밋하세요.",
		"이름 바꾸기 {0}, 미리 보기 {1}",
	],
	"vs/editor/contrib/smartSelect/smartSelect": [
		"선택 영역 확장",
		"선택 영역 확장(&&E)",
		"선택 영역 축소",
		"선택 영역 축소(&&S)",
	],
	"vs/editor/contrib/snippet/snippetController2": [
		"현재 편집기가 코드 조각 모드인지 여부",
		"코드 조각 모드일 때 다음 탭 정지가 있는지 여부",
		"코드 조각 모드일 때 이전 탭 정지가 있는지 여부",
	],
	"vs/editor/contrib/snippet/snippetVariables": [
		"일요일",
		"월요일",
		"화요일",
		"수요일",
		"목요일",
		"금요일",
		"토요일",
		"일",
		"월",
		"화",
		"수",
		"목",
		"금",
		"토",
		"1월",
		"2월",
		"3월",
		"4월",
		"5월",
		"6월",
		"7월",
		"8월",
		"9월",
		"10월",
		"11월",
		"12월",
		"1월",
		"2월",
		"3월",
		"4월",
		"5월",
		"6월",
		"7월",
		"8월",
		"9월",
		"10월",
		"11월",
		"12월",
	],
	"vs/editor/contrib/suggest/suggest": [
		"제안이 표시되는지 여부",
		"제안 세부 정보가 표시되는지 여부",
		"선택할 수 있는 여러 제안이 있는지 여부",
		"현재 제안을 삽입하면 변경 내용이 생성되는지 또는 모든 항목이 이미 입력되었는지 여부",
		"<Enter> 키를 누를 때 제안이 삽입되는지 여부",
		"현재 제안에 삽입 및 바꾸기 동작이 있는지 여부",
		"기본 동작이 삽입인지 또는 바꾸기인지 여부",
		"현재 제안에서 추가 세부 정보를 확인하도록 지원하는지 여부",
	],
	"vs/editor/contrib/suggest/suggestController": [
		"{0}의 {1}개의 수정사항을 수락하는 중",
		"제안 항목 트리거",
		"삽입",
		"삽입",
		"바꾸기",
		"바꾸기",
		"삽입",
		"간단히 표시",
		"더 보기",
		"제안 위젯 크기 다시 설정",
	],
	"vs/editor/contrib/suggest/suggestWidget": [
		"제안 위젯의 배경색입니다.",
		"제안 위젯의 테두리 색입니다.",
		"제안 위젯의 전경색입니다.",
		"제한 위젯에서 선택된 항목의 전경색입니다.",
		"제한 위젯에서 선택된 항목의 아이콘 전경색입니다.",
		"제한 위젯에서 선택된 항목의 배경색입니다.",
		"제안 위젯의 일치 항목 강조 표시 색입니다.",
		"항목에 포커스가 있을 때 추천 위젯에서 일치하는 항목의 색이 강조 표시됩니다.",
		"제안 위젯 상태의 배경색입니다.",
		"로드 중...",
		"제안 항목이 없습니다.",
		"{0}, 문서: {1}",
		"제안",
	],
	"vs/editor/contrib/suggest/suggestWidgetDetails": [
		"닫기",
		"로드 중...",
	],
	"vs/editor/contrib/suggest/suggestWidgetRenderer": [
		"제안 위젯에서 자세한 정보의 아이콘입니다.",
		"자세한 정보",
	],
	"vs/editor/contrib/suggest/suggestWidgetStatus": [
		"{0} ({1})",
	],
	"vs/editor/contrib/symbolIcons/symbolIcons": [
		"배열 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"부울 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"클래스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"색 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안에 표시됩니다.",
		"상수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"생성자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"열거자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"열거자 멤버 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"이벤트 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"필드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"파일 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"폴더 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"함수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"인터페이스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"키 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"키워드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"메서드 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"모듈 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"네임스페이스 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"null 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"숫자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"개체 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"연산자 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"패키지 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"속성 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"참조 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"코드 조각 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"문자열 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"구조 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"텍스트 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 나타납니다.",
		"형식 매개변수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"단위 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
		"변수 기호의 전경색입니다. 이러한 기호는 개요, 이동 경로 및 제안 위젯에 표시됩니다.",
	],
	"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode": [
		"<Tab> 키로 포커스 이동 설정/해제",
		"이제 <Tab> 키를 누르면 포커스가 다음 포커스 가능한 요소로 이동합니다.",
		"이제 <Tab> 키를 누르면 탭 문자가 삽입됩니다.",
	],
	"vs/editor/contrib/tokenization/tokenization": [
		"개발자: 강제로 다시 토큰화",
	],
	"vs/editor/contrib/unicodeHighlighter/unicodeHighlighter": [
		"확장 편집기에 경고 메시지와 함께 표시되는 아이콘입니다.",
		"이 문서에는 기본 ASCII 유니코드 문자가 아닌 문자가 많이 포함되어 있습니다.",
		"이 문서에는 모호한 유니코드 문자가 많이 포함되어 있습니다.",
		"이 문서에는 보이지 않는 유니코드 문자가 많이 포함되어 있습니다.",
		"{0} 문자는 소스 코드에서 더 일반적인 {1} 문자와 혼동될 수 있습니다.",
		"{0} 문자가 보이지 않습니다.",
		"{0} 문자는 기본 ASCII 문자가 아닙니다.",
		"설정 조정",
		"모호한 강조 사용 안 함",
		"모호한 문자 강조 표시 사용 안 함",
		"보이지 않는 강조 사용 안 함",
		"보이지 않는 문자 강조 표시 사용 안 함",
		"ASCII가 문자가 아닌 강조 사용 안 함",
		"기본이 아닌 ASCII 문자 강조 표시 사용 안 함",
		"제외 옵션 표시",
		"{0}(보이지 않는 문자)이(가) 강조 표시되지 않도록 제외",
		"강조 표시에서 {0} 제외",
		"유니코드 강조 표시 옵션 구성",
	],
	"vs/editor/contrib/unusualLineTerminators/unusualLineTerminators": [
		"비정상적인 줄 종결자",
		"비정상적인 줄 종결자가 검색됨",
		"이 파일 ‘\r\n’에 LS(줄 구분 기호) 또는 PS(단락 구분 기호) 같은 하나 이상의 비정상적인 줄 종결자 문자가 포함되어 있습니다.{0}\r\n파일에서 제거하는 것이 좋습니다. `editor.unusualLineTerminators`를 통해 구성할 수 있습니다.",
		"비정상적인 줄 종결자 제거",
		"무시",
	],
	"vs/editor/contrib/wordHighlighter/wordHighlighter": [
		"변수 읽기와 같은 읽기 액세스 중 기호의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"변수에 쓰기와 같은 쓰기 액세스 중 기호의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"변수 읽기와 같은 읽기 액세스 중 기호의 테두리 색입니다.",
		"변수에 쓰기와 같은 쓰기 액세스 중 기호의 테두리 색입니다.",
		"기호 강조 표시의 개요 눈금자 표식 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"쓰기 액세스 기호에 대한 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"다음 강조 기호로 이동",
		"이전 강조 기호로 이동",
		"기호 강조 표시 트리거",
	],
	"vs/editor/contrib/wordOperations/wordOperations": [
		"단어 삭제",
	],
	"vs/platform/actions/browser/menuEntryActionViewItem": [
		"{0}({1})",
		"{0}({1})",
	],
	"vs/platform/configuration/common/configurationRegistry": [
		"기본 언어 구성 재정의",
		"{0}에서 재정의할 설정을 구성합니다.",
		"언어에 대해 재정의할 편집기 설정을 구성합니다.",
		"이 설정은 언어별 구성을 지원하지 않습니다.",
		"언어에 대해 재정의할 편집기 설정을 구성합니다.",
		"이 설정은 언어별 구성을 지원하지 않습니다.",
		"빈 속성을 등록할 수 없음",
		"\'{0}\'을(를) 등록할 수 없습니다. 이는 언어별 편집기 설정을 설명하는 속성 패턴인 \'\\\\[.*\\\\]$\'과(와) 일치합니다. \'configurationDefaults\' 기여를 사용하세요.",
		"\'{0}\'을(를) 등록할 수 없습니다. 이 속성은 이미 등록되어 있습니다.",
	],
	"vs/platform/contextkey/browser/contextKeyService": [
		"컨텍스트 키에 대한 정보를 반환하는 명령",
	],
	"vs/platform/contextkey/common/contextkeys": [
		"운영 체제가 macOS인지 여부",
		"운영 체제가 Linux인지 여부",
		"운영 체제가 Windows인지 여부",
		"플랫폼이 웹 브라우저인지 여부",
		"브라우저 기반이 아닌 플랫폼에서 운영 체제가 macOS인지 여부",
		"운영 체제가 iOS인지 여부",
		"키보드 포커스가 입력 상자 내에 있는지 여부",
	],
	"vs/platform/keybinding/common/abstractKeybindingService": [
		"({0})을(를) 눌렀습니다. 둘째 키는 잠시 기다렸다가 누르십시오...",
		"키 조합({0}, {1})은 명령이 아닙니다.",
	],
	"vs/platform/list/browser/listService": [
		"워크벤치",
		"Windows와 Linux의 \'Control\'을 macOS의 \'Command\'로 매핑합니다.",
		"Windows와 Linux의 \'Alt\'를 macOS의 \'Option\'으로 매핑합니다.",
		"마우스로 트리와 목록의 항목을 다중 선택에 추가할 때 사용할 한정자입니다(예를 들어 탐색기에서 편집기와 SCM 보기를 여는 경우). \'옆에서 열기\' 마우스 제스처(지원되는 경우)는 다중 선택 한정자와 충돌하지 않도록 조정됩니다.",
		"트리와 목록에서 마우스를 사용하여 항목을 여는 방법을 제어합니다(지원되는 경우). 일부 트리와 목록에서는 이 설정을 적용할 수 없는 경우 무시하도록 선택할 수 있습니다.",
		"워크벤치에서 목록 및 트리의 가로 스크롤 여부를 제어합니다. 경고: 이 설정을 켜면 성능에 영향을 미칩니다.",
		"트리 들여쓰기를 픽셀 단위로 제어합니다.",
		"트리에서 들여쓰기 가이드를 렌더링할지 여부를 제어합니다.",
		"목록과 트리에 부드러운 화면 이동 기능이 있는지를 제어합니다.",
		"마우스 휠 스크롤 이벤트의 `deltaX` 및 `deltaY`에서 사용할 승수입니다.",
		"\'Alt\' 키를 누를 때 스크롤 속도 승수입니다.",
		"간단한 키보드 탐색에서는 키보드 입력과 일치하는 요소에 집중합니다. 일치는 접두사에서만 수행됩니다.",
		"키보드 탐색 강조 표시에서는 키보드 입력과 일치하는 요소를 강조 표시합니다. 이후로 탐색에서 위 및 아래로 이동하는 경우 강조 표시된 요소만 트래버스합니다.",
		"키보드 탐색 필터링에서는 키보드 입력과 일치하지 않는 요소를 모두 필터링하여 숨깁니다.",
		"워크벤치의 목록 및 트리 키보드 탐색 스타일을 제어합니다. 간소화하고, 강조 표시하고, 필터링할 수 있습니다.",
		"목록 및 트리에서 키보드 탐색이 입력만으로 자동 트리거되는지 여부를 제어합니다. \'false\'로 설정하면 \'list.toggleKeyboardNavigation\' 명령을 실행할 때만 키보드 탐색이 트리거되어 바로 가기 키를 할당할 수 있습니다.",
		"폴더 이름을 클릭할 때 트리 폴더가 확장되는 방법을 제어합니다. 일부 트리와 목록에서는 이 설정을 적용할 수 없는 경우 무시하도록 선택할 수 있습니다.",
	],
	"vs/platform/markers/common/markers": [
		"오류",
		"경고",
		"정보",
	],
	"vs/platform/quickinput/browser/commandsQuickAccess": [
		"{0}, {1}",
		"최근에 사용한 항목",
		"기타 명령",
		"명령 \'{0}\'에서 오류({1})가 발생했습니다.",
	],
	"vs/platform/quickinput/browser/helpQuickAccess": [
		"전역 명령",
		"편집기 명령",
		"{0}, {1}",
	],
	"vs/platform/theme/common/colorRegistry": [
		"전체 전경색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.",
		"오류 메시지에 대한 전체 전경색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.",
		"레이블과 같이 추가 정보를 제공하는 설명 텍스트의 전경색입니다.",
		"워크벤치 아이콘의 기본 색상입니다.",
		"포커스가 있는 요소의 전체 테두리 색입니다. 이 색은 구성 요소에서 재정의하지 않은 경우에만 사용됩니다.",
		"더 뚜렷이 대비되도록 요소를 다른 요소와 구분하는 요소 주위의 추가 테두리입니다.",
		"더 뚜렷이 대비되도록 요소를 다른 요소와 구분하는 활성 요소 주위의 추가 테두리입니다.",
		"워크벤치의 텍스트 선택(예: 입력 필드 또는 텍스트 영역) 전경색입니다. 편집기 내의 선택에는 적용되지 않습니다.",
		"텍스트 구분자 색상입니다.",
		"텍스트 내 링크의 전경색입니다.",
		"클릭하고 마우스가 올라간 상태의 텍스트 내 링크의 전경색입니다.",
		"미리 서식이 지정된 텍스트 세그먼트의 전경색입니다.",
		"텍스트 내 블록 인용의 전경색입니다.",
		"텍스트 내 블록 인용의 테두리 색입니다.",
		"텍스트 내 코드 블록의 전경색입니다.",
		"편집기 내에서 찾기/바꾸기 같은 위젯의 그림자 색입니다.",
		"입력 상자 배경입니다.",
		"입력 상자 전경입니다.",
		"입력 상자 테두리입니다.",
		"입력 필드에서 활성화된 옵션의 테두리 색입니다.",
		"입력 필드에서 활성화된 옵션의 배경색입니다.",
		"입력 필드에 있는 옵션의 배경 가리키기 색입니다.",
		"입력 필드에서 활성화된 옵션의 전경색입니다.",
		"위치 표시자 텍스트에 대한 입력 상자 전경색입니다.",
		"정보 심각도의 입력 유효성 검사 배경색입니다.",
		"정보 심각도의 입력 유효성 검사 전경색입니다.",
		"정보 심각도의 입력 유효성 검사 테두리 색입니다.",
		"경고 심각도의 입력 유효성 검사 배경색입니다.",
		"경고 심각도의 입력 유효성 검사 전경색입니다.",
		"경고 심각도의 입력 유효성 검사 테두리 색입니다.",
		"오류 심각도의 입력 유효성 검사 배경색입니다.",
		"오류 심각도의 입력 유효성 검사 전경색입니다.",
		"오류 심각도의 입력 유효성 검사 테두리 색입니다.",
		"드롭다운 배경입니다.",
		"드롭다운 목록 배경입니다.",
		"드롭다운 전경입니다.",
		"드롭다운 테두리입니다.",
		"확인란 위젯의 배경색입니다.",
		"확인란 위젯의 전경색입니다.",
		"확인란 위젯의 테두리 색입니다.",
		"단추 기본 전경색입니다.",
		"단추 배경색입니다.",
		"마우스로 가리킬 때 단추 배경색입니다.",
		"버튼 테두리 색입니다.",
		"보조 단추 전경색입니다.",
		"보조 단추 배경색입니다.",
		"마우스로 가리킬 때 보조 단추 배경색입니다.",
		"배지 배경색입니다. 배지는 검색 결과 수와 같은 소량의 정보 레이블입니다.",
		"배지 전경색입니다. 배지는 검색 결과 수와 같은 소량의 정보 레이블입니다.",
		"스크롤되는 보기를 나타내는 스크롤 막대 그림자입니다.",
		"스크롤 막대 슬라이버 배경색입니다.",
		"마우스로 가리킬 때 스크롤 막대 슬라이더 배경색입니다.",
		"클릭된 상태일 때 스크롤 막대 슬라이더 배경색입니다.",
		"장기 작업을 대상으로 표시될 수 있는 진행률 표시줄의 배경색입니다.",
		"편집기에서 오류 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"편집기 내 오류 표시선의 전경색입니다.",
		"편집기에서 오류 상자의 테두리 색입니다.",
		"편집기에서 경고 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"편집기 내 경고 표시선의 전경색입니다.",
		"편집기에서 경고 상자의 테두리 색입니다.",
		"편집기에서 정보 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"편집기 내 정보 표시선의 전경색입니다.",
		"편집기에서 정보 상자의 테두리 색입니다.",
		"편집기에서 힌트 표시선의 전경색입니다.",
		"편집기에서 힌트 상자의 테두리 색입니다.",
		"활성 섀시의 테두리 색입니다.",
		"편집기 배경색입니다.",
		"편집기 기본 전경색입니다.",
		"찾기/바꾸기 같은 편집기 위젯의 배경색입니다.",
		"찾기/바꾸기와 같은 편집기 위젯의 전경색입니다.",
		"편집기 위젯의 테두리 색입니다. 위젯에 테두리가 있고 위젯이 색상을 무시하지 않을 때만 사용됩니다.",
		"편집기 위젯 크기 조정 막대의 테두리 색입니다. 이 색은 위젯에서 크기 조정 막대를 표시하도록 선택하고 위젯에서 색을 재지정하지 않는 경우에만 사용됩니다.",
		"빠른 선택기 배경색. 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.",
		"빠른 선택기 전경색. 이 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.",
		"빠른 선택기 제목 배경색. 이 빠른 선택기 위젯은 명령 팔레트와 같은 선택기를 위한 컨테이너입니다.",
		"그룹화 레이블에 대한 빠른 선택기 색입니다.",
		"그룹화 테두리에 대한 빠른 선택기 색입니다.",
		"키 바인딩 레이블 배경색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.",
		"키 바인딩 레이블 전경색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.",
		"키 바인딩 레이블 테두리 색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.",
		"키 바인딩 레이블 테두리 아래쪽 색입니다. 키 바인딩 레이블은 바로 가기 키를 나타내는 데 사용됩니다.",
		"편집기 선택 영역의 색입니다.",
		"고대비를 위한 선택 텍스트의 색입니다.",
		"비활성 편집기의 선택 항목 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"선택 영역과 동일한 콘텐츠가 있는 영역의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"선택 영역과 동일한 콘텐츠가 있는 영역의 테두리 색입니다.",
		"현재 검색 일치 항목의 색입니다.",
		"기타 검색 일치 항목의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"검색을 제한하는 범위의 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"현재 검색과 일치하는 테두리 색입니다.",
		"다른 검색과 일치하는 테두리 색입니다.",
		"검색을 제한하는 범위의 테두리 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"검색 편집기 쿼리의 색상이 일치합니다.",
		"검색 편집기 쿼리의 테두리 색상이 일치합니다.",
		"호버가 표시된 단어 아래를 강조 표시합니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"편집기 호버의 배경색.",
		"편집기 호버의 전경색입니다.",
		"편집기 호버의 테두리 색입니다.",
		"편집기 호버 상태 표시줄의 배경색입니다.",
		"활성 링크의 색입니다.",
		"인라인 힌트의 전경색",
		"인라인 힌트의 배경색",
		"형식에 대한 인라인 힌트의 전경색",
		"형식에 대한 인라인 힌트의 배경색",
		"매개 변수에 대한 인라인 힌트의 전경색",
		"매개 변수에 대한 인라인 힌트의 배경색",
		"전구 작업 아이콘에 사용되는 색상입니다.",
		"전구 자동 수정 작업 아이콘에 사용되는 색상입니다.",
		"삽입된 텍스트의 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"제거된 텍스트 배경색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"삽입된 텍스트의 윤곽선 색입니다.",
		"제거된 텍스트의 윤곽선 색입니다.",
		"두 텍스트 편집기 사이의 테두리 색입니다.",
		"diff 편집기의 대각선 채우기 색입니다. 대각선 채우기는 diff 나란히 보기에서 사용됩니다.",
		"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 활성 상태인 경우 포커스가 있는 항목의 목록/트리 윤곽선 색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 활성 상태인 경우 선택한 항목의 목록/트리 아이콘 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 비활성 상태인 경우 선택한 항목의 목록/트리 아이콘 전경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 비활성 상태인 경우 포커스가 있는 항목의 목록/트리 배경색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"목록/트리가 비활성 상태인 경우 포커스가 있는 항목의 목록/트리 윤곽선 색입니다. 목록/트리가 활성 상태이면 키보드 포커스를 가지며, 비활성 상태이면 포커스가 없습니다.",
		"마우스로 항목을 가리킬 때 목록/트리 배경입니다.",
		"마우스로 항목을 가리킬 때 목록/트리 전경입니다.",
		"마우스로 항목을 이동할 때 목록/트리 끌어서 놓기 배경입니다.",
		"목록/트리 내에서 검색할 때 일치 항목 강조 표시의 목록/트리 전경색입니다.",
		"목록/트리 내에서 검색할 때 일치 항목의 목록/트리 전경색이 능동적으로 포커스가 있는 항목을 강조 표시합니다.",
		"잘못된 항목에 대한 목록/트리 전경 색(예: 탐색기의 확인할 수 없는 루트).",
		"오류를 포함하는 목록 항목의 전경색입니다.",
		"경고를 포함하는 목록 항목의 전경색입니다.",
		"목록 및 트리에서 형식 필터 위젯의 배경색입니다.",
		"목록 및 트리에서 형식 필터 위젯의 윤곽선 색입니다.",
		"일치하는 항목이 없을 때 목록 및 트리에서 표시되는 형식 필터 위젯의 윤곽선 색입니다.",
		"필터링된 일치 항목의 배경색입니다.",
		"필터링된 일치 항목의 테두리 색입니다.",
		"들여쓰기 가이드의 트리 스트로크 색입니다.",
		"열 사이의 표 테두리 색입니다.",
		"홀수 테이블 행의 배경색입니다.",
		"강조되지 않은 항목의 목록/트리 전경색. ",
		"대신 quickInputList.focusBackground를 사용하세요.",
		"포커스가 있는 항목의 빠른 선택기 전경색입니다.",
		"포커스가 있는 항목의 빠른 선택기 아이콘 전경색입니다.",
		"포커스가 있는 항목의 빠른 선택기 배경색입니다.",
		"메뉴 테두리 색입니다.",
		"메뉴 항목 전경색입니다.",
		"메뉴 항목 배경색입니다.",
		"메뉴의 선택된 메뉴 항목 전경색입니다.",
		"메뉴의 선택된 메뉴 항목 배경색입니다.",
		"메뉴의 선택된 메뉴 항목 테두리 색입니다.",
		"메뉴에서 구분 기호 메뉴 항목의 색입니다.",
		"마우스를 사용하여 작업 위로 마우스를 가져가는 경우 도구 모음 배경",
		"마우스를 사용하여 작업 위로 마우스를 가져가는 경우 도구 모음 윤곽선",
		"작업 위에 마우스를 놓았을 때 도구 모음 배경",
		"코드 조각 탭 정지의 강조 표시 배경색입니다.",
		"코드 조각 탭 정지의 강조 표시 테두리 색입니다.",
		"코드 조각 마지막 탭 정지의 강조 표시 배경색입니다.",
		"코드 조각 마지막 탭 정지의 강조 표시 배경색입니다.",
		"포커스가 있는 이동 경로 항목의 색입니다.",
		"이동 경로 항목의 배경색입니다.",
		"포커스가 있는 이동 경로 항목의 색입니다.",
		"선택한 이동 경로 항목의 색입니다.",
		"이동 경로 항목 선택기의 배경색입니다.",
		"인라인 병합 충돌의 현재 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"인라인 병합 충돌의 현재 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"인라인 병합 충돌의 들어오는 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"인라인 병합 충돌의 들어오는 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"인라인 병합 충돌의 공통 상위 헤더 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"인라인 병합 충돌의 공통 상위 콘텐츠 배경입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"인라인 병합 충돌에서 헤더 및 스플리터의 테두리 색입니다.",
		"인라인 병합 충돌에서 현재 개요 눈금 전경색입니다.",
		"인라인 병합 충돌에서 수신 개요 눈금 전경색입니다.",
		"인라인 병합 충돌에서 공통 과거 개요 눈금 전경색입니다.",
		"일치 항목 찾기의 개요 눈금자 표식 색입니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"선택 항목의 개요 눈금자 표식 색이 강조 표시됩니다. 기본 장식을 숨기지 않도록 색은 불투명하지 않아야 합니다.",
		"강조 표시된 유니코드 문자의 눈금자 표시 색상을 간략히 설명합니다. 기본 장식이 숨겨지지 않도록 색상이 불투명하면 안 됩니다.",
		"일치하는 항목을 찾기 위한 미니맵 표식 색입니다.",
		"편집기 선택을 반복하기 위한 미니맵 표식 색입니다.",
		"편집기 선택 작업을 위한 미니맵 마커 색입니다.",
		"오류에 대한 미니맵 마커 색상입니다.",
		"경고의 미니맵 마커 색상입니다.",
		"미니맵 배경색입니다.",
		"미니맵에서 렌더링된 전경 요소의 불투명도입니다. 예를 들어, \"#000000c0\"은 불투명도 75%로 요소를 렌더링합니다.",
		"강조 표시된 유니코드 문자의 미니맵 표식 색입니다.",
		"미니맵 슬라이더 배경색입니다.",
		"마우스로 가리킬 때 미니맵 슬라이더 배경색입니다.",
		"클릭했을 때 미니맵 슬라이더 배경색입니다.",
		"문제 오류 아이콘에 사용되는 색입니다.",
		"문제 경고 아이콘에 사용되는 색입니다.",
		"문제 정보 아이콘에 사용되는 색입니다.",
		"차트에 사용된 전경색입니다.",
		"차트 가로줄에 사용된 색입니다.",
		"차트 시각화에 사용되는 빨간색입니다.",
		"차트 시각화에 사용되는 파란색입니다.",
		"차트 시각화에 사용되는 노란색입니다.",
		"차트 시각화에 사용되는 주황색입니다.",
		"차트 시각화에 사용되는 녹색입니다.",
		"차트 시각화에 사용되는 자주색입니다.",
	],
	"vs/platform/theme/common/iconRegistry": [
		"사용할 글꼴의 ID입니다. 설정하지 않으면 첫 번째로 정의한 글꼴이 사용됩니다.",
		"아이콘 정의와 연결된 글꼴 문자입니다.",
		"위젯에서 닫기 작업의 아이콘입니다.",
		"이전 편집기 위치로 이동 아이콘입니다.",
		"다음 편집기 위치로 이동 아이콘입니다.",
	],
	"vs/platform/undoRedo/common/undoRedoService": [
		"{0} 파일이 닫히고 디스크에서 수정되었습니다.",
		"{0} 파일은 호환되지 않는 방식으로 수정되었습니다.",
		"모든 파일에서 \'{0}\'을(를) 실행 취소할 수 없습니다. {1}",
		"모든 파일에서 \'{0}\'을(를) 실행 취소할 수 없습니다. {1}",
		"{1}에 변경 내용이 적용되었으므로 모든 파일에서 \'{0}\'을(를) 실행 취소할 수 없습니다.",
		"{1}에서 실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 모든 파일에서 \'{0}\'을(를) 실행 취소할 수 없습니다.",
		"그동안 실행 취소 또는 다시 실행 작업이 발생했기 때문에 모든 파일에서 \'{0}\'을(를) 실행 취소할 수 없습니다.",
		"모든 파일에서 \'{0}\'을(를) 실행 취소하시겠습니까?",
		"{0}개 파일에서 실행 취소",
		"이 파일 실행 취소",
		"취소",
		"실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 \'{0}\'을(를) 실행 취소할 수 없습니다.",
		"\'{0}\'을(를) 실행 취소하시겠습니까?",
		"예",
		"취소",
		"모든 파일에서 \'{0}\'을(를) 다시 실행할 수 없습니다. {1}",
		"모든 파일에서 \'{0}\'을(를) 다시 실행할 수 없습니다. {1}",
		"{1}에 변경 내용이 적용되었으므로 모든 파일에서 \'{0}\'을(를) 다시 실행할 수 없습니다.",
		"{1}에서 실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 모든 파일에서 \'{0}\'을(를) 다시 실행할 수 없습니다.",
		"그동안 실행 취소 또는 다시 실행 작업이 발생했기 때문에 모든 파일에서 \'{0}\'을(를) 다시 실행할 수 없습니다.",
		"실행 취소 또는 다시 실행 작업이 이미 실행 중이므로 \'{0}\'을(를) 다시 실행할 수 없습니다.",
	],
	"vs/platform/workspaces/common/workspaces": [
		"코드 작업 영역",
	]
});