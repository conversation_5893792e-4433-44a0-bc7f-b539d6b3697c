/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.31.1(5a1b4999493d49c857497ad481d73a737439f305)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/editor/editor.main.nls.ru", {
	"vs/base/browser/ui/actionbar/actionViewItems": [
		"{0} ({1})",
	],
	"vs/base/browser/ui/findinput/findInput": [
		"входные данные",
	],
	"vs/base/browser/ui/findinput/findInputCheckboxes": [
		"С учетом регистра",
		"Слово целиком",
		"Использовать регулярное выражение",
	],
	"vs/base/browser/ui/findinput/replaceInput": [
		"входные данные",
		"Сохранить регистр",
	],
	"vs/base/browser/ui/iconLabel/iconLabelHover": [
		"Загрузка…",
	],
	"vs/base/browser/ui/inputbox/inputBox": [
		"Ошибка: {0}",
		"Предупреждение: {0}",
		"Информация: {0}",
		"для журнала",
	],
	"vs/base/browser/ui/keybindingLabel/keybindingLabel": [
		"свободный",
	],
	"vs/base/browser/ui/menu/menu": [
		"{0} ({1})",
	],
	"vs/base/browser/ui/tree/abstractTree": [
		"Сброс",
		"Отключить фильтр по типу",
		"Включить фильтр по типу",
		"Элементы не найдены",
		"Сопоставлено элементов: {0} из {1}",
	],
	"vs/base/common/actions": [
		"(пусто)",
	],
	"vs/base/common/errorMessage": [
		"{0}: {1}",
		"Произошла системная ошибка ({0})",
		"Произошла неизвестная ошибка. Подробные сведения см. в журнале.",
		"Произошла неизвестная ошибка. Подробные сведения см. в журнале.",
		"{0} (всего ошибок: {1})",
		"Произошла неизвестная ошибка. Подробные сведения см. в журнале.",
	],
	"vs/base/common/keybindingLabels": [
		"CTRL",
		"SHIFT",
		"ALT",
		"Windows",
		"CTRL",
		"SHIFT",
		"ALT",
		"Превосходно",
		"CTRL",
		"SHIFT",
		"Параметр",
		"Команда",
		"CTRL",
		"SHIFT",
		"ALT",
		"Windows",
		"CTRL",
		"SHIFT",
		"ALT",
		"Превосходно",
	],
	"vs/base/parts/quickinput/browser/quickInput": [
		"Назад",
		"Нажмите клавишу ВВОД, чтобы подтвердить введенные данные, или ESCAPE для отмены",
		"{0} / {1}",
		"Введите текст, чтобы уменьшить число результатов.",
		"Результаты: {0}",
		"{0} выбрано",
		"OK",
		"Другой",
		"Назад ({0})",
		"Назад",
	],
	"vs/base/parts/quickinput/browser/quickInputList": [
		"Быстрый ввод",
	],
	"vs/editor/browser/controller/coreCommands": [
		"Размещать на конце даже для более длинных строк",
		"Размещать на конце даже для более длинных строк",
		"Дополнительные курсоры удалены.",
	],
	"vs/editor/browser/controller/textAreaHandler": [
		"редактор",
		"Сейчас редактор недоступен. Нажмите {0} для отображения вариантов.",
	],
	"vs/editor/browser/core/keybindingCancellation": [
		"Выполняются ли в редакторе операции, допускающие отмену, например, \"Показать ссылки\"",
	],
	"vs/editor/browser/editorExtensions": [
		"&&Отменить",
		"Отменить",
		"&&Повторить",
		"Вернуть",
		"&&Выделить все",
		"Выбрать все",
	],
	"vs/editor/browser/widget/codeEditorWidget": [
		"Количество курсоров ограничено {0}.",
	],
	"vs/editor/browser/widget/diffEditorWidget": [
		"Оформление строки для вставок в редакторе несовпадений.",
		"Оформление строки для удалений в редакторе несовпадений.",
		"Нельзя сравнить файлы, потому что один из файлов слишком большой.",
	],
	"vs/editor/browser/widget/diffReview": [
		"Значок для кнопки \"Вставить\" в окне проверки несовпадений.",
		"Значок для кнопки \"Удалить\" в окне проверки несовпадений.",
		"Значок для кнопки \"Закрыть\" в окне проверки несовпадений.",
		"Закрыть",
		"нет измененных строк",
		"1 строка изменена",
		"Строк изменено: {0}",
		"Различие {0} из {1}: исходная строка {2}, {3}, измененная строка {4}, {5}",
		"пустой",
		"{0} неизмененная строка {1}",
		"{0} исходная строка {1} измененная строка {2}",
		"+ {0} измененная строка {1}",
		"- {0} исходная строка {1}",
		"Перейти к следующему различию",
		"Перейти к предыдущему различию",
	],
	"vs/editor/browser/widget/inlineDiffMargin": [
		"Копировать удаленные строки",
		"Копировать удаленную строку",
		"Копировать измененные строки",
		"Копировать измененную строку",
		"Копировать удаленную строку ({0})",
		"Копировать измененную строку ({0})",
		"Отменить это изменение",
		"Копировать удаленную строку ({0})",
		"Копировать измененную строку ({0})",
	],
	"vs/editor/common/config/commonEditorConfig": [
		"Редактор",
		"Число пробелов в табуляции. Этот параметр переопределяется на основе содержимого файла, если установлен параметр \"#editor.detectIndentation#\".",
		"Вставлять пробелы при нажатии клавиши TAB. Этот параметр переопределяется на основе содержимого файла, если установлен параметр \"#editor.detectIndentation#\". ",
		"Управляет тем, будут ли параметры \"#editor.tabSize#\" и \"#editor.insertSpaces#\" определяться автоматически при открытии файла на основе содержимого файла.",
		"Удалить автоматически вставляемый конечный пробел.",
		"Специальная обработка для больших файлов с отключением некоторых функций, которые интенсивно используют память.",
		"Определяет, следует ли оценивать завершения на основе слов в документе.",
		"Предложение слов только из активного документа.",
		"Предложение слов из всех открытых документов на одном языке.",
		"Предложение слов из всех открытых документов.",
		"Определяет, из каких документов будут вычисляться завершения на основе слов.",
		"Семантическое выделение включено для всех цветовых тем.",
		"Семантическое выделение отключено для всех цветовых тем.",
		"Семантическое выделение настраивается с помощью параметра \"semanticHighlighting\" текущей цветовой темы.",
		"Определяет показ семантической подсветки для языков, поддерживающих ее.",
		"Оставлять быстрый редактор открытым даже при двойном щелчке по его содержимому и при нажатии ESC.",
		"Строки, длина которых превышает указанное значение, не будут размечены из соображений производительности",
		"Определяет символы скобок, увеличивающие или уменьшающие отступ.",
		"Открывающий символ скобки или строковая последовательность.",
		"Закрывающий символ скобки или строковая последовательность.",
		"Определяет пары скобок, цвет которых зависит от их уровня вложения, если включена опция выделения цветом.",
		"Открывающий символ скобки или строковая последовательность.",
		"Закрывающий символ скобки или строковая последовательность.",
		"Время ожидания в миллисекундах, по истечении которого вычисление несовпадений отменяется. Укажите значение 0, чтобы не использовать время ожидания.",
		"Максимальный размер файла в МБ для вычисления различий. Используйте 0 без ограничений.",
		"Определяет, как редактор несовпадений отображает отличия: рядом или в тексте.",
		"Когда параметр включен, редактор несовпадений игнорирует изменения начального или конечного пробела.",
		"Определяет, должны ли в редакторе отображаться индикаторы +/- для добавленных или удаленных изменений.",
		"Определяет, отображается ли CodeLens в редакторе.",
		"Строки не будут переноситься никогда.",
		"Строки будут переноситься по ширине окна просмотра.",
		"Строки будут переноситься в соответствии с параметром \"#editor.wordWrap#\".",
	],
	"vs/editor/common/config/editorOptions": [
		"Редактор будет определять, подключено ли средство чтения с экрана, с помощью API-интерфейсов платформы.",
		"Редактор будет оптимизирован для использования со средством чтения с экрана в постоянном режиме. Перенос текста будет отключен.",
		"Редактор никогда не будет оптимизироваться для использования со средством чтения с экрана.",
		"Определяет, следует ли запустить редактор в режиме оптимизации для средства чтения с экрана. Если параметр включен, перенос строк будет отключен.",
		"Определяет, вставляется ли пробел при комментировании.",
		"Определяет, должны ли пустые строки игнорироваться с помощью действий переключения, добавления или удаления для комментариев к строкам.",
		"Управляет тем, копируется ли текущая строка при копировании без выделения.",
		"Определяет, должен ли курсор перемещаться для поиска совпадений при вводе.",
		"Никогда не вставлять начальные значения в строку поиска из выделенного фрагмента редактора.",
		"Всегда вставлять начальные значения в строку поиска из выделенного фрагмента редактора, включая слова в позиции курсора.",
		"Вставлять начальные значения в строку поиска только из выделенного фрагмента редактора.",
		"Определяет, можно ли передать строку поиска в мини-приложение поиска из текста, выделенного в редакторе.",
		"Никогда не включать функцию «Найти в выделении» автоматически (по умолчанию).",
		"Всегда включать функцию «Найти в выделении» автоматически.",
		"Автоматическое включение функции «Найти в выделении» при выборе нескольких строк содержимого.",
		"Управляет условием автоматического включения функции «Найти в выделении».",
		"Определяет, должно ли мини-приложение поиска считывать или изменять общий буфер обмена поиска в macOS.",
		"Определяет, должно ли мини-приложение поиска добавлять дополнительные строки в начале окна редактора. Если задано значение true, вы можете прокрутить первую строку при отображаемом мини-приложении поиска.",
		"Определяет, будет ли поиск автоматически перезапускаться с начала (или с конца), если не найдено никаких других соответствий.",
		"Включает или отключает лигатуры шрифтов (характеристики шрифта \"calt\" и \"liga\"). Измените этот параметр на строку для детального управления свойством CSS \"font-feature-settings\".",
		"Явное свойство CSS \"font-feature-settings\". Если необходимо только включить или отключить лигатуры, вместо него можно передать логическое значение.",
		"Настраивает лигатуры или характеристики шрифта. Можно указать логическое значение, чтобы включить или отключить лигатуры, или строку для значения свойства CSS \"font-feature-settings\".",
		"Определяет размер шрифта в пикселях.",
		"Допускаются только ключевые слова \"normal\" или \"bold\" и числа в диапазоне от 1 до 1000.",
		"Управляет насыщенностью шрифта. Допустимые значения: ключевые слова \"normal\" или \"bold\", а также числа в диапазоне от 1 до 1000.",
		"Показать предварительные результаты (по умолчанию)",
		"Перейти к основному результату и показать быстрый редактор",
		"Перейдите к основному результату и включите быструю навигацию для остальных",
		"Этот параметр устарел. Используйте вместо него отдельные параметры, например, \'editor.editor.gotoLocation.multipleDefinitions\' или \'editor.editor.gotoLocation.multipleImplementations\'.",
		"Управляет поведением команды \"Перейти к определению\" при наличии нескольких целевых расположений.",
		"Управляет поведением команды \"Перейти к определению типа\" при наличии нескольких целевых расположений.",
		"Управляет поведением команды \"Перейти к объявлению\" при наличии нескольких целевых расположений.",
		"Управляет поведением команды \"Перейти к реализациям\" при наличии нескольких целевых расположений.",
		"Управляет поведением команды \"Перейти к ссылкам\" при наличии нескольких целевых расположений.",
		"Идентификатор альтернативной команды, выполняемой в том случае, когда результатом операции \"Перейти к определению\" является текущее расположение.",
		"Идентификатор альтернативной команды, которая выполняется в том случае, если результатом операции \"Перейти к определению типа\" является текущее расположение.",
		"Идентификатор альтернативный команды, выполняемой в том случае, когда результатом операции \"Перейти к объявлению\" является текущее расположение.",
		"Идентификатор альтернативный команды, выполняемой, когда результатом команды \"Перейти к реализации\" является текущее расположение.",
		"Идентификатор альтернативной команды, выполняемой в том случае, когда результатом выполнения операции \"Перейти к ссылке\" является текущее расположение.",
		"Управляет тем, отображается ли наведение.",
		"Определяет время задержки в миллисекундах перед отображением наведения.",
		"Управляет тем, должно ли наведение оставаться видимым при наведении на него курсора мыши.",
		"Предпочитать отображать наведение над строкой, если есть место.",
		"Включает индикатор действия кода в редакторе.",
		"Включает встроенные указания в редакторе.",
		"Управляет размером шрифта вложенных указаний в редакторе. Значение по умолчанию 90% \"#editor.fontSize#\" используется, если заданное значение меньше \"5\" или больше размера шрифта редактора.",
		"Определяет семейство шрифтов для указаний-вкладок в редакторе. Если никакое значение не задано, используется `#editor.fontFamily#`.",
		"Определяет высоту строки. \r\n– Используйте 0, чтобы автоматически вычислить высоту строки на основе размера шрифта.\r\n– Значения от 0 до 8 будут использоваться в качестве множителя для размера шрифта.\r\n– Значения больше или равные 8 будут использоваться в качестве действующих значений.",
		"Определяет, отображается ли мини-карта.",
		"Мини-карта имеет такой же размер, что и содержимое редактора (возможна прокрутка).",
		"Мини-карта будет растягиваться или сжиматься по мере необходимости, чтобы заполнить редактор по высоте (без прокрутки).",
		"Миникарта будет уменьшаться по мере необходимости, чтобы никогда не быть больше, чем редактор (без прокрутки).",
		"Управляет размером миникарты.",
		"Определяет, с какой стороны будет отображаться мини-карта.",
		"Определяет, когда отображается ползунок мини-карты.",
		"Масштаб содержимого, нарисованного на мини-карте: 1, 2 или 3.",
		"Отображает фактические символы в строке вместо цветных блоков.",
		"Ограничивает ширину мини-карты, чтобы количество отображаемых столбцов не превышало определенное количество.",
		"Задает пространство между верхним краем редактора и первой строкой.",
		"Задает пространство между нижним краем редактора и последней строкой.",
		"Включает всплывающее окно с документацией по параметру и сведениями о типе, которое отображается во время набора.",
		"Определяет, меню подсказок остается открытым или закроется при достижении конца списка.",
		"Разрешение кратких предложений в строках.",
		"Разрешение кратких предложений в комментариях.",
		"Разрешение кратких предложений вне строк и комментариев.",
		"Определяет, должны ли при вводе текста автоматически отображаться предложения.",
		"Номера строк не отображаются.",
		"Отображаются абсолютные номера строк.",
		"Отображаемые номера строк вычисляются как расстояние в строках до положения курсора.",
		"Номера строк отображаются каждые 10 строк.",
		"Управляет отображением номеров строк.",
		"Число моноширинных символов, при котором будет отрисовываться линейка этого редактора.",
		"Цвет линейки этого редактора.",
		"Отображать вертикальные линейки после определенного числа моноширинных символов. Для отображения нескольких линеек укажите несколько значений. Если не указано ни одного значения, вертикальные линейки отображаться не будут.",
		"Вертикальная полоса прокрутки будет видна только при необходимости.",
		"Вертикальная полоса прокрутки всегда будет видна.",
		"Вертикальная полоса прокрутки всегда будет скрыта.",
		"Управляет видимостью вертикальной полосы прокрутки.",
		"Горизонтальная полоса прокрутки будет видна только при необходимости.",
		"Горизонтальная полоса прокрутки всегда будет видна.",
		"Горизонтальная полоса прокрутки всегда будет скрыта.",
		"Управляет видимостью горизонтальной полосы прокрутки.",
		"Ширина вертикальной полосы прокрутки.",
		"Высота горизонтальной полосы прокрутки.",
		"Управляет прокруткой при нажатии страницы или переходом к позиции щелчка.",
		"Управляет выделением всех нестандартных символов ASCII. Базовыми ASCII считаются только символы между U+0020 и U+007E, табуляция, перевод строки и возврат каретки.",
		"Определяет, выделяются ли символы, которые просто резервируют пространство или вообще не имеют ширины.",
		"Управляет выделением символов, которые можно спутать с основными символами ASCII, кроме тех, которые являются общими в текущем языковом стандарте пользователя.",
		"Определяет, должны ли символы в комментариях также выделяться в Юникоде.",
		"Определяет разрешенные символы, которые не выделяются.",
		"Определяет, следует ли автоматически показывать встроенные предложения в редакторе.",
		"Активирует раскраску парных скобок. Переопределить цвета выделения скобок можно с помощью \"workbench.colorCustomizations\".",
		"Включение направляющих для пар скобок.",
		"Включение направляющих для пар скобок только для активной пары скобок.",
		"Отключение направляющих для пар скобок.",
		"Определяет, включены ли направляющие пар скобок.",
		"Включение горизонтальных направляющих в дополнение к вертикальным направляющим для пар скобок.",
		"Включение горизонтальных направляющих только для активной пары скобок.",
		"Отключение горизонтальных направляющих для пар скобок.",
		"Определяет, включены ли горизонтальные направляющие для скобок.",
		"Определяет, включены ли направляющие пар скобок.",
		"Определяет, должны ли в редакторе отображаться направляющие отступа.",
		"Управляет тем, должна ли выделяться активная направляющая отступа в редакторе.",
		"Вставить предложение без перезаписи текста справа от курсора.",
		"Вставить предложение и перезаписать текст справа от курсора.",
		"Определяет, будут ли перезаписываться слова при принятии вариантов завершения. Обратите внимание, что это зависит от расширений, использующих эту функцию.",
		"Управляет тем, допускаются ли небольшие опечатки в предложениях фильтрации и сортировки.",
		"Определяет, следует ли учитывать при сортировке слова, расположенные рядом с курсором.",
		"Определяет, используются ли сохраненные варианты выбора предложений совместно несколькими рабочими областями и окнами (требуется \"#editor.suggestSelection#\").",
		"Определяет, запрещает ли активный фрагмент кода экспресс-предложения.",
		"Указывает, нужно ли отображать значки в предложениях.",
		"Определяет видимость строки состояния в нижней части виджета предложений.",
		"Определяет, следует ли просматривать результат предложения в редакторе.",
		"Определяет, отображаются ли сведения о предложении встроенным образом вместе с меткой или только в мини-приложении сведений.",
		"Этот параметр является нерекомендуемым. Теперь размер мини-приложения предложений можно изменить.",
		"Этот параметр устарел. Используйте вместо него отдельные параметры, например, \'editor.suggest.showKeywords\' или \'editor.suggest.showSnippets\'.",
		"Когда параметр включен, в IntelliSense отображаются предложения \"method\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"function\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"constructor\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"deprecated\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"field\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"variable\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"class\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"struct\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"interface\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"module\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"property\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"event\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"operator\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"unit\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"value\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"constant\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"enum\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"enumMember\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"keyword\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"text\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"color\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"file\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"reference\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"customcolor\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"folder\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"typeParameter\".",
		"Когда параметр включен, в IntelliSense отображаются предложения \"snippet\".",
		"Во включенном состоянии IntelliSense показывает предложения типа \"пользователи\".",
		"Во включенном состоянии IntelliSense отображает предложения типа \"проблемы\".",
		"Должны ли всегда быть выбраны начальный и конечный пробелы.",
		"Определяет, будут ли предложения приниматься при вводе символов фиксации. Например, в JavaScript точка с запятой (\";\") может быть символом фиксации, при вводе которого предложение принимается.",
		"Принимать предложение при нажатии клавиши ВВОД только в том случае, если оно изменяет текст.",
		"Определяет, будут ли предложения приниматься клавишей ВВОД в дополнение к клавише TAB. Это помогает избежать неоднозначности между вставкой новых строк и принятием предложений.",
		"Управляет числом строк в редакторе, которые могут быть прочитаны средством чтения с экрана за один раз. При обнаружении средства чтения с экрана автоматически устанавливается значение по умолчанию 500. Внимание! При указании числа строк, превышающего значение по умолчанию, возможно снижение производительности.",
		"Содержимое редактора",
		"Использовать конфигурации языка для автоматического закрытия скобок.",
		"Автоматически закрывать скобки только в том случае, если курсор находится слева от пробела.",
		"Определяет, должен ли редактор автоматически добавлять закрывающую скобку при вводе пользователем открывающей скобки.",
		"Удалять соседние закрывающие кавычки и квадратные скобки только в том случае, если они были вставлены автоматически.",
		"Определяет, должен ли редактор удалять соседние закрывающие кавычки или квадратные скобки при удалении.",
		"Заменять закрывающие кавычки и скобки при вводе только в том случае, если кавычки или скобки были вставлены автоматически.",
		"Определяет, должны ли в редакторе заменяться закрывающие кавычки или скобки при вводе.",
		"Использовать конфигурации языка для автоматического закрытия кавычек.",
		"Автоматически закрывать кавычки только в том случае, если курсор находится слева от пробела.",
		"Определяет, должен ли редактор автоматически закрывать кавычки, если пользователь добавил открывающую кавычку.",
		"Редактор не будет вставлять отступы автоматически.",
		"Редактор будет сохранять отступ текущей строки.",
		"Редактор будет сохранять отступы текущей строки и учитывать скобки в соответствии с синтаксисом языка.",
		"Редактор будет сохранять отступ текущей строки, учитывать определенные языком скобки и вызывать специальные правила onEnterRules, определяемые языками.",
		"Редактор будет сохранять отступ текущей строки, учитывать определенные языком скобки, вызывать специальные правила onEnterRules, определяемые языками и учитывать правила отступа indentationRules, определяемые языками.",
		"Определяет, должен ли редактор автоматически изменять отступы, когда пользователи вводят, вставляют или перемещают текст или изменяют отступы строк.",
		"Использовать конфигурации языка для автоматического обрамления выделений.",
		"Обрамлять с помощью кавычек, а не скобок.",
		"Обрамлять с помощью скобок, а не кавычек.",
		"Определяет, должен ли редактор автоматически обрамлять выделения при вводе кавычек или квадратных скобок.",
		"Эмулировать поведение выделения для символов табуляции при использовании пробелов для отступа. Выделение будет применено к позициям табуляции.",
		"Определяет, отображается ли CodeLens в редакторе.",
		"Управляет семейством шрифтов для CodeLens.",
		"Определяет размер шрифта в пикселях для CodeLens. Если задано значение \"0\", то используется 90 % от размера \"#editor.fontSize#\".",
		"Определяет, должны ли в редакторе отображаться внутренние декораторы цвета и средство выбора цвета.",
		"Включение того, что выбор с помощью клавиатуры и мыши приводит к выбору столбца.",
		"Определяет, будет ли текст скопирован в буфер обмена с подсветкой синтаксиса.",
		"Управляет стилем анимации курсора.",
		"Управляет тем, следует ли включить плавную анимацию курсора.",
		"Управляет стилем курсора.",
		"Определяет минимальное число видимых начальных и конечных линий, окружающих курсор. Этот параметр имеет название \"scrollOff\" или \"scrollOffset\" в некоторых других редакторах.",
		"\"cursorSurroundingLines\" применяется только при запуске с помощью клавиатуры или API.",
		"\"cursorSurroundingLines\" принудительно применяется во всех случаях.",
		"Определяет, когда необходимо применять \"cursorSurroundingLines\".",
		"Управляет шириной курсора, когда для параметра \"#editor.cursorStyle#\" установлено значение \'line\'",
		"Определяет, следует ли редактору разрешить перемещение выделенных элементов с помощью перетаскивания.",
		"Коэффициент увеличения скорости прокрутки при нажатии клавиши ALT.",
		"Определяет, включено ли свертывание кода в редакторе.",
		"Используйте стратегию свертывания для конкретного языка, если она доступна, в противном случае используйте стратегию на основе отступов.",
		"Используйте стратегию свертывания на основе отступов.",
		"Управляет стратегией для вычисления свертываемых диапазонов.",
		"Определяет, должен ли редактор выделять сложенные диапазоны.",
		"Определяет, будет ли редактор автоматически сворачивать диапазоны импорта.",
		"Определяет, будет ли щелчок пустого содержимого после свернутой строки развертывать ее.",
		"Определяет семейство шрифтов.",
		"Определяет, будет ли редактор автоматически форматировать вставленное содержимое. Модуль форматирования должен быть доступен и иметь возможность форматировать диапазон в документе.",
		"Управляет параметром, определяющим, должен ли редактор автоматически форматировать строку после ввода.",
		"Управляет отображением вертикальных полей глифа в редакторе. Поля глифа в основном используются для отладки.",
		"Управляет скрытием курсора в обзорной линейке.",
		"Управляет интервалом между буквами в пикселях.",
		"Определяет, включена ли поддержка связанного редактирования в редакторе. В зависимости от языка, связанные символы, например, теги HTML, обновляются при редактировании.",
		"Определяет, должен ли редактор определять ссылки и делать их доступными для щелчка.",
		"Выделять соответствующие скобки.",
		"Множитель, используемый для параметров deltaX и deltaY событий прокрутки колесика мыши.",
		"Изменение размера шрифта в редакторе при нажатой клавише CTRL и движении колесика мыши.",
		"Объединить несколько курсоров, когда они перекрываются.",
		"Соответствует клавише CTRL в Windows и Linux и клавише COMMAND в macOS.",
		"Соответствует клавише ALT в Windows и Linux и клавише OPTION в macOS.",
		"Модификатор, который будет использоваться для добавления нескольких курсоров с помощью мыши. Жесты мыши \"Перейти к определению\" и \"Открыть ссылку\" будут изменены так, чтобы они не конфликтовали модификатором нескольких курсоров. [Подробнее](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).",
		"Каждый курсор вставляет одну строку текста.",
		"Каждый курсор вставляет полный текст.",
		"Управляет вставкой, когда число вставляемых строк соответствует числу курсоров.",
		"Определяет, должен ли редактор выделять экземпляры семантических символов.",
		"Определяет, должна ли отображаться граница на обзорной линейке.",
		"Фокусировка на дереве при открытии обзора",
		"Фокусировка на редакторе при открытии обзора",
		"Определяет, следует ли переключить фокус на встроенный редактор или дерево в виджете обзора.",
		"Определяет, всегда ли жест мышью для перехода к определению открывает мини-приложение быстрого редактирования.",
		"Управляет длительностью задержки (в мс) перед отображением кратких предложений.",
		"Определяет, выполняет ли редактор автоматическое переименование по типу.",
		"Не рекомендуется; используйте вместо этого параметр \"editor.linkedEditing\".",
		"Определяет, должны ли в редакторе отображаться управляющие символы.",
		"Отображение номера последней строки, когда файл заканчивается новой строкой.",
		"Выделяет поле и текущую строку.",
		"Определяет, должен ли редактор выделять текущую строку.",
		"Определяет, должен ли редактор отрисовывать выделение текущей строки, только когда он находится в фокусе.",
		"Отрисовка пробелов, кроме одиночных пробелов между словами.",
		"Отображать пробелы только в выделенном тексте.",
		"Отображать только конечные пробелы.",
		"Определяет, должны ли в редакторе отображаться пробелы.",
		"Управляет тем, необходимо ли отображать скругленные углы для выделения.",
		"Управляет количеством дополнительных символов, на которое содержимое редактора будет прокручиваться по горизонтали.",
		"Определяет, будет ли содержимое редактора прокручиваться за последнюю строку.",
		"Прокрутка только вдоль основной оси при прокрутке по вертикали и горизонтали одновременно. Предотвращает смещение по горизонтали при прокрутке по вертикали на трекпаде.",
		"Контролирует, следует ли поддерживать первичный буфер обмена Linux.",
		"Определяет, должен ли редактор выделять совпадения, аналогичные выбранному фрагменту.",
		"Всегда показывать свертываемые элементы управления.",
		"Показывать только элементы управления свертывания, когда указатель мыши находится над переплетом.",
		"Определяет, когда элементы управления свертывания отображаются на переплете.",
		"Управляет скрытием неиспользуемого кода.",
		"Управляет перечеркиванием устаревших переменных.",
		"Отображать предложения фрагментов поверх других предложений.",
		"Отображать предложения фрагментов под другими предложениями.",
		"Отображать предложения фрагментов рядом с другими предложениями.",
		"Не отображать предложения фрагментов.",
		"Управляет отображением фрагментов вместе с другими предложениями и их сортировкой.",
		"Определяет, будет ли использоваться анимация при прокрутке содержимого редактора",
		"Размер шрифта мини-приложения с предложениями. Если установить значение \"0\", будет использовано значение \"#editor.fontSize#\".",
		"Высота строки мини-приложения с предложениями. Если установить значение \"0\", будет использовано значение \"#editor.lineHeight#\". Минимальное значение — 8.",
		"Определяет, должны ли при вводе триггерных символов автоматически отображаться предложения.",
		"Всегда выбирать первое предложение.",
		"Выбор недавних предложений, если только дальнейший ввод не приводит к использованию одного из них, например \"console.| -> console.log\", так как \"log\" недавно использовался для завершения.",
		"Выбор предложений с учетом предыдущих префиксов, использованных для завершения этих предложений, например \"co -> console\" и \"con -> const\".",
		"Управляет предварительным выбором предложений при отображении списка предложений.",
		"При использовании дополнения по TAB будет добавляться наилучшее предложение при нажатии клавиши TAB.",
		"Отключить дополнение по TAB.",
		"Вставка дополнений по TAB при совпадении их префиксов. Функция работает оптимально, если параметр \"quickSuggestions\" отключен.",
		"Включает дополнения по TAB.",
		"Необычные символы завершения строки автоматически удаляются.",
		"Необычные символы завершения строки игнорируются.",
		"Для необычных символов завершения строки запрашивается удаление.",
		"Удалите необычные символы завершения строки, которые могут вызвать проблемы.",
		"Вставка и удаление пробелов после позиции табуляции",
		"Символы, которые будут использоваться как разделители слов при выполнении навигации или других операций, связанных со словами.",
		"Строки не будут переноситься никогда.",
		"Строки будут переноситься по ширине окна просмотра.",
		"Строки будут переноситься по \"#editor.wordWrapColumn#\".",
		"Строки будут перенесены по минимальному значению из двух: ширина окна просмотра и \"#editor.wordWrapColumn#\".",
		"Управляет тем, как следует переносить строки.",
		"Определяет столбец переноса редактора, если значение \"#editor.wordWrap#\" — \"wordWrapColumn\" или \"bounded\".",
		"Без отступа. Перенос строк начинается со столбца 1.",
		"Перенесенные строки получат тот же отступ, что и родительская строка.",
		"Перенесенные строки получат отступ, увеличенный на единицу по сравнению с родительской строкой. ",
		"Перенесенные строки получат отступ, увеличенный на два по сравнению с родительской строкой.",
		"Управляет отступом строк с переносом по словам.",
		"Предполагает, что все символы имеют одинаковую ширину. Это быстрый алгоритм, который работает правильно для моноширинных шрифтов и некоторых скриптов (например, латинских символов), где глифы имеют одинаковую ширину.",
		"Делегирует вычисление точек переноса браузеру. Это медленный алгоритм, который может привести к зависаниям при обработке больших файлов, но работает правильно во всех случаях.",
		"Управляет алгоритмом, вычисляющим точки переноса.",
	],
	"vs/editor/common/editorContextKeys": [
		"Находится ли фокус на тексте в редакторе (курсор мигает)",
		"Находится ли фокус на редакторе или на мини-приложении редактора (например, фокус находится на мини-приложении поиска)",
		"Находится ли фокус на редакторе или на поле ввода форматированного текста (курсор мигает)",
		"Доступен ли редактор только для чтения",
		"Является ли контекст редактором несовпадений",
		"Включен ли параметр \"editor.columnSelection\"",
		"Есть ли в редакторе выбранный текст",
		"Есть ли в редакторе множественный выбор",
		"Перемещается ли фокус с редактора при нажатии клавиши TAB",
		"Является ли наведение в редакторе видимым",
		"Является ли редактор частью большего редактора (например, записных книжек)",
		"Идентификатор языка редактора",
		"Есть ли в редакторе поставщик элементов завершения",
		"Есть ли в редакторе поставщик действий с кодом",
		"Есть ли в редакторе поставщик CodeLens",
		"Есть ли в редакторе поставщик определений",
		"Есть ли в редакторе поставщик объявлений",
		"Есть ли в редакторе поставщик реализации",
		"Есть ли в редакторе поставщик определений типов",
		"Есть ли в редакторе поставщик наведения",
		"Есть ли в редакторе поставщик выделения документов",
		"Есть ли в редакторе поставщик символов документа",
		"Есть ли в редакторе поставщик ссылок",
		"Есть ли в редакторе поставщик переименования",
		"Есть ли в редакторе поставщик справки по сигнатурам",
		"Есть ли в редакторе поставщик встроенных подсказок",
		"Есть ли в редакторе поставщик форматирования документов",
		"Есть ли в редакторе поставщик форматирования для выделения документов",
		"Есть ли в редакторе несколько поставщиков форматирования документов",
		"Есть ли в редакторе несколько поставщиков форматирования для выделения документов",
	],
	"vs/editor/common/model/editStack": [
		"Ввод",
	],
	"vs/editor/common/modes/modesRegistry": [
		"Простой текст",
	],
	"vs/editor/common/standaloneStrings": [
		"Ничего не выбрано",
		"Строка {0}, столбец {1} (выбрано: {2})",
		"Строка {0}, столбец {1}",
		"Выделений: {0} (выделено символов: {1})",
		"Выделений: {0}",
		"Теперь для параметра \"accessibilitySupport\" устанавливается значение \"вкл\".",
		"Открывается страница документации о специальных возможностях редактора.",
		"в панели только для чтения редактора несовпадений.",
		"на панели редактора несовпадений.",
		" в редакторе кода только для чтения",
		" в редакторе кода",
		"Чтобы оптимизировать редактор для использования со средством чтения с экрана, нажмите COMMAND+E.",
		"Чтобы оптимизировать редактор для использования со средством чтения с экрана, нажмите CTRL+E.",
		"Редактор настроен для оптимальной работы со средством чтения с экрана.",
		"Редактор настроен без оптимизации для использования средства чтения с экрана, что не подходит в данной ситуации.",
		"При нажатии клавиши TAB в текущем редакторе фокус ввода переместится на следующий элемент, способный его принять. Чтобы изменить это поведение, нажмите клавишу {0}.",
		"При нажатии клавиши TAB в текущем редакторе фокус ввода переместится на следующий элемент, способный его принять. Команду {0} сейчас невозможно выполнить с помощью настраиваемого сочетания клавиш.",
		"При нажатии клавиши TAB в текущем редакторе будет вставлен символ табуляции. Чтобы изменить это поведение, нажмите клавишу {0}.",
		"При нажатии клавиши TAB в текущем редакторе будет вставлен символ табуляции. Команду {0} сейчас невозможно выполнить с помощью настраиваемого сочетания клавиш.",
		"Нажмите COMMAND+H, чтобы открыть окно браузера с дополнительной информацией о специальных возможностях редактора.",
		"Нажмите CTRL+H, чтобы открыть окно браузера с дополнительной информацией о специальных возможностях редактора.",
		"Вы можете закрыть эту подсказку и вернуться в редактор, нажав клавиши ESCAPE или SHIFT+ESCAPE.",
		"Показать справку по специальным возможностям",
		"Разработчик: проверить токены",
		"Перейти к строке/столбцу...",
		"Показать всех поставщиков быстрого доступа",
		"Палитра команд",
		"Показать и выполнить команды",
		"Перейти к символу...",
		"Перейти к символу по категориям...",
		"Содержимое редактора",
		"Нажмите ALT+F1 для доступа к параметрам специальных возможностей.",
		"Переключить высококонтрастную тему",
		"Внесено изменений в файлах ({1}): {0}.",
	],
	"vs/editor/common/view/editorColorRegistry": [
		"Цвет фона для выделения строки в позиции курсора.",
		"Цвет фона границ вокруг строки в позиции курсора.",
		"Цвет фона для выделенных диапазонов, например при использовании функций Quick Open или поиска. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет фона обводки выделения.",
		"Цвет фона выделенного символа, например, в функциях \"Перейти к определению\" или \"Перейти к следующему/предыдущему символу\". Цвет должен быть прозрачным, чтобы не скрывать оформление текста под ним.",
		"Цвет фона для границы вокруг выделенных символов.",
		"Цвет курсора редактора.",
		"Цвет фона курсора редактора. Позволяет настраивать цвет символа, перекрываемого прямоугольным курсором.",
		"Цвет пробелов в редакторе.",
		"Цвет направляющих для отступов редактора.",
		"Цвет активных направляющих для отступов редактора.",
		"Цвет номеров строк редактора.",
		"Цвет номера активной строки редактора",
		"Параметр \'Id\' является устаревшим. Используйте вместо него параметр \'editorLineNumber.activeForeground\'.",
		"Цвет номера активной строки редактора",
		"Цвет линейки редактора.",
		"Цвет переднего плана элемента CodeLens в редакторе",
		"Цвет фона парных скобок",
		"Цвет прямоугольников парных скобок",
		"Цвет границы для линейки в окне просмотра.",
		"Цвет фона обзорной линейки редактора. Используется, только если мини-карта включена и размещена в правой части редактора.",
		"Цвет фона поля в редакторе. В поле размещаются отступы глифов и номера строк.",
		"Цвет границы для ненужного (неиспользуемого) исходного кода в редакторе.",
		"Непрозрачность ненужного (неиспользуемого) исходного кода в редакторе. Например, \"#000000c0\" отображает код с непрозрачностью 75 %. В высококонтрастных темах для выделения ненужного кода вместо затенения используйте цвет темы \"editorUnnecessaryCode.border\".",
		"Цвет границы для едва различимого текста в редакторе.",
		"Цвет переднего плана для едва различимого текста в редакторе.",
		"Цвет фона для едва различимого текста в редакторе.",
		"Цвет маркера обзорной линейки для выделения диапазонов. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет метки линейки в окне просмотра для ошибок.",
		"Цвет метки линейки в окне просмотра для предупреждений.",
		"Цвет метки линейки в окне просмотра для информационных сообщений.",
		"Цвет переднего плана для скобок (1). Требуется включить раскраску парных скобок.",
		"Цвет переднего плана для скобок (2). Требуется включить раскраску парных скобок.",
		"Цвет переднего плана для скобок (3). Требуется включить раскраску парных скобок.",
		"Цвет переднего плана для скобок (4). Требуется включить раскраску парных скобок.",
		"Цвет переднего плана для скобок (5). Требуется включить раскраску парных скобок.",
		"Цвет переднего плана для скобок (6). Требуется включить раскраску парных скобок.",
		"Цвет переднего плана непредвиденных скобок.",
		"Цвет фона неактивных направляющих пар скобок (1). Требуется включить направляющие пар скобок.",
		"Цвет фона неактивных направляющих пар скобок (2). Требуется включить направляющие пар скобок.",
		"Цвет фона неактивных направляющих пар скобок (3). Требуется включить направляющие пар скобок.",
		"Цвет фона неактивных направляющих пар скобок (4). Требуется включить направляющие пар скобок.",
		"Цвет фона неактивных направляющих пар скобок (5). Требуется включить направляющие пар скобок.",
		"Цвет фона неактивных направляющих пар скобок (6). Требуется включить направляющие пар скобок.",
		"Цвет фона активных направляющих пар скобок (1). Требуется включить направляющие пар скобок.",
		"Цвет фона активных направляющих пар скобок (2). Требуется включить направляющие пар скобок.",
		"Цвет фона активных направляющих пар скобок (3). Требуется включить направляющие пар скобок.",
		"Цвет фона активных направляющих пар скобок (4). Требуется включить направляющие пар скобок.",
		"Цвет фона активных направляющих пар скобок (5). Требуется включить направляющие пар скобок.",
		"Цвет фона активных направляющих пар скобок (6). Требуется включить направляющие пар скобок.",
		"Цвет границы, используемый для выделения символов Юникода.",
	],
	"vs/editor/contrib/anchorSelect/anchorSelect": [
		"Начальная точка выделения",
		"Начальная точка установлена в {0}:{1}",
		"Установить начальную точку выделения",
		"Перейти к начальной точке выделения",
		"Выделить текст от начальной точки выделения до курсора",
		"Отменить начальную точку выделения",
	],
	"vs/editor/contrib/bracketMatching/bracketMatching": [
		"Цвет метки линейки в окне просмотра для пар скобок.",
		"Перейти к скобке",
		"Выбрать скобку",
		"Перейти к &&скобке",
	],
	"vs/editor/contrib/caretOperations/caretOperations": [
		"Переместить выделенный текст влево",
		"Переместить выделенный текст вправо",
	],
	"vs/editor/contrib/caretOperations/transpose": [
		"Транспортировать буквы",
	],
	"vs/editor/contrib/clipboard/clipboard": [
		"&&Вырезать",
		"Вырезать",
		"Вырезать",
		"Вырезать",
		"&&Копировать",
		"Копирование",
		"Копирование",
		"Копирование",
		"Копировать как",
		"Копировать как",
		"&&Вставить",
		"Вставить",
		"Вставить",
		"Вставить",
		"Копировать с выделением синтаксиса",
	],
	"vs/editor/contrib/codeAction/codeActionCommands": [
		"Тип запускаемого действия кода.",
		"Определяет, когда применяются возвращенные действия.",
		"Всегда применять первое возвращенное действие кода.",
		"Применить первое действие возвращенного кода, если оно является единственным.",
		"Не применять действия возвращенного кода.",
		"Определяет, следует ли возвращать только предпочтительные действия кода.",
		"При применении действия кода произошла неизвестная ошибка",
		"Быстрое исправление...",
		"Доступные действия кода отсутствуют",
		"Нет доступных предпочтительных действий кода для \"{0}\".",
		"Действия кода для \"{0}\" недоступны",
		"Нет доступных предпочтительных действий кода",
		"Доступные действия кода отсутствуют",
		"Рефакторинг...",
		"Нет доступных предпочтительных рефакторингов для \"{0}\"",
		"Нет доступного рефакторинга для \"{0}\"",
		"Нет доступных предпочтительных рефакторингов",
		"Доступные операции рефакторинга отсутствуют",
		"Действие с исходным кодом...",
		"Нет доступных предпочтительных действий источника для \'{0}\'",
		"Нет доступных исходных действий для \"{0}\"",
		"Предпочтительные действия источника недоступны",
		"Доступные исходные действия отсутствуют",
		"Организация импортов",
		"Действие для упорядочения импортов отсутствует",
		"Исправить все",
		"Нет доступного действия по общему исправлению",
		"Автоисправление...",
		"Нет доступных автоисправлений",
	],
	"vs/editor/contrib/codeAction/lightBulbWidget": [
		"Показать действия кода. Доступно предпочтительное быстрое исправление ({0})",
		"Показать действия кода ({0})",
		"Показать действия кода",
	],
	"vs/editor/contrib/codelens/codelensController": [
		"Показать команды CodeLens для текущей строки",
	],
	"vs/editor/contrib/colorPicker/colorPickerWidget": [
		"Щелкните, чтобы переключить параметры цвета (RGB/HSL/HEX)",
	],
	"vs/editor/contrib/comment/comment": [
		"Закомментировать или раскомментировать строку",
		"Переключить комментарий &&строки",
		"Закомментировать строку",
		"Раскомментировать строку",
		"Закомментировать или раскомментировать блок",
		"Переключить комментарий &&блока",
	],
	"vs/editor/contrib/contextmenu/contextmenu": [
		"Показать контекстное меню редактора",
	],
	"vs/editor/contrib/cursorUndo/cursorUndo": [
		"Отмена действия курсора",
		"Повтор действия курсора",
	],
	"vs/editor/contrib/find/findController": [
		"Найти",
		"&&Найти",
		"Переопределяет флаг \"Использовать регулярное выражение\".\r\nЭтот флаг не будет сохранен на будущее.\r\n0: бездействие\r\n1: true\r\n2: false",
		"Переопределяет флаг \"Слово целиком\".\r\nЭтот флаг не будет сохранен на будущее.\r\n0: бездействие\r\n1: true\r\n2: false",
		"Переопределяет флаг \"Учитывать регистр\".\r\nЭтот флаг не будет сохранен на будущее.\r\n0: бездействие\r\n1: true\r\n2: false",
		"Переопределяет флаг \"Сохранить регистр\".\r\nЭтот флаг не будет сохранен на будущее.\r\n0: бездействие\r\n1: true\r\n2: false",
		"Найти с аргументами",
		"Найти в выбранном",
		"Найти далее",
		"Найти ранее",
		"Найти следующее выделение",
		"Найти предыдущее выделение",
		"Заменить",
		"&&Заменить",
	],
	"vs/editor/contrib/find/findWidget": [
		"Значок для кнопки \"Найти в выбранном\" в мини-приложении поиска в редакторе.",
		"Значок, указывающий, что мини-приложение поиска в редакторе свернуто.",
		"Значок, указывающий, что мини-приложение поиска в редакторе развернуто.",
		"Значок для кнопки \"Заменить\" в мини-приложении поиска в редакторе.",
		"Значок для кнопки \"Заменить все\" в мини-приложении поиска в редакторе.",
		"Значок для кнопки \"Найти ранее\" в мини-приложении поиска в редакторе.",
		"Значок для кнопки \"Найти далее\" в мини-приложении поиска в редакторе.",
		"Найти",
		"Найти",
		"Предыдущее совпадение",
		"Следующее совпадение",
		"Найти в выделении",
		"Закрыть",
		"Заменить",
		"Заменить",
		"Заменить",
		"Заменить все",
		"Переключение замены",
		"Отображаются только первые {0} результатов, но все операции поиска выполняются со всем текстом.",
		"{0} из {1}",
		"Результаты отсутствуют",
		"{0} обнаружено",
		"{0} найден для \"{1}\"",
		"{0} найден для \"{1}\", в {2}",
		"{0} найден для \"{1}\"",
		"Теперь при нажатии клавиш CTRL+ВВОД вставляется символ перехода на новую строку вместо замены всего текста. Вы можете изменить сочетание клавиш editor.action.replaceAll, чтобы переопределить это поведение.",
	],
	"vs/editor/contrib/folding/folding": [
		"Развернуть",
		"Развернуть рекурсивно",
		"Свернуть",
		"Переключить свертывание",
		"Свернуть рекурсивно",
		"Свернуть все блоки комментариев",
		"Свернуть все регионы",
		"Развернуть все регионы",
		"Свернуть все регионы, кроме выбранных",
		"Развернуть все регионы, кроме выбранных",
		"Свернуть все",
		"Развернуть все",
		"Перейти к родительскому свертыванию",
		"Перейти к предыдущему диапазону сложенных данных",
		"Перейти к следующему диапазону сложенных данных",
		"Уровень папки {0}",
		"Цвет фона за свернутыми диапазонами. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже декоративные элементы.",
		"Цвет элемента управления свертыванием во внутреннем поле редактора.",
	],
	"vs/editor/contrib/folding/foldingDecorations": [
		"Значок для развернутых диапазонов на поле глифов редактора.",
		"Значок для свернутых диапазонов на поле глифов редактора.",
	],
	"vs/editor/contrib/fontZoom/fontZoom": [
		"Увеличить шрифт редактора",
		"Уменьшить шрифт редактора",
		"Сбросить масштаб шрифта редактора",
	],
	"vs/editor/contrib/format/format": [
		"Внесена одна правка форматирования в строке {0}.",
		"Внесены правки форматирования ({0}) в строке {1}.",
		"Внесена одна правка форматирования между строками {0} и {1}.",
		"Внесены правки форматирования ({0}) между строками {1} и {2}.",
	],
	"vs/editor/contrib/format/formatActions": [
		"Форматировать документ",
		"Форматировать выделенный фрагмент",
	],
	"vs/editor/contrib/gotoError/gotoError": [
		"Перейти к Следующей Проблеме (Ошибке, Предупреждению, Информации)",
		"Значок для перехода к следующему маркеру.",
		"Перейти к Предыдущей Проблеме (Ошибке, Предупреждению, Информации)",
		"Значок для перехода к предыдущему маркеру.",
		"Перейти к следующей проблеме в файлах (ошибки, предупреждения, информационные сообщения)",
		"Следующая &&проблема",
		"Перейти к предыдущей проблеме в файлах (ошибки, предупреждения, информационные сообщения)",
		"Предыдущая &&проблема",
	],
	"vs/editor/contrib/gotoError/gotoErrorWidget": [
		"Ошибка",
		"Предупреждение",
		"Информация",
		"Указание",
		"{0} в {1}. ",
		"Проблемы: {0} из {1}",
		"Проблемы: {0} из {1}",
		"Цвет ошибки в мини-приложении навигации по меткам редактора.",
		"Фон заголовка ошибки в мини-приложении навигации по меткам редактора.",
		"Цвет предупреждения в мини-приложении навигации по меткам редактора.",
		"Фон заголовка предупреждения в мини-приложении навигации по меткам редактора.",
		"Цвет информационного сообщения в мини-приложении навигации по меткам редактора.",
		"Фон заголовка информационного сообщения в мини-приложении навигации по меткам редактора.",
		"Фон мини-приложения навигации по меткам редактора.",
	],
	"vs/editor/contrib/gotoSymbol/goToCommands": [
		"Обзор",
		"Определения",
		"Определение для \"{0}\" не найдено.",
		"Определения не найдены.",
		"Перейти к определению",
		"Открыть определение сбоку",
		"Показать определение",
		"Объявления",
		"Объявление для \"{0}\" не найдено.",
		"Объявление не найдено",
		"Перейти к объявлению",
		"Объявление для \"{0}\" не найдено.",
		"Объявление не найдено",
		"Просмотреть объявление",
		"Определения типов",
		"Не найдено определение типа для \"{0}\".",
		"Не найдено определение типа.",
		"Перейти к определению типа",
		"Показать определение типа",
		"Реализации",
		"Не найдена реализация для \"{0}\".",
		"Не найдена реализация.",
		"Перейти к реализациям",
		"Просмотреть реализации",
		"Ссылки для \"{0}\" не найдены",
		"Ссылки не найдены",
		"Перейти к ссылкам",
		"Ссылки",
		"Показать ссылки",
		"Ссылки",
		"Перейти к любому символу",
		"Расположения",
		"Нет результатов для \"{0}\"",
		"Ссылки",
		"Перейти к &&определению",
		"Перейти к &&объявлению",
		"Перейти к &&определению типа",
		"Перейти к &&реализациям",
		"Перейти к &&ссылкам",
	],
	"vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition": [
		"Щелкните, чтобы отобразить определения ({0}).",
	],
	"vs/editor/contrib/gotoSymbol/peek/referencesController": [
		"Открыто ли окно просмотра ссылок, например, \"Ссылки для просмотра\" или \"Определение просмотра\"",
		"Загрузка...",
		"{0} ({1})",
	],
	"vs/editor/contrib/gotoSymbol/peek/referencesTree": [
		"Ссылок: {0}",
		"{0} ссылка",
		"Ссылки",
	],
	"vs/editor/contrib/gotoSymbol/peek/referencesWidget": [
		"предварительный просмотр недоступен",
		"Результаты отсутствуют",
		"Ссылки",
	],
	"vs/editor/contrib/gotoSymbol/referencesModel": [
		"ссылка в {0} в строке {1} и символе {2}",
		"символ в {0} в строке {1} и столбце {2}, {3}",
		"1 символ в {0}, полный путь: {1}",
		"{0} символов в {1}, полный путь: {2} ",
		"Результаты не найдены",
		"Обнаружен 1 символ в {0}",
		"Обнаружено {0} символов в {1}",
		"Обнаружено {0} символов в {1} файлах",
	],
	"vs/editor/contrib/gotoSymbol/symbolNavigation": [
		"Существуют ли расположения символов, к которым можно перейти только с помощью клавиатуры",
		"Символ {0} из {1}, {2} для следующего",
		"Символ {0} из {1}",
	],
	"vs/editor/contrib/hover/hover": [
		"Показать при наведении",
		"Отображать предварительный просмотр определения при наведении курсора мыши",
	],
	"vs/editor/contrib/hover/markdownHoverParticipant": [
		"Загрузка...",
		"Разметка пропускается для длинных строк из соображений производительности. Это можно настроить с помощью \"editor.maxTokenizationLineLength\".",
	],
	"vs/editor/contrib/hover/markerHoverParticipant": [
		"Просмотреть проблему",
		"Исправления недоступны",
		"Проверка наличия исправлений...",
		"Исправления недоступны",
		"Быстрое исправление...",
	],
	"vs/editor/contrib/inPlaceReplace/inPlaceReplace": [
		"Заменить предыдущим значением",
		"Заменить следующим значением",
	],
	"vs/editor/contrib/indentation/indentation": [
		"Преобразовать отступ в пробелы",
		"Преобразовать отступ в шаги табуляции",
		"Настроенный размер шага табуляции",
		"Выбрать размер шага табуляции для текущего файла",
		"Отступ с использованием табуляции",
		"Отступ с использованием пробелов",
		"Определение отступа от содержимого",
		"Повторно расставить отступы строк",
		"Повторно расставить отступы для выбранных строк",
	],
	"vs/editor/contrib/inlineCompletions/ghostTextController": [
		"Отображается ли встроенное предложение",
		"Начинается ли встроенное предложение с пробела",
		"Проверяет, не является ли пробел перед встроенной рекомендацией короче, чем текст, вставляемый клавишей TAB",
		"Показывать следующее встроенное предложение",
		"Показать предыдущее встроенное предложение",
		"Активировать встроенное предложение",
	],
	"vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant": [
		"Далее",
		"Назад",
		"Принять",
		"Предложение:",
	],
	"vs/editor/contrib/lineSelection/lineSelection": [
		"Развернуть выделение строки",
	],
	"vs/editor/contrib/linesOperations/linesOperations": [
		"Копировать строку сверху",
		"&&Копировать на строку выше",
		"Копировать строку снизу",
		"Копировать на строку &&ниже",
		"Дублировать выбранное",
		"&&Дублировать выбранное",
		"Переместить строку вверх",
		"Переместить на с&&троку выше",
		"Переместить строку вниз",
		"&&Переместить на строку ниже",
		"Сортировка строк по возрастанию",
		"Сортировка строк по убыванию",
		"Удалить дублирующиеся строки",
		"Удалить конечные символы-разделители",
		"Удалить строку",
		"Увеличить отступ",
		"Уменьшить отступ",
		"Вставить строку выше",
		"Вставить строку ниже",
		"Удалить все слева",
		"Удалить все справа",
		"_Объединить строки",
		"Транспонировать символы вокруг курсора",
		"Преобразовать в верхний регистр",
		"Преобразовать в нижний регистр",
		"Преобразовать в заглавные буквы",
		"Преобразовать в написание с подчеркиваниями",
	],
	"vs/editor/contrib/linkedEditing/linkedEditing": [
		"Запустить связанное редактирование",
		"Цвет фона при автоматическом переименовании типа редактором.",
	],
	"vs/editor/contrib/links/links": [
		"Выполнить команду",
		"перейти по ссылке",
		"Кнопка CMD и щелчок левой кнопкой мыши",
		"Кнопка CTRL и щелчок левой кнопкой мыши",
		"Кнопка OPTION и щелчок левой кнопкой мыши",
		"Кнопка ALT и щелчок левой кнопкой мыши",
		"Выполнение команды {0}",
		"Не удалось открыть ссылку, так как она имеет неправильный формат: {0}",
		"Не удалось открыть ссылку, у нее отсутствует целевой объект.",
		"Открыть ссылку",
	],
	"vs/editor/contrib/message/messageController": [
		"Отображается ли сейчас в редакторе внутреннее сообщение",
		"Не удается выполнить изменение в редакторе только для чтения",
	],
	"vs/editor/contrib/multicursor/multicursor": [
		"Курсор добавлен: {0}",
		"Курсоры добавлены: {0}",
		"Добавить курсор выше",
		"Добавить курсор &&выше",
		"Добавить курсор ниже",
		"Добавить курсор &&ниже",
		"Добавить курсоры к окончаниям строк",
		"Добавить курсоры в &&окончания строк",
		"Добавить курсоры ниже",
		"Добавить курсоры выше",
		"Добавить выделение в следующее найденное совпадение",
		"Добавить &&следующее вхождение",
		"Добавить выделенный фрагмент в предыдущее найденное совпадение",
		"Добавить &&предыдущее вхождение",
		"Переместить последнее выделение в следующее найденное совпадение",
		"Переместить последний выделенный фрагмент в предыдущее найденное совпадение",
		"Выбрать все вхождения найденных совпадений",
		"Выбрать все &&вхождения",
		"Изменить все вхождения",
	],
	"vs/editor/contrib/parameterHints/parameterHints": [
		"Переключить подсказки к параметрам",
	],
	"vs/editor/contrib/parameterHints/parameterHintsWidget": [
		"Значок для отображения подсказки следующего параметра.",
		"Значок для отображения подсказки предыдущего параметра.",
		"{0}, указание",
		"Цвет переднего плана активного элемента в указании параметра.",
	],
	"vs/editor/contrib/peekView/peekView": [
		"Встроен ли текущий редактор кода в окно просмотра",
		"Закрыть",
		"Цвет фона области заголовка быстрого редактора.",
		"Цвет заголовка быстрого редактора.",
		"Цвет сведений о заголовке быстрого редактора.",
		"Цвет границ быстрого редактора и массива.",
		"Цвет фона в списке результатов представления быстрого редактора.",
		"Цвет переднего плана узлов строки в списке результатов быстрого редактора.",
		"Цвет переднего плана узлов файла в списке результатов быстрого редактора.",
		"Цвет фона выбранной записи в списке результатов быстрого редактора.",
		"Цвет переднего плана выбранной записи в списке результатов быстрого редактора.",
		"Цвет фона быстрого редактора.",
		"Цвет фона поля в окне быстрого редактора.",
		"Цвет выделения совпадений в списке результатов быстрого редактора.",
		"Цвет выделения совпадений в быстром редакторе.",
		"Граница выделения совпадений в быстром редакторе.",
	],
	"vs/editor/contrib/quickAccess/gotoLineQuickAccess": [
		"Чтобы перейти к строке, сначала откройте текстовый редактор.",
		"Перейдите к строке {0} и столбцу {1}.",
		"Перейти к строке {0}.",
		"Текущая строка: {0}, символ: {1}. Введите номер строки между 1 и {2} для перехода.",
		"Текущая строка: {0}, символ: {1}. Введите номер строки для перехода.",
	],
	"vs/editor/contrib/quickAccess/gotoSymbolQuickAccess": [
		"Чтобы перейти к символу, сначала откройте текстовый редактор с символьной информацией.",
		"Активный текстовый редактор не предоставляет символьную информацию.",
		"Нет совпадающих символов редактора",
		"Нет символов редактора",
		"Открыть сбоку",
		"Открыть внизу",
		"символы ({0})",
		"свойства ({0})",
		"методы ({0})",
		"функции ({0})",
		"конструкторы ({0})",
		"переменные ({0})",
		"классы ({0})",
		"структуры ({0})",
		"события ({0})",
		"операторы ({0})",
		"интерфейсы ({0})",
		"пространства имен ({0})",
		"пакеты ({0})",
		"параметры типа ({0})",
		"модули ({0})",
		"свойства ({0})",
		"перечисления ({0})",
		"элемента перечисления ({0})",
		"строки ({0})",
		"файлы ({0})",
		"массивы ({0})",
		"числа ({0})",
		"логические значения ({0})",
		"объекты ({0})",
		"ключи ({0})",
		"поля ({0})",
		"константы ({0})",
	],
	"vs/editor/contrib/rename/rename": [
		"Результаты отсутствуют.",
		"Произошла неизвестная ошибка при определении расположения после переименования",
		"Переименование \"{0}\"",
		"Переименование {0}",
		"«{0}» успешно переименован в «{1}». Сводка: {2}",
		"Операции переименования не удалось применить правки",
		"Операции переименования не удалось вычислить правки",
		"Переименовать символ",
		"Включить/отключить возможность предварительного просмотра изменений перед переименованием",
	],
	"vs/editor/contrib/rename/renameInputField": [
		"Отображается ли мини-приложение переименования входных данных",
		"Введите новое имя для входных данных и нажмите клавишу ВВОД для подтверждения.",
		"Нажмите {0} для переименования, {1} для просмотра.",
	],
	"vs/editor/contrib/smartSelect/smartSelect": [
		"Развернуть выбранный фрагмент",
		"&&Развернуть выделение",
		"Уменьшить выделенный фрагмент",
		"&&Сжать выделение",
	],
	"vs/editor/contrib/snippet/snippetController2": [
		"Находится ли текущий редактор в режиме фрагментов",
		"Указывает, существует ли следующая позиция табуляции в режиме фрагментов",
		"Указывает, существует ли предыдущая позиция табуляции в режиме фрагментов",
	],
	"vs/editor/contrib/snippet/snippetVariables": [
		"воскресенье",
		"понедельник",
		"вторник",
		"среда",
		"четверг",
		"пятница",
		"суббота",
		"Вс",
		"Пн",
		"Вт",
		"Ср",
		"Чт",
		"Пт",
		"Сб",
		"Январь",
		"Февраль",
		"Март",
		"Апрель",
		"Май",
		"Июнь",
		"Июль",
		"Август",
		"Сентябрь",
		"Октябрь",
		"Ноябрь",
		"Декабрь",
		"Янв",
		"Фев",
		"Мар",
		"Апр",
		"Май",
		"Июн",
		"Июл",
		"Авг",
		"Сен",
		"Окт",
		"Ноя",
		"Дек",
	],
	"vs/editor/contrib/suggest/suggest": [
		"Отображаются ли предложения",
		"Отображаются ли сведения о предложениях",
		"Существует ли несколько предложений для выбора",
		"Приводит ли вставка текущего предложения к изменению или все уже было введено",
		"Вставляются ли предложения при нажатии клавиши ВВОД",
		"Есть ли у текущего предложения варианты поведения \"вставка\" и \"замена\"",
		"Является ли текущее поведение поведением \"вставка\" или \"замена\"",
		"Поддерживает ли текущее предложение разрешение дополнительных сведений",
	],
	"vs/editor/contrib/suggest/suggestController": [
		"Принятие \"{0}\" привело к внесению дополнительных правок ({1})",
		"Переключить предложение",
		"Вставить",
		"Вставить",
		"Заменить",
		"Заменить",
		"Вставить",
		"показать меньше",
		"показать больше",
		"Сброс предложения размера мини-приложения",
	],
	"vs/editor/contrib/suggest/suggestWidget": [
		"Цвет фона виджета подсказок.",
		"Цвет границ виджета подсказок.",
		"Цвет переднего плана мини-приложения предложений.",
		"Цвет переднего плана выбранной записи в мини-приложении предложений.",
		"Цвет переднего плана значка выбранной записи в мини-приложении предложений.",
		"Фоновый цвет выбранной записи в мини-приложении предложений.",
		"Цвет выделения соответствия в мини-приложении предложений.",
		"Цвет совпадения выделяется в мини-приложениях предложений, когда элемент находится в фокусе.",
		"Цвет переднего плана для состояния рекомендации мини-приложения.",
		"Загрузка...",
		"Предложения отсутствуют.",
		"{0}, документы: {1}",
		"Предложить",
	],
	"vs/editor/contrib/suggest/suggestWidgetDetails": [
		"Закрыть",
		"Загрузка...",
	],
	"vs/editor/contrib/suggest/suggestWidgetRenderer": [
		"Значок для получения дополнительных сведений в мини-приложении предложений.",
		"Подробнее",
	],
	"vs/editor/contrib/suggest/suggestWidgetStatus": [
		"{0} ({1})",
	],
	"vs/editor/contrib/symbolIcons/symbolIcons": [
		"Цвет переднего плана для символов массива. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для логических символов. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов класса. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов цвета. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов константы. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов конструктора. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов перечислителя. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов члена перечислителя. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов события. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов поля. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов файла. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов папки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов функции. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов интерфейса. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов ключа. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов ключевого слова. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов метода. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов модуля. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов пространства имен. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов NULL. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов числа. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов объекта. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов оператора. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов пакета. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов свойства. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов ссылки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов фрагмента кода. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов строки. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов структуры. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов текста. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов типа параметров. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов единиц. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
		"Цвет переднего плана для символов переменной. Эти символы отображаются в структуре, элементе навигации и мини-приложении предложений.",
	],
	"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode": [
		"Переключение клавиши TAB перемещает фокус.",
		"При нажатии клавиши TAB фокус перейдет на следующий элемент, который может получить фокус",
		"Теперь при нажатии клавиши TAB будет вставлен символ табуляции",
	],
	"vs/editor/contrib/tokenization/tokenization": [
		"Разработчик: принудительная повторная установка токенов",
	],
	"vs/editor/contrib/unicodeHighlighter/unicodeHighlighter": [
		"Значок, отображаемый с предупреждением в редакторе расширений.",
		"Этот документ содержит много нестандартных символов Юникода ASCII",
		"Этот документ содержит много неоднозначных символов Юникода",
		"Этот документ содержит много невидимых символов Юникода",
		"Символ {0} можно спутать с символом {1}, который чаще встречается в исходном коде.",
		"Символ {0} невидим.",
		"Символ {0} не является базовым символом ASCII.",
		"Настройка параметров",
		"Отключить неоднозначное выделение",
		"Отключить выделение неоднозначных символов",
		"Отключить невидимое выделение",
		"Отключить выделение невидимых символов",
		"Отключить выделение, отличное от ASCII",
		"Отключить выделение нестандартных символов ASCII",
		"Показать параметры исключения",
		"Исключить {0} (невидимый символ) из выделения",
		"Исключить {0} из выделения",
		"Настройка параметров выделения Юникода",
	],
	"vs/editor/contrib/unusualLineTerminators/unusualLineTerminators": [
		"Необычные символы завершения строки",
		"Обнаружены необычные символы завершения строки",
		"Файл \"{0}\" содержит один или несколько необычных символов завершения строки, таких как разделитель строк (LS) или разделитель абзацев (PS).\r\n\r\nРекомендуется удалить их из файла. Удаление этих символов можно настроить с помощью параметра \"editor.unusualLineTerminators\".",
		"Удалить необычные символы завершения строки",
		"Пропустить",
	],
	"vs/editor/contrib/wordHighlighter/wordHighlighter": [
		"Цвет фона символа при доступе на чтение, например, при чтении переменной. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет фона для символа во время доступа на запись, например при записи в переменную. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет границы символа при доступе на чтение, например, при считывании переменной.",
		"Цвет границы символа при доступе на запись, например, при записи переменной. ",
		"Цвет маркера обзорной линейки для выделения символов. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.",
		"Цвет маркера обзорной линейки для выделения символов доступа на запись. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Перейти к следующему выделению символов",
		"Перейти к предыдущему выделению символов",
		"Включить или отключить выделение символов",
	],
	"vs/editor/contrib/wordOperations/wordOperations": [
		"Удалить слово",
	],
	"vs/platform/actions/browser/menuEntryActionViewItem": [
		"{0} ({1})",
		"{0} ({1})",
	],
	"vs/platform/configuration/common/configurationRegistry": [
		"Переопределения конфигурации языка по умолчанию",
		"Настройка переопределяемых параметров для языка {0}.",
		"Настройка параметров редактора, переопределяемых для языка.",
		"Этот параметр не поддерживает настройку для отдельных языков.",
		"Настройка параметров редактора, переопределяемых для языка.",
		"Этот параметр не поддерживает настройку для отдельных языков.",
		"Не удается зарегистрировать пустое свойство",
		"Невозможно зарегистрировать \"{0}\". Оно соответствует шаблону свойства \'\\\\[.*\\\\]$\' для описания параметров редактора, определяемых языком. Используйте участие configurationDefaults.",
		"Невозможно зарегистрировать \"{0}\". Это свойство уже зарегистрировано.",
	],
	"vs/platform/contextkey/browser/contextKeyService": [
		"Команда, возвращающая сведения о ключах контекста",
	],
	"vs/platform/contextkey/common/contextkeys": [
		"Используется ли операционная система macOS",
		"Используется ли операционная система Linux",
		"Используется ли операционная система Windows",
		"Является ли платформа браузерной",
		"Используется ли операционная система macOS на платформе, отличной от браузерной",
		"Используется ли операционная система IOS",
		"Находится ли фокус клавиатуры в поле ввода",
	],
	"vs/platform/keybinding/common/abstractKeybindingService": [
		"Была нажата клавиша {0}. Ожидание нажатия второй клавиши сочетания...",
		"Сочетание клавиш ({0} и {1}) не является командой.",
	],
	"vs/platform/list/browser/listService": [
		"Рабочее место",
		"Соответствует клавише CTRL в Windows и Linux и клавише COMMAND в macOS.",
		"Соответствует клавише ALT в Windows и Linux и клавише OPTION в macOS.",
		"Модификатор, который будет использоваться для добавления элементов в деревьях и списках в элемент множественного выбора с помощью мыши (например, в проводнике, в открытых редакторах и в представлении scm). Жесты мыши \"Открыть сбоку\" (если они поддерживаются) будут изменены таким образом, чтобы они не конфликтовали с модификатором элемента множественного выбора.",
		"Управляет тем, как открывать элементы в деревьях и списках с помощью мыши (если поддерживается). Обратите внимание, что этот параметр может игнорироваться в некоторых деревьях и списках, если он не применяется к ним.",
		"Определяет, поддерживают ли горизонтальную прокрутку списки и деревья на рабочем месте. Предупреждение! Включение этого параметра может повлиять на производительность.",
		"Определяет отступ для дерева в пикселях.",
		"Определяет, нужно ли в дереве отображать направляющие отступа.",
		"Управляет тем, используется ли плавная прокрутка для списков и деревьев.",
		"Множитель, используемый для параметров deltaX и deltaY событий прокрутки колесика мыши.",
		"Коэффициент увеличения скорости прокрутки при нажатии клавиши ALT.",
		"Про простой навигации с клавиатуры выбираются элементы, соответствующие вводимым с клавиатуры данным. Сопоставление осуществляется только по префиксам.",
		"Функция подсветки навигации с клавиатуры выделяет элементы, соответствующие вводимым с клавиатуры данным. При дальнейшей навигации вверх и вниз выполняется обход только выделенных элементов.",
		"Фильтр навигации с клавиатуры позволяет отфильтровать и скрыть все элементы, не соответствующие вводимым с клавиатуры данным.",
		"Управляет стилем навигации с клавиатуры для списков и деревьев в Workbench. Доступен простой режим, режим выделения и режим фильтрации.",
		"Указывает, активируется ли навигация с помощью клавиатуры в списках и деревьях автоматически простым вводом. Если задано значение \"false\", навигация с клавиатуры активируется только при выполнении команды \"list.toggleKeyboardNavigation\", для которой можно назначить сочетание клавиш.",
		"Управляет тем, как папки дерева разворачиваются при нажатии на имена папок. Обратите внимание, что этот параметр может игнорироваться в некоторых деревьях и списках, если он не применяется к ним.",
	],
	"vs/platform/markers/common/markers": [
		"Ошибка",
		"Предупреждение",
		"Информация",
	],
	"vs/platform/quickinput/browser/commandsQuickAccess": [
		"{0}, {1}",
		"недавно использованные",
		"другие команды",
		"Команда \"{0}\" привела к ошибке ({1})",
	],
	"vs/platform/quickinput/browser/helpQuickAccess": [
		"глобальные команды",
		"команды редактора",
		"{0}, {1}",
	],
	"vs/platform/theme/common/colorRegistry": [
		"Общий цвет переднего плана. Этот цвет используется, только если его не переопределит компонент.",
		"Общий цвет переднего плана для сообщений об ошибках. Этот цвет используется только если его не переопределяет компонент.",
		"Цвет текста элемента, содержащего пояснения, например, для метки.",
		"Цвет по умолчанию для значков на рабочем месте.",
		"Общий цвет границ для элементов с фокусом. Этот цвет используется только в том случае, если не переопределен в компоненте.",
		"Дополнительная граница вокруг элементов, которая отделяет их от других элементов для улучшения контраста.",
		"Дополнительная граница вокруг активных элементов, которая отделяет их от других элементов для улучшения контраста.",
		"Цвет фона выделенного текста в рабочей области (например, в полях ввода или в текстовых полях). Не применяется к выделенному тексту в редакторе.",
		"Цвет для разделителей текста.",
		"Цвет переднего плана для ссылок в тексте.",
		"Цвет переднего плана для ссылок в тексте при щелчке и при наведении курсора мыши.",
		"Цвет текста фиксированного формата.",
		"Цвет фона для блоков с цитатами в тексте.",
		"Цвет границ для блоков с цитатами в тексте.",
		"Цвет фона для программного кода в тексте.",
		"Цвет тени мини-приложений редактора, таких как \"Найти/заменить\".",
		"Фон поля ввода.",
		"Передний план поля ввода.",
		"Граница поля ввода.",
		"Цвет границ активированных параметров в полях ввода.",
		"Цвет фона активированных параметров в полях ввода.",
		"Цвет фонового наведения параметров в полях ввода.",
		"Цвет переднего плана активированных параметров в полях ввода.",
		"Цвет фона поясняющего текста в элементе ввода.",
		"Фоновый цвет проверки ввода для уровня серьезности \"Сведения\".",
		"Цвет переднего плана области проверки ввода для уровня серьезности \"Сведения\".",
		"Цвет границы проверки ввода для уровня серьезности \"Сведения\".",
		"Фоновый цвет проверки ввода для уровня серьезности \"Предупреждение\".",
		"Цвет переднего плана области проверки ввода для уровня серьезности \"Предупреждение\".",
		"Цвет границы проверки ввода для уровня серьезности \"Предупреждение\".",
		"Фоновый цвет проверки ввода для уровня серьезности \"Ошибка\".",
		"Цвет переднего плана области проверки ввода для уровня серьезности \"Ошибка\".",
		"Цвет границы проверки ввода для уровня серьезности \"Ошибка\".",
		"Фон раскрывающегося списка.",
		"Цвет фона раскрывающегося списка.",
		"Передний план раскрывающегося списка.",
		"Граница раскрывающегося списка.",
		"Цвет фона мини-приложения флажка.",
		"Цвет переднего плана мини-приложения флажка.",
		"Цвет границы мини-приложения флажка.",
		"Цвет переднего плана кнопки.",
		"Цвет фона кнопки.",
		"Цвет фона кнопки при наведении.",
		"Цвет границы кнопки.",
		"Цвет переднего плана вторичной кнопки.",
		"Цвет фона вторичной кнопки.",
		"Цвет фона вторичной кнопки при наведении курсора мыши.",
		"Цвет фона бэджа. Бэджи - небольшие информационные элементы, отображающие количество, например, результатов поиска.",
		"Цвет текста бэджа. Бэджи - небольшие информационные элементы, отображающие количество, например, результатов поиска.",
		"Цвет тени полосы прокрутки, которая свидетельствует о том, что содержимое прокручивается.",
		"Цвет фона для ползунка полосы прокрутки.",
		"Цвет фона ползунка полосы прокрутки при наведении курсора.",
		"Цвет фона ползунка полосы прокрутки при щелчке по нему.",
		"Цвет фона индикатора выполнения, который может отображаться для длительных операций.",
		"Цвет фона для текста ошибки в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет волнистой линии для выделения ошибок в редакторе.",
		"Цвет границы для окон ошибок в редакторе.",
		"Цвет фона для текста предупреждения в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет волнистой линии для выделения предупреждений в редакторе.",
		"Цвет границы для окон предупреждений в редакторе.",
		"Цвет фона для текста информационного сообщения в редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет волнистой линии для выделения информационных сообщений в редакторе.",
		"Цвет границы для окон сведений в редакторе.",
		"Цвет волнистой линии для выделения подсказок в редакторе.",
		"Цвет границы для окон указаний в редакторе.",
		"Цвет границы активных лент.",
		"Цвет фона редактора.",
		"Цвет переднего плана редактора по умолчанию.",
		"Цвет фона виджетов редактора, таких как найти/заменить.",
		"Цвет переднего плана мини-приложений редактора, таких как \"Поиск/замена\".",
		"Цвет границы мини-приложений редактора. Этот цвет используется только в том случае, если у мини-приложения есть граница и если этот цвет не переопределен мини-приложением.",
		"Цвет границы панели изменения размера мини-приложений редактора. Этот цвет используется только в том случае, если у мини-приложения есть граница для изменения размера и если этот цвет не переопределен мини-приложением.",
		"Цвет фона для средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.",
		"Цвет переднего плана для средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.",
		"Цвет фона для заголовка средства быстрого выбора. Мини-приложение быстрого выбора является контейнером для таких средств выбора, как палитра команд.",
		"Цвет средства быстрого выбора для группировки меток.",
		"Цвет средства быстрого выбора для группировки границ.",
		"Цвет фона метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.",
		"Цвет переднего плана метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.",
		"Цвет границы метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.",
		"Цвет нижней границы метки настраиваемого сочетания клавиш. Метка настраиваемого сочетания клавиш используется для обозначения сочетания клавиш.",
		"Цвет выделения редактора.",
		"Цвет выделенного текста в режиме высокого контраста.",
		"Цвет выделения в неактивном редакторе. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет для областей, содержимое которых совпадает с выбранным фрагментом. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет границы регионов с тем же содержимым, что и в выделении.",
		"Цвет текущего поиска совпадений.",
		"Цвет других совпадений при поиске. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет диапазона, ограничивающего поиск. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет границы текущего результата поиска.",
		"Цвет границы других результатов поиска.",
		"Цвет границы для диапазона, ограничивающего поиск. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет соответствий для запроса в редакторе поиска.",
		"Цвет границы для соответствующих запросов в редакторе поиска.",
		"Выделение под словом, для которого отображается меню при наведении курсора. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет фона при наведении указателя на редактор.",
		"Цвет переднего плана для наведения указателя на редактор.",
		"Цвет границ при наведении указателя на редактор.",
		"Цвет фона строки состояния при наведении в редакторе.",
		"Цвет активных ссылок.",
		"Цвет переднего плана встроенных указаний",
		"Цвет фона встроенных указаний",
		"Цвет переднего плана встроенных указаний для шрифтов",
		"Цвет фона встроенных указаний для шрифтов",
		"Цвет переднего плана встроенных указаний для параметров",
		"Цвет фона встроенных указаний для параметров",
		"Цвет, используемый для значка действий в меню лампочки.",
		"Цвет, используемый для значка действий автоматического исправления в меню лампочки.",
		"Цвет фона для вставленного текста. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет фона для удаленного текста. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет контура для добавленных строк.",
		"Цвет контура для удаленных строк.",
		"Цвет границы между двумя текстовыми редакторами.",
		"Цвет диагональной заливки для редактора несовпадений. Диагональная заливка используется в размещаемых рядом представлениях несовпадений.",
		"Фоновый цвет находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Цвет переднего плана находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Цвет контура находящегося в фокусе элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Фоновый цвет выбранного элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Цвет переднего плана выбранного элемента List/Tree, когда элемент List/Tree активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Цвет переднего плана значка списка или дерева для выбранного элемента, когда список или дерево активны. Активный список или дерево находятся в фокусе клавиатуры, а неактивный — нет.",
		"Фоновый цвет выбранного элемента List/Tree, когда элемент List/Tree неактивен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Цвет текста выбранного элемента List/Tree, когда элемент List/Tree неактивен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Цвет переднего плана значка списка или дерева для выбранного элемента, когда список или дерево неактивны. Активный список или дерево находятся в фокусе клавиатуры, а неактивный — нет.",
		"Фоновый цвет находящегося в фокусе элемента List/Tree, когда элемент List/Tree не активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Цвет контура находящегося в фокусе элемента List/Tree, когда элемент List/Tree не активен. На активном элементе List/Tree есть фокус клавиатуры, на неактивном — нет.",
		"Фоновый цвет элементов List/Tree при наведении курсора мыши.",
		"Цвет переднего плана элементов List/Tree при наведении курсора мыши.",
		"Фоновый цвет элементов List/Tree при перемещении с помощью мыши.",
		"Цвет переднего плана для выделения соответствия при поиске по элементу List/Tree.",
		"Цвет переднего плана для выделения соответствия выделенных элементов при поиске по элементу List/Tree.",
		"Цвет переднего плана списка/дерева для недопустимых элементов, например, для неразрешенного корневого узла в проводнике.",
		"Цвет переднего плана элементов списка, содержащих ошибки.",
		"Цвет переднего плана элементов списка, содержащих предупреждения.",
		"Цвет фона для мини-приложения фильтра типов в списках и деревьях.",
		"Цвет контура для мини-приложения фильтра типов в списках и деревьях.",
		"Цвет контура для мини-приложения фильтра типов в списках и деревьях при отсутствии совпадений.",
		"Цвет фона для отфильтрованного совпадения.",
		"Цвет границы для отфильтрованного совпадения.",
		"Цвет штриха дерева для направляющих отступа.",
		"Цвет границы таблицы между столбцами.",
		"Цвет фона для нечетных строк таблицы.",
		"Цвет переднего плана в списке/дереве для элементов, выделение которых отменено.",
		"Рекомендуется использовать quickInputList.focusBackground.",
		"Цвет переднего плана средства быстрого выбора для элемента, на котором находится фокус.",
		"Цвет переднего плана значка средства быстрого выбора для элемента, на котором находится фокус.",
		"Цвет фона средства быстрого выбора для элемента, на котором находится фокус.",
		"Цвет границ меню.",
		"Цвет переднего плана пунктов меню.",
		"Цвет фона пунктов меню.",
		"Цвет переднего плана выбранного пункта меню в меню.",
		"Цвет фона для выбранного пункта в меню.",
		"Цвет границы для выбранного пункта в меню.",
		"Цвет разделителя меню в меню.",
		"Фон панели инструментов при наведении указателя мыши на действия",
		"Контур панели инструментов при наведении указателя мыши на действия",
		"Фон панели инструментов при удержании указателя мыши над действиями",
		"Цвет фона выделения в позиции табуляции фрагмента.",
		"Цвет границы выделения в позиции табуляции фрагмента.",
		"Цвет фона выделения в последней позиции табуляции фрагмента.",
		"Выделение цветом границы в последней позиции табуляции фрагмента.",
		"Цвет элементов навигации, находящихся в фокусе.",
		"Фоновый цвет элементов навигации.",
		"Цвет элементов навигации, находящихся в фокусе.",
		"Цвет выделенных элементов навигации.",
		"Фоновый цвет средства выбора элементов навигации.",
		"Текущий цвет фона заголовка при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Фон текущего содержимого при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Фон входящего заголовка при внутренних конфликтах объединения. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Фон входящего содержимого при внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Фон заголовка общего предка во внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Фон содержимого общего предка во внутренних конфликтах слияния. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет границы заголовков и разделителя во внутренних конфликтах слияния.",
		"Цвет переднего плана линейки текущего окна во внутренних конфликтах слияния.",
		"Цвет переднего плана линейки входящего окна во внутренних конфликтах слияния.",
		"Цвет переднего плана для обзорной линейки для общего предка во внутренних конфликтах слияния. ",
		"Цвет маркера обзорной линейки для совпадений при поиске. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Маркер обзорной линейки для выделения выбранного фрагмента. Цвет не должен быть непрозрачным, чтобы не скрыть расположенные ниже элементы оформления.",
		"Цвет маркера обзорной линейки для выделенных символов Юникода. Этот цвет не должен быть непрозрачным, чтобы не скрывать расположенные ниже элементы оформления.",
		"Цвет маркера мини-карты для поиска совпадений.",
		"Цвет маркера мини-карты для повторяющихся выделений редактора.",
		"Цвет маркера мини-карты для выбора редактора.",
		"Цвет маркера миникарты для ошибок.",
		"Цвет маркера миникарты для предупреждений.",
		"Цвет фона мини-карты.",
		"Прозрачность элементов переднего плана, отображаемая га мини-карте. Например, \"#000000c0\" отображает элементы с прозрачностью 75%.",
		"Цвет маркера мини-карты для выделенных символов Юникода.",
		"Цвет фона ползунка мини-карты.",
		"Цвет фона ползунка мини-карты при наведении на него указателя.",
		"Цвет фона ползунка мини-карты при его щелчке.",
		"Цвет, используемый для значка ошибки, указывающего на наличие проблем.",
		"Цвет, используемый для предупреждающего значка, указывающего на наличие проблем.",
		"Цвет, используемый для информационного значка, указывающего на наличие проблем.",
		"Цвет переднего плана на диаграммах.",
		"Цвет горизонтальных линий на диаграммах.",
		"Красный цвет, используемый в визуализациях диаграмм.",
		"Синий цвет, используемый в визуализациях диаграмм.",
		"Желтый цвет, используемый в визуализациях диаграмм.",
		"Оранжевый цвет, используемый в визуализациях диаграмм.",
		"Зеленый цвет, используемый в визуализациях диаграмм.",
		"Лиловый цвет, используемый в визуализациях диаграмм.",
	],
	"vs/platform/theme/common/iconRegistry": [
		"Идентификатор используемого шрифта. Если параметр не задан, используется шрифт, определенный первым.",
		"Символ шрифта, связанный с определением значка.",
		"Значок для действия закрытия в мини-приложениях.",
		"Значок для перехода к предыдущему расположению в редакторе.",
		"Значок для перехода к следующему расположению в редакторе.",
	],
	"vs/platform/undoRedo/common/undoRedoService": [
		"Следующие файлы были закрыты и изменены на диске: {0}.",
		"Следующие файлы были изменены несовместимым образом: {0}.",
		"Не удалось отменить \"{0}\" для всех файлов. {1}",
		"Не удалось отменить \"{0}\" для всех файлов. {1}",
		"Не удалось отменить операцию \"{0}\" для всех файлов, так как были внесены изменения в {1}",
		"Не удалось отменить действие \"{0}\" для всех файлов, так как в {1} уже выполняется операция отмены или повтора действия",
		"Не удалось отменить действие \"{0}\" для всех файлов, так как уже выполнялась операция отмены или повтора действия",
		"Вы хотите отменить \"{0}\" для всех файлов?",
		"Отменить действие в нескольких файлах ({0})",
		"Отменить этот файл",
		"Отмена",
		"Не удалось отменить действие \"{0}\", так как уже выполняется операция отмены или повтора действия",
		"Вы хотите отменить \"{0}\"?",
		"Да",
		"Отмена",
		"Не удалось повторить операцию \"{0}\" для всех файлов. {1}",
		"Не удалось повторить операцию \"{0}\" для всех файлов. {1}",
		"Не удалось повторить операцию \"{0}\" для всех файлов, так как были внесены изменения в {1}",
		"Не удалось повторить действие \"{0}\" для всех файлов, так как для {1} уже выполняется операция отмены или повтора действия.",
		"Не удалось повторить действие \"{0}\" для всех файлов, так как уже выполнялась операция отмены или повтора действия",
		"Не удалось повторить действие \"{0}\", так как уже выполняется операция отмены или повтора действия",
	],
	"vs/platform/workspaces/common/workspaces": [
		"Рабочая область кода",
	]
});