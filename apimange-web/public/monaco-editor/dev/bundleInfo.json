{"graph": {"===anonymous1===": ["require"], "===anonymous2===": ["vs/editor/editor.main", "vs/css", "vs/nls", "vs/base/common/worker/simpleWorker", "vs/editor/common/services/editorSimpleWorker"], "vs/editor/editor.main": ["require", "exports", "vs/editor/editor.api", "vs/editor/editor.all", "vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp", "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess", "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch", "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast"], "vs/css": ["require", "exports", "module"], "vs/nls": ["require", "exports", "module"], "vs/base/common/worker/simpleWorker": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/types", "vs/base/common/strings"], "vs/editor/common/services/editorSimpleWorker": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/platform", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/diff/diffComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/model/wordHelper", "vs/editor/common/modes/linkComputer", "vs/editor/common/modes/supports/inplaceReplaceSupport", "vs/editor/common/standalone/standaloneBase", "vs/base/common/types", "vs/base/common/stopwatch", "vs/editor/common/modes/unicodeTextModelHighlighter"], "vs/editor/editor.api": ["require", "exports", "vs/editor/common/config/editorOptions", "vs/editor/common/standalone/standaloneBase", "vs/editor/standalone/browser/standaloneEditor", "vs/editor/standalone/browser/standaloneLanguages", "vs/base/common/platform", "vs/editor/contrib/format/format"], "vs/editor/editor.all": ["require", "exports", "vs/editor/browser/controller/coreCommands", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditorWidget", "vs/editor/browser/widget/diffNavigator", "vs/editor/contrib/anchorSelect/anchorSelect", "vs/editor/contrib/bracketMatching/bracketMatching", "vs/editor/contrib/caretOperations/caretOperations", "vs/editor/contrib/caretOperations/transpose", "vs/editor/contrib/clipboard/clipboard", "vs/editor/contrib/codeAction/codeActionContributions", "vs/editor/contrib/codelens/codelensController", "vs/editor/contrib/colorPicker/colorContributions", "vs/editor/contrib/comment/comment", "vs/editor/contrib/contextmenu/contextmenu", "vs/editor/contrib/cursorUndo/cursorUndo", "vs/editor/contrib/dnd/dnd", "vs/editor/contrib/find/findController", "vs/editor/contrib/folding/folding", "vs/editor/contrib/fontZoom/fontZoom", "vs/editor/contrib/format/formatActions", "vs/editor/contrib/documentSymbols/documentSymbols", "vs/editor/contrib/inlineCompletions/ghostTextController", "vs/editor/contrib/gotoSymbol/goToCommands", "vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition", "vs/editor/contrib/gotoError/gotoError", "vs/editor/contrib/hover/hover", "vs/editor/contrib/indentation/indentation", "vs/editor/contrib/inlayHints/inlayHintsController", "vs/editor/contrib/inPlaceReplace/inPlaceReplace", "vs/editor/contrib/lineSelection/lineSelection", "vs/editor/contrib/linesOperations/linesOperations", "vs/editor/contrib/linkedEditing/linkedEditing", "vs/editor/contrib/links/links", "vs/editor/contrib/multicursor/multicursor", "vs/editor/contrib/parameterHints/parameterHints", "vs/editor/contrib/rename/rename", "vs/editor/contrib/smartSelect/smartSelect", "vs/editor/contrib/snippet/snippetController2", "vs/editor/contrib/suggest/suggestController", "vs/editor/contrib/tokenization/tokenization", "vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode", "vs/editor/contrib/unicodeHighlighter/unicodeHighlighter", "vs/editor/contrib/unusualLineTerminators/unusualLineTerminators", "vs/editor/contrib/viewportSemanticTokens/viewportSemanticTokens", "vs/editor/contrib/wordHighlighter/wordHighlighter", "vs/editor/contrib/wordOperations/wordOperations", "vs/editor/contrib/wordPartOperations/wordPartOperations", "vs/editor/common/standaloneStrings", "vs/base/browser/ui/codicons/codiconStyles"], "vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/formattedTextRenderer", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/widget", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/standaloneStrings", "vs/css!vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp"], "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/base/common/platform", "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard"], "vs/editor/standalone/browser/inspectTokens/inspectTokens": ["require", "exports", "vs/base/browser/dom", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/modes", "vs/editor/common/modes/nullMode", "vs/editor/common/services/modeService", "vs/editor/standalone/common/standaloneThemeService", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/standaloneStrings", "vs/platform/theme/common/theme", "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens"], "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess": ["require", "exports", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/common/standaloneStrings", "vs/platform/quickinput/browser/helpQuickAccess"], "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess": ["require", "exports", "vs/editor/contrib/quickAccess/gotoLineQuickAccess", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/browser/services/codeEditorService", "vs/base/common/types", "vs/editor/common/standaloneStrings", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput"], "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess": ["require", "exports", "vs/editor/contrib/quickAccess/gotoSymbolQuickAccess", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/browser/services/codeEditorService", "vs/base/common/types", "vs/editor/common/standaloneStrings", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput", "vs/base/browser/ui/codicons/codiconStyles", "vs/editor/contrib/symbolIcons/symbolIcons"], "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess": ["require", "exports", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/common/standaloneStrings", "vs/editor/browser/services/codeEditorService", "vs/editor/contrib/quickAccess/commandsQuickAccess", "vs/base/common/types", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/commands/common/commands", "vs/platform/telemetry/common/telemetry", "vs/platform/dialogs/common/dialogs", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput"], "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/contrib/gotoSymbol/peek/referencesController", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage"], "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/standalone/common/standaloneThemeService", "vs/editor/common/standaloneStrings"], "vs/base/common/errors": ["require", "exports"], "vs/base/common/event": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/stopwatch"], "vs/base/common/lifecycle": ["require", "exports", "vs/base/common/functional", "vs/base/common/iterator"], "vs/base/common/platform": ["require", "exports"], "vs/base/common/types": ["require", "exports"], "vs/base/common/strings": ["require", "exports", "vs/base/common/platform"], "vs/base/common/diff/diff": ["require", "exports", "vs/base/common/diff/diffChange", "vs/base/common/hash"], "vs/base/common/uri": ["require", "exports", "vs/base/common/path", "vs/base/common/platform"], "vs/editor/common/core/position": ["require", "exports"], "vs/editor/common/core/range": ["require", "exports", "vs/editor/common/core/position"], "vs/editor/common/diff/diffComputer": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/strings"], "vs/editor/common/model/mirrorTextModel": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/position", "vs/editor/common/viewModel/prefixSumComputer"], "vs/editor/common/model/wordHelper": ["require", "exports"], "vs/editor/common/modes/linkComputer": ["require", "exports", "vs/editor/common/core/characterClassifier"], "vs/editor/common/modes/supports/inplaceReplaceSupport": ["require", "exports"], "vs/editor/common/standalone/standaloneBase": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/keyCodes", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/core/token", "vs/editor/common/standalone/standaloneEnums"], "vs/base/common/stopwatch": ["require", "exports", "vs/base/common/platform"], "vs/editor/common/modes/unicodeTextModelHighlighter": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model/textModelSearch", "vs/base/common/strings", "vs/base/common/types"], "vs/editor/common/config/editorOptions": ["require", "exports", "vs/nls!vs/editor/common/config/editorOptions", "vs/base/common/platform", "vs/editor/common/model/wordHelper", "vs/base/common/arrays", "vs/base/common/objects"], "vs/editor/standalone/browser/standaloneEditor": ["require", "exports", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/services/openerService", "vs/editor/browser/widget/diffNavigator", "vs/editor/common/config/editorOptions", "vs/editor/common/config/fontInfo", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/model", "vs/editor/common/modes", "vs/editor/common/modes/nullMode", "vs/editor/common/services/editorWorkerService", "vs/editor/common/services/modeService", "vs/editor/common/services/resolverService", "vs/editor/common/services/webWorker", "vs/editor/common/standalone/standaloneEnums", "vs/editor/standalone/browser/colorizer", "vs/editor/standalone/browser/simpleServices", "vs/editor/standalone/browser/standaloneCodeEditor", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/standaloneThemeService", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/opener/common/opener", "vs/platform/accessibility/common/accessibility", "vs/editor/browser/config/configuration", "vs/platform/progress/common/progress", "vs/platform/clipboard/common/clipboardService", "vs/base/common/strings", "vs/editor/common/services/modelService", "vs/css!vs/editor/standalone/browser/standalone-tokens"], "vs/editor/standalone/browser/standaloneLanguages": ["require", "exports", "vs/base/common/color", "vs/editor/common/core/range", "vs/editor/common/core/token", "vs/editor/common/modes", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/common/modes/modesRegistry", "vs/editor/common/standalone/standaloneEnums", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/monarch/monarchCompile", "vs/editor/standalone/common/monarch/monarchLexer"], "vs/editor/contrib/format/format": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/base/common/linkedList", "vs/base/common/types", "vs/base/common/uri", "vs/editor/browser/core/editorState", "vs/editor/browser/editorBrowser", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/modes", "vs/editor/common/services/editorWorkerService", "vs/editor/common/services/resolverService", "vs/editor/contrib/format/formattingEdit", "vs/nls!vs/editor/contrib/format/format", "vs/platform/commands/common/commands", "vs/platform/extensions/common/extensions", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/controller/coreCommands": ["require", "exports", "vs/nls!vs/editor/browser/controller/coreCommands", "vs/base/browser/browser", "vs/base/common/types", "vs/base/browser/ui/aria/aria", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/controller/cursorColumnSelection", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/controller/cursorDeleteOperations", "vs/editor/common/controller/cursorMoveCommands", "vs/editor/common/controller/cursorTypeOperations", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry"], "vs/editor/browser/widget/codeEditorWidget": ["require", "exports", "vs/nls!vs/editor/browser/widget/codeEditorWidget", "vs/base/browser/dom", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/editor/browser/config/configuration", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/view/viewImpl", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/common/config/editorOptions", "vs/editor/common/controller/cursor", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorAction", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes", "vs/editor/common/view/editorColorRegistry", "vs/platform/theme/common/colorRegistry", "vs/editor/common/viewModel/viewModelImpl", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility", "vs/base/common/types", "vs/editor/common/viewModel/monospaceLineBreaksComputer", "vs/editor/browser/view/domLineBreaksComputer", "vs/editor/common/controller/cursorWordOperations", "vs/editor/browser/services/markerDecorations", "vs/css!vs/editor/browser/widget/media/editor"], "vs/editor/browser/widget/diffEditorWidget": ["require", "exports", "vs/nls!vs/editor/browser/widget/diffEditorWidget", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/sash/sash", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/config/configuration", "vs/editor/browser/core/editorState", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffReview", "vs/editor/common/config/editorOptions", "vs/editor/common/core/range", "vs/editor/common/core/stringBuilder", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/model/textModel", "vs/editor/common/services/editorWorkerService", "vs/editor/common/view/overviewZoneManager", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel/viewModel", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/platform/notification/common/notification", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/platform/contextview/browser/contextView", "vs/editor/browser/widget/inlineDiffMargin", "vs/platform/clipboard/common/clipboardService", "vs/editor/browser/editorExtensions", "vs/base/common/errors", "vs/platform/progress/common/progress", "vs/editor/browser/config/elementSizeObserver", "vs/base/common/codicons", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/platform/theme/common/iconRegistry", "vs/css!vs/editor/browser/widget/media/diffEditor"], "vs/editor/browser/widget/diffNavigator": ["require", "exports", "vs/base/common/assert", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/editor/common/core/range"], "vs/editor/contrib/anchorSelect/anchorSelect": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/htmlContent", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/anchorSelect/anchorSelect", "vs/platform/contextkey/common/contextkey", "vs/css!vs/editor/contrib/anchorSelect/anchorSelect"], "vs/editor/contrib/bracketMatching/bracketMatching": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/view/editorColorRegistry", "vs/nls!vs/editor/contrib/bracketMatching/bracketMatching", "vs/platform/actions/common/actions", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/bracketMatching/bracketMatching"], "vs/editor/contrib/caretOperations/caretOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/caretOperations/moveCaretCommand", "vs/nls!vs/editor/contrib/caretOperations/caretOperations"], "vs/editor/contrib/caretOperations/transpose": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/controller/cursorMoveOperations", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/caretOperations/transpose"], "vs/editor/contrib/clipboard/clipboard": ["require", "exports", "vs/base/browser/browser", "vs/base/common/platform", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/clipboard/clipboard", "vs/platform/actions/common/actions", "vs/platform/clipboard/common/clipboardService"], "vs/editor/contrib/codeAction/codeActionContributions": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/codeAction/codeActionCommands"], "vs/editor/contrib/codelens/codelensController": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/hash", "vs/base/common/lifecycle", "vs/editor/browser/core/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes", "vs/editor/common/modes/languageFeatureRegistry", "vs/editor/contrib/codelens/codelens", "vs/editor/contrib/codelens/codeLensCache", "vs/editor/contrib/codelens/codelensWidget", "vs/nls!vs/editor/contrib/codelens/codelensController", "vs/platform/commands/common/commands", "vs/platform/notification/common/notification", "vs/platform/quickinput/common/quickInput"], "vs/editor/contrib/colorPicker/colorContributions": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/contrib/hover/hover", "vs/editor/contrib/colorPicker/colorDetector"], "vs/editor/contrib/comment/comment": ["require", "exports", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/comment/blockCommentCommand", "vs/editor/contrib/comment/lineCommentCommand", "vs/nls!vs/editor/contrib/comment/comment", "vs/platform/actions/common/actions"], "vs/editor/contrib/contextmenu/contextmenu": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/contextmenu/contextmenu", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding"], "vs/editor/contrib/cursorUndo/cursorUndo": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/cursorUndo/cursorUndo"], "vs/editor/contrib/dnd/dnd": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModel", "vs/editor/contrib/dnd/dragAndDropCommand", "vs/css!vs/editor/contrib/dnd/dnd"], "vs/editor/contrib/find/findController": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/find/findModel", "vs/editor/contrib/find/findOptionsWidget", "vs/editor/contrib/find/findState", "vs/editor/contrib/find/findWidget", "vs/nls!vs/editor/contrib/find/findController", "vs/platform/actions/common/actions", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/platform/theme/common/themeService"], "vs/editor/contrib/folding/folding": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/types", "vs/editor/browser/core/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/contrib/folding/foldingModel", "vs/editor/contrib/folding/hiddenRangeModel", "vs/editor/contrib/folding/indentRange<PERSON>rovider", "vs/editor/contrib/folding/intializingRangeProvider", "vs/nls!vs/editor/contrib/folding/folding", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/folding/foldingDecorations", "vs/editor/contrib/folding/syntaxRangeProvider", "vs/css!vs/editor/contrib/folding/folding"], "vs/editor/contrib/fontZoom/fontZoom": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorZoom", "vs/nls!vs/editor/contrib/fontZoom/fontZoom"], "vs/editor/contrib/format/formatActions": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes", "vs/editor/common/services/editorWorkerService", "vs/editor/contrib/format/format", "vs/editor/contrib/format/formattingEdit", "vs/nls!vs/editor/contrib/format/formatActions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/progress/common/progress"], "vs/editor/contrib/documentSymbols/documentSymbols": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/services/modelService", "vs/editor/common/services/resolverService", "vs/editor/contrib/documentSymbols/outlineModel", "vs/platform/commands/common/commands"], "vs/editor/contrib/inlineCompletions/ghostTextController": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/controller/cursorColumns", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/inlineCompletions/consts", "vs/editor/contrib/inlineCompletions/ghostTextModel", "vs/editor/contrib/inlineCompletions/ghostTextWidget", "vs/nls!vs/editor/contrib/inlineCompletions/ghostTextController", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry"], "vs/editor/contrib/gotoSymbol/goToCommands": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/keyCodes", "vs/base/common/platform", "vs/base/common/types", "vs/base/common/uri", "vs/editor/browser/core/editorState", "vs/editor/browser/editorBrowser", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes", "vs/editor/contrib/gotoSymbol/peek/referencesController", "vs/editor/contrib/gotoSymbol/referencesModel", "vs/editor/contrib/gotoSymbol/symbolNavigation", "vs/editor/contrib/message/messageController", "vs/editor/contrib/peekView/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/goToCommands", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/editor/contrib/gotoSymbol/goToSymbol"], "vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/types", "vs/editor/browser/core/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/common/services/modeService", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/link/clickLinkGesture", "vs/editor/contrib/peekView/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/gotoSymbol/goToCommands", "vs/editor/contrib/gotoSymbol/goToSymbol", "vs/css!vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition"], "vs/editor/contrib/gotoError/gotoError": ["require", "exports", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/gotoError/markerNavigationService", "vs/nls!vs/editor/contrib/gotoError/gotoError", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/iconRegistry", "vs/editor/contrib/gotoError/gotoErrorWidget"], "vs/editor/contrib/hover/hover": ["require", "exports", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/services/modeService", "vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition", "vs/editor/contrib/hover/modesContentHover", "vs/editor/contrib/hover/modesGlyphHover", "vs/nls!vs/editor/contrib/hover/hover", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/indentation/indentation": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/common/services/modelService", "vs/editor/contrib/indentation/indentUtils", "vs/nls!vs/editor/contrib/indentation/indentation", "vs/platform/quickinput/common/quickInput"], "vs/editor/contrib/inlayHints/inlayHintsController": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/types", "vs/base/common/uri", "vs/editor/browser/editorDom", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/common/modes/languageFeatureRegistry", "vs/editor/common/services/resolverService", "vs/platform/commands/common/commands", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/inPlaceReplace/inPlaceReplace": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/editor/browser/core/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/services/editorWorkerService", "vs/editor/common/view/editorColorRegistry", "vs/nls!vs/editor/contrib/inPlaceReplace/inPlaceReplace", "vs/platform/theme/common/themeService", "vs/editor/contrib/inPlaceReplace/inPlaceReplaceCommand"], "vs/editor/contrib/lineSelection/lineSelection": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/controller/cursorMoveCommands", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/lineSelection/lineSelection"], "vs/editor/contrib/linesOperations/linesOperations": ["require", "exports", "vs/base/common/keyCodes", "vs/editor/browser/controller/coreCommands", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/trimTrailingWhitespaceCommand", "vs/editor/common/controller/cursorTypeOperations", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/linesOperations/copyLinesCommand", "vs/editor/contrib/linesOperations/moveLinesCommand", "vs/editor/contrib/linesOperations/sortLinesCommand", "vs/nls!vs/editor/contrib/linesOperations/linesOperations", "vs/platform/actions/common/actions"], "vs/editor/contrib/linkedEditing/linkedEditing": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/modes", "vs/editor/common/modes/languageConfigurationRegistry", "vs/nls!vs/editor/contrib/linkedEditing/linkedEditing", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/links/links": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/common/model/textModel", "vs/editor/common/modes", "vs/editor/contrib/gotoSymbol/link/clickLinkGesture", "vs/editor/contrib/links/getLinks", "vs/nls!vs/editor/contrib/links/links", "vs/platform/notification/common/notification", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/links/links"], "vs/editor/contrib/multicursor/multicursor": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/controller/cursorMoveCommands", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/modes", "vs/editor/contrib/find/findController", "vs/nls!vs/editor/contrib/multicursor/multicursor", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/parameterHints/parameterHints": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes", "vs/editor/contrib/parameterHints/provideSignatureHelp", "vs/nls!vs/editor/contrib/parameterHints/parameterHints", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/parameterHints/parameterHintsWidget"], "vs/editor/contrib/rename/rename": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/browser/core/editorState", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes", "vs/editor/common/services/textResourceConfigurationService", "vs/editor/contrib/message/messageController", "vs/nls!vs/editor/contrib/rename/rename", "vs/platform/configuration/common/configurationRegistry", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/registry/common/platform", "vs/editor/contrib/rename/renameInputField"], "vs/editor/contrib/smartSelect/smartSelect": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes", "vs/editor/contrib/smartSelect/bracketSelections", "vs/editor/contrib/smartSelect/wordSelections", "vs/nls!vs/editor/contrib/smartSelect/smartSelect", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands"], "vs/editor/contrib/snippet/snippetController2": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/suggest/suggest", "vs/nls!vs/editor/contrib/snippet/snippetController2", "vs/platform/contextkey/common/contextkey", "vs/platform/log/common/log", "vs/editor/contrib/snippet/snippetSession"], "vs/editor/contrib/suggest/suggestController": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/keybindings", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/stopwatch", "vs/base/common/types", "vs/editor/browser/core/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/snippet/snippetController2", "vs/editor/contrib/snippet/snippetParser", "vs/editor/contrib/suggest/suggestMemory", "vs/editor/contrib/suggest/wordContextKey", "vs/nls!vs/editor/contrib/suggest/suggestController", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/log/common/log", "vs/editor/contrib/suggest/suggest", "vs/editor/contrib/suggest/suggestAlternatives", "vs/editor/contrib/suggest/suggestCommitCharacters", "vs/editor/contrib/suggest/suggestModel", "vs/editor/contrib/suggest/suggestOvertypingCapturer", "vs/editor/contrib/suggest/suggestWidget", "vs/platform/telemetry/common/telemetry", "vs/base/common/resources", "vs/base/common/hash"], "vs/editor/contrib/tokenization/tokenization": ["require", "exports", "vs/base/common/stopwatch", "vs/editor/browser/editorExtensions", "vs/nls!vs/editor/contrib/tokenization/tokenization"], "vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/editor/browser/editorExtensions", "vs/editor/common/config/commonEditorConfig", "vs/nls!vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode"], "vs/editor/contrib/unicodeHighlighter/unicodeHighlighter": ["require", "exports", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/modes/unicodeTextModelHighlighter", "vs/editor/common/services/editorWorkerService", "vs/editor/common/services/modeService", "vs/editor/common/viewModel/viewModelDecorations", "vs/editor/contrib/hover/markdownHoverParticipant", "vs/editor/contrib/unicodeHighlighter/bannerController", "vs/nls!vs/editor/contrib/unicodeHighlighter/unicodeHighlighter", "vs/platform/configuration/common/configuration", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/quickinput/common/quickInput", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/platform/workspace/common/workspaceTrust", "vs/css!vs/editor/contrib/unicodeHighlighter/unicodeHighlighter"], "vs/editor/contrib/unusualLineTerminators/unusualLineTerminators": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/nls!vs/editor/contrib/unusualLineTerminators/unusualLineTerminators", "vs/platform/dialogs/common/dialogs"], "vs/editor/contrib/viewportSemanticTokens/viewportSemanticTokens": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/modes", "vs/editor/common/services/getSemanticTokens", "vs/editor/common/services/modelService", "vs/editor/common/services/modelServiceImpl", "vs/editor/common/services/semanticTokensProviderStyling", "vs/platform/configuration/common/configuration", "vs/platform/theme/common/themeService"], "vs/editor/contrib/wordHighlighter/wordHighlighter": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/modes", "vs/nls!vs/editor/contrib/wordHighlighter/wordHighlighter", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/wordOperations/wordOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/config/editorOptions", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/controller/cursorWordOperations", "vs/editor/common/controller/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/modes/languageConfigurationRegistry", "vs/nls!vs/editor/contrib/wordOperations/wordOperations", "vs/platform/accessibility/common/accessibility", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys"], "vs/editor/contrib/wordPartOperations/wordPartOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/controller/cursorWordOperations", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/wordOperations/wordOperations", "vs/platform/commands/common/commands"], "vs/editor/common/standaloneStrings": ["require", "exports", "vs/nls!vs/editor/common/standaloneStrings"], "vs/base/browser/ui/codicons/codiconStyles": ["require", "exports", "vs/base/common/codicons", "vs/css!vs/base/browser/ui/codicons/codicon/codicon", "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers"], "vs/base/browser/dom": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/canIUse", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform"], "vs/base/browser/fastDomNode": ["require", "exports"], "vs/base/browser/formattedTextRenderer": ["require", "exports", "vs/base/browser/dom"], "vs/base/browser/ui/aria/aria": ["require", "exports", "vs/base/browser/dom", "vs/base/common/platform", "vs/css!vs/base/browser/ui/aria/aria"], "vs/base/browser/ui/widget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/touch", "vs/base/common/lifecycle"], "vs/editor/browser/editorExtensions": ["require", "exports", "vs/nls!vs/editor/browser/editorExtensions", "vs/base/common/uri", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/services/modelService", "vs/editor/common/services/resolverService", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/registry/common/platform", "vs/platform/telemetry/common/telemetry", "vs/base/common/types", "vs/platform/log/common/log"], "vs/editor/common/editorContextKeys": ["require", "exports", "vs/nls!vs/editor/common/editorContextKeys", "vs/platform/contextkey/common/contextkey"], "vs/platform/contextkey/common/contextkey": ["require", "exports", "vs/base/common/platform", "vs/base/common/strings", "vs/platform/instantiation/common/instantiation"], "vs/platform/instantiation/common/instantiation": ["require", "exports"], "vs/platform/keybinding/common/keybinding": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/opener/common/opener": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/platform/instantiation/common/instantiation"], "vs/platform/theme/common/colorRegistry": ["require", "exports", "vs/base/common/async", "vs/base/common/color", "vs/base/common/event", "vs/base/common/types", "vs/nls!vs/platform/theme/common/colorRegistry", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "vs/platform/theme/common/themeService": ["require", "exports", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/platform/registry/common/platform", "vs/platform/theme/common/theme"], "vs/css!vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp": [], "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard": [], "vs/base/common/color": ["require", "exports"], "vs/editor/common/modes": ["require", "exports", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/modes/languageFeatureRegistry", "vs/editor/common/modes/tokenizationRegistry", "vs/base/common/codicons"], "vs/editor/common/modes/nullMode": ["require", "exports", "vs/editor/common/core/token"], "vs/editor/common/services/modeService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/common/standaloneThemeService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/theme/common/theme": ["require", "exports"], "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens": [], "vs/platform/registry/common/platform": ["require", "exports", "vs/base/common/assert", "vs/base/common/types"], "vs/platform/quickinput/common/quickAccess": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/platform/registry/common/platform"], "vs/platform/quickinput/browser/helpQuickAccess": ["require", "exports", "vs/base/common/lifecycle", "vs/nls!vs/platform/quickinput/browser/helpQuickAccess", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform"], "vs/editor/contrib/quickAccess/gotoLineQuickAccess": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorBrowser", "vs/editor/contrib/quickAccess/editorNavigationQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/gotoLineQuickAccess"], "vs/editor/browser/services/codeEditorService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/quickinput/common/quickInput": ["require", "exports", "vs/platform/instantiation/common/instantiation", "vs/base/parts/quickinput/common/quickInput"], "vs/editor/contrib/quickAccess/gotoSymbolQuickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/fuzzyScorer", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/contrib/documentSymbols/outlineModel", "vs/editor/contrib/quickAccess/editorNavigationQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/gotoSymbolQuickAccess"], "vs/editor/contrib/symbolIcons/symbolIcons": ["require", "exports", "vs/base/common/codicons", "vs/nls!vs/editor/contrib/symbolIcons/symbolIcons", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/quickAccess/commandsQuickAccess": ["require", "exports", "vs/base/common/iconLabels", "vs/platform/quickinput/browser/commandsQuickAccess"], "vs/platform/commands/common/commands": ["require", "exports", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/types", "vs/platform/instantiation/common/instantiation"], "vs/platform/telemetry/common/telemetry": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/dialogs/common/dialogs": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/gotoSymbol/peek/referencesController": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/contrib/peekView/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesController", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/list/browser/listService", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/editor/contrib/gotoSymbol/referencesModel", "vs/editor/contrib/gotoSymbol/peek/referencesWidget"], "vs/platform/configuration/common/configuration": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/notification/common/notification": ["require", "exports", "vs/base/common/severity", "vs/platform/instantiation/common/instantiation"], "vs/platform/storage/common/storage": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/parts/storage/common/storage", "vs/platform/instantiation/common/instantiation"], "vs/base/common/linkedList": ["require", "exports"], "vs/base/common/functional": ["require", "exports"], "vs/base/common/iterator": ["require", "exports"], "vs/base/common/diff/diffChange": ["require", "exports"], "vs/base/common/hash": ["require", "exports", "vs/base/common/strings"], "vs/base/common/path": ["require", "exports", "vs/base/common/process"], "vs/editor/common/viewModel/prefixSumComputer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/uint"], "vs/editor/common/core/characterClassifier": ["require", "exports", "vs/base/common/uint"], "vs/base/common/cancellation": ["require", "exports", "vs/base/common/event"], "vs/base/common/keyCodes": ["require", "exports"], "vs/editor/common/core/selection": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/core/token": ["require", "exports"], "vs/editor/common/standalone/standaloneEnums": ["require", "exports"], "vs/editor/common/model/textModelSearch": ["require", "exports", "vs/base/common/strings", "vs/editor/common/controller/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model"], "vs/nls!vs/editor/common/config/editorOptions": [], "vs/base/common/arrays": ["require", "exports"], "vs/base/common/objects": ["require", "exports", "vs/base/common/types"], "===anonymous3===": ["vs/editor/common/config/editorOptions.nls", "vs/editor/common/config/editorOptions.nls.keys"], "vs/editor/common/config/editorOptions.nls": [], "vs/editor/common/config/editorOptions.nls.keys": [], "vs/editor/browser/services/openerService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/cancellation", "vs/base/common/linkedList", "vs/base/common/map", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/browser/services/codeEditorService", "vs/platform/commands/common/commands", "vs/platform/editor/common/editor", "vs/platform/opener/common/opener"], "vs/editor/common/config/fontInfo": ["require", "exports", "vs/base/common/platform", "vs/editor/common/config/editorZoom"], "vs/editor/common/editorCommon": ["require", "exports"], "vs/editor/common/model": ["require", "exports", "vs/base/common/objects"], "vs/editor/common/services/editorWorkerService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/services/resolverService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/services/webWorker": ["require", "exports", "vs/editor/common/services/editorWorkerServiceImpl", "vs/base/common/types"], "vs/editor/standalone/browser/colorizer": ["require", "exports", "vs/base/common/async", "vs/base/common/strings", "vs/editor/common/core/lineTokens", "vs/editor/common/modes", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel/viewModel", "vs/editor/standalone/common/monarch/monarchLexer"], "vs/editor/standalone/browser/simpleServices": ["require", "exports", "vs/base/common/strings", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/common/event", "vs/base/common/keybindings", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/severity", "vs/base/common/uri", "vs/editor/browser/editorBrowser", "vs/editor/browser/services/bulkEditService", "vs/editor/common/config/commonEditorConfig", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/services/modelService", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationModels", "vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/keybindingResolver", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/keybinding/common/resolvedKeybindingItem", "vs/platform/keybinding/common/usLayoutResolvedKeybinding", "vs/platform/notification/common/notification", "vs/platform/workspace/common/workspace", "vs/editor/common/standaloneStrings"], "vs/editor/standalone/browser/standaloneCodeEditor": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditorWidget", "vs/editor/common/editorAction", "vs/editor/common/services/editorWorkerService", "vs/editor/standalone/browser/simpleServices", "vs/editor/standalone/common/standaloneThemeService", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility", "vs/editor/common/standaloneStrings", "vs/platform/clipboard/common/clipboardService", "vs/platform/progress/common/progress", "vs/editor/common/services/modelService", "vs/editor/common/services/modeService", "vs/editor/standalone/browser/standaloneCodeServiceImpl", "vs/base/common/mime"], "vs/editor/standalone/browser/standaloneServices": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/common/services/editorWorkerService", "vs/editor/common/services/editorWorkerServiceImpl", "vs/editor/common/services/modeService", "vs/editor/common/services/modeServiceImpl", "vs/editor/common/services/modelService", "vs/editor/common/services/modelServiceImpl", "vs/editor/common/services/textResourceConfigurationService", "vs/editor/standalone/browser/simpleServices", "vs/editor/standalone/browser/standaloneCodeServiceImpl", "vs/editor/standalone/browser/standaloneThemeServiceImpl", "vs/editor/standalone/common/standaloneThemeService", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/browser/contextKeyService", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextMenuService", "vs/platform/contextview/browser/contextView", "vs/platform/contextview/browser/contextViewService", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/instantiationService", "vs/platform/instantiation/common/serviceCollection", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/list/browser/listService", "vs/platform/log/common/log", "vs/platform/markers/common/markerService", "vs/platform/markers/common/markers", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/storage/common/storage", "vs/platform/telemetry/common/telemetry", "vs/platform/theme/common/themeService", "vs/platform/workspace/common/workspace", "vs/platform/actions/common/menuService", "vs/editor/common/services/markersDecorationService", "vs/editor/common/services/markerDecorationsServiceImpl", "vs/platform/accessibility/common/accessibility", "vs/platform/layout/browser/layoutService", "vs/platform/instantiation/common/extensions", "vs/platform/accessibility/browser/accessibilityService", "vs/platform/clipboard/common/clipboardService", "vs/platform/clipboard/browser/clipboardService", "vs/platform/undoRedo/common/undoRedo", "vs/platform/undoRedo/common/undoRedoService", "vs/editor/standalone/browser/quickInput/standaloneQuickInputServiceImpl", "vs/platform/quickinput/common/quickInput", "vs/editor/common/modes/languageConfigurationRegistry", "vs/platform/workspace/common/workspaceTrust"], "vs/platform/contextview/browser/contextView": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/accessibility/common/accessibility": ["require", "exports", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/config/configuration": ["require", "exports", "vs/base/browser/browser", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/config/charWidthReader", "vs/editor/browser/config/elementSizeObserver", "vs/editor/common/config/commonEditorConfig", "vs/editor/common/config/editorOptions", "vs/editor/common/config/fontInfo"], "vs/platform/progress/common/progress": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/clipboard/common/clipboardService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/services/modelService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/standalone/browser/standalone-tokens": [], "vs/editor/common/modes/languageConfigurationRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/model/wordHelper", "vs/editor/common/modes/languageConfiguration", "vs/editor/common/modes/supports", "vs/editor/common/modes/supports/characterPair", "vs/editor/common/modes/supports/electricCharacter", "vs/editor/common/modes/supports/indentRules", "vs/editor/common/modes/supports/onEnter", "vs/editor/common/modes/supports/richEditBrackets", "vs/platform/instantiation/common/instantiation", "vs/platform/configuration/common/configuration", "vs/editor/common/services/modeService", "vs/platform/instantiation/common/extensions"], "vs/editor/common/modes/modesRegistry": ["require", "exports", "vs/nls!vs/editor/common/modes/modesRegistry", "vs/base/common/event", "vs/editor/common/modes/languageConfigurationRegistry", "vs/platform/registry/common/platform", "vs/base/common/mime", "vs/platform/configuration/common/configurationRegistry"], "vs/editor/standalone/common/monarch/monarchCompile": ["require", "exports", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon"], "vs/editor/standalone/common/monarch/monarchLexer": ["require", "exports", "vs/editor/common/core/token", "vs/editor/common/modes", "vs/editor/common/modes/nullMode", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon"], "vs/editor/browser/core/editorState": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/editor/browser/core/keybindingCancellation"], "vs/editor/browser/editorBrowser": ["require", "exports", "vs/editor/common/editor<PERSON><PERSON><PERSON>"], "vs/editor/contrib/format/formattingEdit": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/nls!vs/editor/contrib/format/format": [], "vs/platform/extensions/common/extensions": ["require", "exports"], "===anonymous4===": ["vs/editor/contrib/format/format.nls", "vs/editor/contrib/format/format.nls.keys"], "vs/editor/contrib/format/format.nls": [], "vs/editor/contrib/format/format.nls.keys": [], "vs/nls!vs/editor/browser/controller/coreCommands": [], "vs/base/browser/browser": ["require", "exports", "vs/base/common/event"], "vs/editor/common/controller/cursorColumnSelection": ["require", "exports", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/controller/cursorCommon": ["require", "exports", "vs/base/common/errors", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModel", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/common/controller/cursorColumns"], "vs/editor/common/controller/cursorDeleteOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/replaceCommand", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/controller/cursorMoveOperations", "vs/editor/common/core/range", "vs/editor/common/core/position"], "vs/editor/common/controller/cursorMoveCommands": ["require", "exports", "vs/base/common/types", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/controller/cursorMoveOperations", "vs/editor/common/controller/cursorWordOperations", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/controller/cursorTypeOperations": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/shiftCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/controller/wordCharacterClassifier", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/core/position", "vs/editor/common/modes/languageConfiguration", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/common/modes/supports"], "vs/platform/keybinding/common/keybindingsRegistry": ["require", "exports", "vs/base/common/keybindings", "vs/base/common/platform", "vs/platform/commands/common/commands", "vs/platform/registry/common/platform"], "===anonymous5===": ["vs/editor/browser/controller/coreCommands.nls", "vs/editor/browser/controller/coreCommands.nls.keys"], "vs/editor/browser/controller/coreCommands.nls": [], "vs/editor/browser/controller/coreCommands.nls.keys": [], "vs/nls!vs/editor/browser/widget/codeEditorWidget": [], "vs/base/common/network": ["require", "exports", "vs/base/common/platform", "vs/base/common/uri"], "vs/editor/browser/view/viewImpl": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/browser", "vs/editor/common/core/selection", "vs/base/browser/fastDomNode", "vs/base/common/errors", "vs/editor/browser/controller/pointerHandler", "vs/editor/browser/controller/text<PERSON><PERSON>Hand<PERSON>", "vs/editor/browser/view/viewController", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/browser/view/viewOverlays", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/contentWidgets/contentWidgets", "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/editor/browser/viewParts/decorations/decorations", "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/viewParts/lines/viewLines", "vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/editor/browser/viewParts/margin/margin", "vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/editor/browser/viewParts/minimap/minimap", "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler", "vs/editor/browser/viewParts/overviewRuler/overviewRuler", "vs/editor/browser/viewParts/rulers/rulers", "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/editor/browser/viewParts/selections/selections", "vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/editor/browser/viewParts/viewZones/viewZones", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/view/renderingContext", "vs/editor/common/view/viewContext", "vs/editor/common/viewLayout/viewLinesViewportData", "vs/editor/common/viewModel/viewEventHandler", "vs/platform/theme/common/themeService", "vs/editor/browser/controller/mouseTarget"], "vs/editor/browser/view/viewUserInputEvents": ["require", "exports", "vs/editor/browser/controller/mouseTarget"], "vs/editor/common/controller/cursor": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/controller/cursorCollection", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/controller/cursorDeleteOperations", "vs/editor/common/controller/cursorTypeOperations", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModelEvents", "vs/editor/common/view/viewEvents", "vs/base/common/lifecycle", "vs/editor/common/viewModel/viewModelEventDispatcher"], "vs/editor/common/editorAction": ["require", "exports"], "vs/editor/common/view/editorColorRegistry": ["require", "exports", "vs/nls!vs/editor/common/view/editorColorRegistry", "vs/base/common/color", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/common/viewModel/viewModelImpl": ["require", "exports", "vs/base/common/color", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model/textModelEvents", "vs/editor/common/modes", "vs/editor/common/modes/textToHtmlTokenizer", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/view/viewEvents", "vs/editor/common/viewLayout/viewLayout", "vs/editor/common/viewModel/viewModelLines", "vs/editor/common/viewModel/viewModel", "vs/editor/common/viewModel/viewModelDecorations", "vs/base/common/async", "vs/base/common/platform", "vs/editor/common/controller/cursor", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/viewModel/viewModelEventDispatcher", "vs/editor/common/modes/modesRegistry", "vs/base/common/arrays"], "vs/platform/instantiation/common/serviceCollection": ["require", "exports"], "vs/editor/common/viewModel/monospaceLineBreaksComputer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/characterClassifier", "vs/editor/common/model/textModelEvents", "vs/editor/common/viewModel/modelLineProjectionData"], "vs/editor/browser/view/domLineBreaksComputer": ["require", "exports", "vs/editor/common/core/stringBuilder", "vs/base/common/strings", "vs/editor/browser/config/configuration", "vs/editor/common/model/textModelEvents", "vs/editor/common/viewModel/modelLineProjectionData"], "vs/editor/common/controller/cursorWordOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/controller/cursorDeleteOperations", "vs/editor/common/controller/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/browser/services/markerDecorations": ["require", "exports", "vs/editor/common/services/markersDecorationService", "vs/editor/browser/editorExtensions"], "vs/css!vs/editor/browser/widget/media/editor": [], "===anonymous6===": ["vs/editor/browser/widget/codeEditorWidget.nls", "vs/editor/browser/widget/codeEditorWidget.nls.keys"], "vs/editor/browser/widget/codeEditorWidget.nls": [], "vs/editor/browser/widget/codeEditorWidget.nls.keys": [], "vs/nls!vs/editor/browser/widget/diffEditorWidget": [], "vs/base/browser/ui/sash/sash": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/touch", "vs/base/common/async", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/css!vs/base/browser/ui/sash/sash"], "vs/base/common/async": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/editor/browser/widget/diffReview": ["require", "exports", "vs/nls!vs/editor/browser/widget/diffReview", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/editor/browser/config/configuration", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/config/editorOptions", "vs/editor/common/core/lineTokens", "vs/editor/common/core/position", "vs/editor/common/view/editorColorRegistry", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel/viewModel", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/base/common/codicons", "vs/platform/theme/common/iconRegistry", "vs/editor/common/services/modeService", "vs/css!vs/editor/browser/widget/media/diffReview"], "vs/editor/common/core/stringBuilder": ["require", "exports", "vs/base/common/strings", "vs/base/common/platform", "vs/base/common/buffer"], "vs/editor/common/model/textModel": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model", "vs/editor/common/model/editStack", "vs/editor/common/model/indentationG<PERSON>ser", "vs/editor/common/model/intervalTree", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder", "vs/editor/common/model/textModelEvents", "vs/editor/common/model/textModelSearch", "vs/editor/common/model/textModelTokens", "vs/editor/common/model/wordHelper", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/common/modes/nullMode", "vs/editor/common/model/tokensStore", "vs/base/common/color", "vs/platform/undoRedo/common/undoRedo", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer", "vs/base/common/arrays", "vs/editor/common/model/bracketPairs/bracketPairsImpl", "vs/editor/common/model/bracketPairs/colorizedBracketPairsDecorationProvider", "vs/editor/common/controller/cursorColumns", "vs/editor/common/services/modeService"], "vs/editor/common/view/overviewZoneManager": ["require", "exports"], "vs/editor/common/viewLayout/lineDecorations": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/viewLayout/viewLineRenderer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/stringBuilder", "vs/editor/common/viewLayout/lineDecorations"], "vs/editor/common/viewModel/viewModel": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/editor/browser/widget/inlineDiffMargin": ["require", "exports", "vs/nls!vs/editor/browser/widget/inlineDiffMargin", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/base/common/codicons"], "vs/editor/browser/config/elementSizeObserver": ["require", "exports", "vs/base/common/lifecycle"], "vs/base/common/codicons": ["require", "exports", "vs/base/common/event"], "vs/base/browser/ui/mouseCursor/mouseCursor": ["require", "exports", "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor"], "vs/platform/theme/common/iconRegistry": ["require", "exports", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/event", "vs/nls!vs/platform/theme/common/iconRegistry", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/browser/widget/media/diffEditor": [], "===anonymous7===": ["vs/editor/browser/widget/diffEditorWidget.nls", "vs/editor/browser/widget/diffEditorWidget.nls.keys"], "vs/editor/browser/widget/diffEditorWidget.nls": [], "vs/editor/browser/widget/diffEditorWidget.nls.keys": [], "vs/base/common/assert": ["require", "exports"], "vs/base/common/htmlContent": ["require", "exports", "vs/base/common/errors", "vs/base/common/iconLabels"], "vs/nls!vs/editor/contrib/anchorSelect/anchorSelect": [], "vs/css!vs/editor/contrib/anchorSelect/anchorSelect": [], "===anonymous8===": ["vs/editor/contrib/anchorSelect/anchorSelect.nls", "vs/editor/contrib/anchorSelect/anchorSelect.nls.keys"], "vs/editor/contrib/anchorSelect/anchorSelect.nls": [], "vs/editor/contrib/anchorSelect/anchorSelect.nls.keys": [], "vs/nls!vs/editor/contrib/bracketMatching/bracketMatching": [], "vs/platform/actions/common/actions": ["require", "exports", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/contrib/bracketMatching/bracketMatching": [], "===anonymous9===": ["vs/editor/contrib/bracketMatching/bracketMatching.nls", "vs/editor/contrib/bracketMatching/bracketMatching.nls.keys"], "vs/editor/contrib/bracketMatching/bracketMatching.nls": [], "vs/editor/contrib/bracketMatching/bracketMatching.nls.keys": [], "vs/editor/contrib/caretOperations/moveCaretCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/nls!vs/editor/contrib/caretOperations/caretOperations": [], "===anonymous10===": ["vs/editor/contrib/caretOperations/caretOperations.nls", "vs/editor/contrib/caretOperations/caretOperations.nls.keys"], "vs/editor/contrib/caretOperations/caretOperations.nls": [], "vs/editor/contrib/caretOperations/caretOperations.nls.keys": [], "vs/editor/common/commands/replaceCommand": ["require", "exports", "vs/editor/common/core/selection"], "vs/editor/common/controller/cursorMoveOperations": ["require", "exports", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/common/strings", "vs/editor/common/controller/cursorAtomicMoveOperations"], "vs/nls!vs/editor/contrib/caretOperations/transpose": [], "===anonymous11===": ["vs/editor/contrib/caretOperations/transpose.nls", "vs/editor/contrib/caretOperations/transpose.nls.keys"], "vs/editor/contrib/caretOperations/transpose.nls": [], "vs/editor/contrib/caretOperations/transpose.nls.keys": [], "vs/editor/browser/controller/textAreaInput": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/strings", "vs/editor/browser/controller/textAreaState", "vs/editor/common/core/selection"], "vs/nls!vs/editor/contrib/clipboard/clipboard": [], "===anonymous12===": ["vs/editor/contrib/clipboard/clipboard.nls", "vs/editor/contrib/clipboard/clipboard.nls.keys"], "vs/editor/contrib/clipboard/clipboard.nls": [], "vs/editor/contrib/clipboard/clipboard.nls.keys": [], "vs/editor/contrib/codeAction/codeActionCommands": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/bulkEditService", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/codeAction/codeAction", "vs/editor/contrib/codeAction/codeActionUi", "vs/editor/contrib/message/messageController", "vs/nls!vs/editor/contrib/codeAction/codeActionCommands", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/markers/common/markers", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/codeAction/codeActionModel", "vs/editor/contrib/codeAction/types"], "vs/editor/common/modes/languageFeatureRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/hash", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/numbers", "vs/editor/common/modes/languageSelector", "vs/editor/common/services/modelService"], "vs/editor/contrib/codelens/codelens": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/modes", "vs/editor/common/services/modelService", "vs/platform/commands/common/commands"], "vs/editor/contrib/codelens/codeLensCache": ["require", "exports", "vs/base/common/async", "vs/base/common/functional", "vs/base/common/map", "vs/editor/common/core/range", "vs/editor/contrib/codelens/codelens", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage"], "vs/editor/contrib/codelens/codelensWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/codelens/codelensWidget"], "vs/nls!vs/editor/contrib/codelens/codelensController": [], "===anonymous13===": ["vs/editor/contrib/codelens/codelensController.nls", "vs/editor/contrib/codelens/codelensController.nls.keys"], "vs/editor/contrib/codelens/codelensController.nls": [], "vs/editor/contrib/codelens/codelensController.nls.keys": [], "vs/editor/contrib/colorPicker/colorDetector": ["require", "exports", "vs/base/common/async", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorDom", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/modes", "vs/editor/contrib/colorPicker/color", "vs/platform/configuration/common/configuration"], "vs/editor/contrib/comment/blockCommentCommand": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/modes/languageConfigurationRegistry"], "vs/editor/contrib/comment/lineCommentCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/contrib/comment/blockCommentCommand"], "vs/nls!vs/editor/contrib/comment/comment": [], "===anonymous14===": ["vs/editor/contrib/comment/comment.nls", "vs/editor/contrib/comment/comment.nls.keys"], "vs/editor/contrib/comment/comment.nls": [], "vs/editor/contrib/comment/comment.nls.keys": [], "vs/base/browser/ui/actionbar/actionViewItems": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/types", "vs/nls!vs/base/browser/ui/actionbar/actionViewItems", "vs/css!vs/base/browser/ui/actionbar/actionbar"], "vs/base/common/actions": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/base/common/actions"], "vs/nls!vs/editor/contrib/contextmenu/contextmenu": [], "===anonymous15===": ["vs/editor/contrib/contextmenu/contextmenu.nls", "vs/editor/contrib/contextmenu/contextmenu.nls.keys"], "vs/editor/contrib/contextmenu/contextmenu.nls": [], "vs/editor/contrib/contextmenu/contextmenu.nls.keys": [], "vs/nls!vs/editor/contrib/cursorUndo/cursorUndo": [], "===anonymous16===": ["vs/editor/contrib/cursorUndo/cursorUndo.nls", "vs/editor/contrib/cursorUndo/cursorUndo.nls.keys"], "vs/editor/contrib/cursorUndo/cursorUndo.nls": [], "vs/editor/contrib/cursorUndo/cursorUndo.nls.keys": [], "vs/editor/contrib/dnd/dragAndDropCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/css!vs/editor/contrib/dnd/dnd": [], "vs/editor/contrib/find/findModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/common/commands/replaceCommand", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModelSearch", "vs/editor/contrib/find/findDecorations", "vs/editor/contrib/find/replaceAllCommand", "vs/editor/contrib/find/replacePattern", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/find/findOptionsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInputCheckboxes", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/editor/contrib/find/findModel", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/find/findState": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/contrib/find/findModel"], "vs/editor/contrib/find/findWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/checkbox/checkbox", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/find/findModel", "vs/nls!vs/editor/contrib/find/findWidget", "vs/platform/browser/contextScopedHistoryWidget", "vs/platform/browser/historyWidgetKeybindingHint", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/find/findWidget"], "vs/nls!vs/editor/contrib/find/findController": [], "===anonymous17===": ["vs/editor/contrib/find/findController.nls", "vs/editor/contrib/find/findController.nls.keys"], "vs/editor/contrib/find/findController.nls": [], "vs/editor/contrib/find/findController.nls.keys": [], "vs/editor/contrib/folding/foldingModel": ["require", "exports", "vs/base/common/event", "vs/editor/contrib/folding/foldingRanges"], "vs/editor/contrib/folding/hiddenRangeModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/editor/common/core/range", "vs/editor/common/model/tokensStore"], "vs/editor/contrib/folding/indentRangeProvider": ["require", "exports", "vs/editor/common/model/textModel", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/contrib/folding/foldingRanges"], "vs/editor/contrib/folding/intializingRangeProvider": ["require", "exports", "vs/editor/contrib/folding/syntaxRangeProvider"], "vs/nls!vs/editor/contrib/folding/folding": [], "vs/editor/contrib/folding/foldingDecorations": ["require", "exports", "vs/base/common/codicons", "vs/editor/common/model/textModel", "vs/nls!vs/editor/contrib/folding/foldingDecorations", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/folding/syntaxRangeProvider": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/contrib/folding/foldingRanges"], "vs/css!vs/editor/contrib/folding/folding": [], "===anonymous18===": ["vs/editor/contrib/folding/folding.nls", "vs/editor/contrib/folding/folding.nls.keys"], "vs/editor/contrib/folding/folding.nls": [], "vs/editor/contrib/folding/folding.nls.keys": [], "vs/editor/common/config/editorZoom": ["require", "exports", "vs/base/common/event"], "vs/nls!vs/editor/contrib/fontZoom/fontZoom": [], "===anonymous19===": ["vs/editor/contrib/fontZoom/fontZoom.nls", "vs/editor/contrib/fontZoom/fontZoom.nls.keys"], "vs/editor/contrib/fontZoom/fontZoom.nls": [], "vs/editor/contrib/fontZoom/fontZoom.nls.keys": [], "vs/nls!vs/editor/contrib/format/formatActions": [], "===anonymous20===": ["vs/editor/contrib/format/formatActions.nls", "vs/editor/contrib/format/formatActions.nls.keys"], "vs/editor/contrib/format/formatActions.nls": [], "vs/editor/contrib/format/formatActions.nls.keys": [], "vs/editor/contrib/documentSymbols/outlineModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/base/common/map", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/common/modes/languageFeatureRegistry"], "vs/editor/common/controller/cursorColumns": ["require", "exports", "vs/base/common/strings"], "vs/editor/contrib/inlineCompletions/consts": ["require", "exports"], "vs/editor/contrib/inlineCompletions/ghostTextModel": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/modes", "vs/editor/contrib/inlineCompletions/inlineCompletionsModel", "vs/editor/contrib/inlineCompletions/suggestWidgetPreviewModel", "vs/editor/contrib/inlineCompletions/utils", "vs/platform/commands/common/commands"], "vs/editor/contrib/inlineCompletions/ghostTextWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/config/configuration", "vs/editor/common/config/editorOptions", "vs/editor/common/core/lineTokens", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/stringBuilder", "vs/editor/common/services/modeService", "vs/editor/common/view/editorColorRegistry", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/inlineCompletions/ghostText"], "vs/nls!vs/editor/contrib/inlineCompletions/ghostTextController": [], "===anonymous21===": ["vs/editor/contrib/inlineCompletions/ghostTextController.nls", "vs/editor/contrib/inlineCompletions/ghostTextController.nls.keys"], "vs/editor/contrib/inlineCompletions/ghostTextController.nls": [], "vs/editor/contrib/inlineCompletions/ghostTextController.nls.keys": [], "vs/editor/browser/widget/embeddedCodeEditorWidget": ["require", "exports", "vs/base/common/objects", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility"], "vs/editor/contrib/gotoSymbol/referencesModel": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/core/range", "vs/nls!vs/editor/contrib/gotoSymbol/referencesModel"], "vs/editor/contrib/gotoSymbol/symbolNavigation": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/range", "vs/nls!vs/editor/contrib/gotoSymbol/symbolNavigation", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/notification/common/notification"], "vs/editor/contrib/message/messageController": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/nls!vs/editor/contrib/message/messageController", "vs/platform/contextkey/common/contextkey", "vs/css!vs/editor/contrib/message/messageController"], "vs/editor/contrib/peekView/peekView": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/color", "vs/base/common/event", "vs/base/common/objects", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/zoneWidget/zoneWidget", "vs/nls!vs/editor/contrib/peekView/peekView", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/colorRegistry", "vs/css!vs/editor/contrib/peekView/media/peekViewWidget"], "vs/nls!vs/editor/contrib/gotoSymbol/goToCommands": [], "vs/editor/contrib/gotoSymbol/goToSymbol": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/modes", "vs/editor/contrib/gotoSymbol/referencesModel"], "===anonymous22===": ["vs/editor/contrib/gotoSymbol/goToCommands.nls", "vs/editor/contrib/gotoSymbol/goToCommands.nls.keys"], "vs/editor/contrib/gotoSymbol/goToCommands.nls": [], "vs/editor/contrib/gotoSymbol/goToCommands.nls.keys": [], "vs/editor/contrib/gotoSymbol/link/clickLinkGesture": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/nls!vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition": [], "vs/css!vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition": [], "===anonymous23===": ["vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.nls", "vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.nls.keys"], "vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.nls": [], "vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition.nls.keys": [], "vs/editor/contrib/gotoError/markerNavigationService": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/common/core/range", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/markers/common/markers", "vs/platform/configuration/common/configuration"], "vs/nls!vs/editor/contrib/gotoError/gotoError": [], "vs/editor/contrib/gotoError/gotoErrorWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/event", "vs/base/common/labels", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/peekView/peekView", "vs/nls!vs/editor/contrib/gotoError/gotoErrorWidget", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/label/common/label", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/platform/severityIcon/common/severityIcon", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/gotoError/media/gotoErrorWidget"], "===anonymous24===": ["vs/editor/contrib/gotoError/gotoError.nls", "vs/editor/contrib/gotoError/gotoError.nls.keys"], "vs/editor/contrib/gotoError/gotoError.nls": [], "vs/editor/contrib/gotoError/gotoError.nls.keys": [], "vs/editor/contrib/hover/modesContentHover": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/hover/hoverWidget", "vs/base/browser/ui/widget", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/modes", "vs/editor/contrib/hover/colorHoverParticipant", "vs/editor/contrib/hover/hoverOperation", "vs/editor/contrib/hover/hoverTypes", "vs/editor/contrib/hover/markdownHoverParticipant", "vs/editor/contrib/hover/markerHoverParticipant", "vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/editor/contrib/suggest/suggest", "vs/editor/contrib/unicodeHighlighter/unicodeHighlighter", "vs/base/common/async"], "vs/editor/contrib/hover/modesGlyphHover": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/core/markdownRenderer", "vs/editor/contrib/hover/hoverOperation", "vs/base/browser/ui/widget", "vs/platform/opener/common/opener", "vs/base/browser/ui/hover/hoverWidget"], "vs/nls!vs/editor/contrib/hover/hover": [], "===anonymous25===": ["vs/editor/contrib/hover/hover.nls", "vs/editor/contrib/hover/hover.nls.keys"], "vs/editor/contrib/hover/hover.nls": [], "vs/editor/contrib/hover/hover.nls.keys": [], "vs/editor/common/commands/shiftCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/modes/languageConfigurationRegistry"], "vs/editor/common/core/editOperation": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/contrib/indentation/indentUtils": ["require", "exports"], "vs/nls!vs/editor/contrib/indentation/indentation": [], "===anonymous26===": ["vs/editor/contrib/indentation/indentation.nls", "vs/editor/contrib/indentation/indentation.nls.keys"], "vs/editor/contrib/indentation/indentation.nls": [], "vs/editor/contrib/indentation/indentation.nls.keys": [], "vs/base/common/map": ["require", "exports", "vs/base/common/strings"], "vs/editor/browser/editorDom": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/lifecycle", "vs/platform/theme/common/colorRegistry"], "vs/nls!vs/editor/contrib/inPlaceReplace/inPlaceReplace": [], "vs/editor/contrib/inPlaceReplace/inPlaceReplaceCommand": ["require", "exports", "vs/editor/common/core/selection"], "===anonymous27===": ["vs/editor/contrib/inPlaceReplace/inPlaceReplace.nls", "vs/editor/contrib/inPlaceReplace/inPlaceReplace.nls.keys"], "vs/editor/contrib/inPlaceReplace/inPlaceReplace.nls": [], "vs/editor/contrib/inPlaceReplace/inPlaceReplace.nls.keys": [], "vs/nls!vs/editor/contrib/lineSelection/lineSelection": [], "===anonymous28===": ["vs/editor/contrib/lineSelection/lineSelection.nls", "vs/editor/contrib/lineSelection/lineSelection.nls.keys"], "vs/editor/contrib/lineSelection/lineSelection.nls": [], "vs/editor/contrib/lineSelection/lineSelection.nls.keys": [], "vs/editor/common/commands/trimTrailingWhitespaceCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/editor/contrib/linesOperations/copyLinesCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/contrib/linesOperations/moveLinesCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/modes/languageConfiguration", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/contrib/indentation/indentUtils"], "vs/editor/contrib/linesOperations/sortLinesCommand": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/nls!vs/editor/contrib/linesOperations/linesOperations": [], "===anonymous29===": ["vs/editor/contrib/linesOperations/linesOperations.nls", "vs/editor/contrib/linesOperations/linesOperations.nls.keys"], "vs/editor/contrib/linesOperations/linesOperations.nls": [], "vs/editor/contrib/linesOperations/linesOperations.nls.keys": [], "vs/nls!vs/editor/contrib/linkedEditing/linkedEditing": [], "===anonymous30===": ["vs/editor/contrib/linkedEditing/linkedEditing.nls", "vs/editor/contrib/linkedEditing/linkedEditing.nls.keys"], "vs/editor/contrib/linkedEditing/linkedEditing.nls": [], "vs/editor/contrib/linkedEditing/linkedEditing.nls.keys": [], "vs/base/common/resources": ["require", "exports", "vs/base/common/extpath", "vs/base/common/network", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings", "vs/base/common/uri"], "vs/editor/contrib/links/getLinks": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/common/services/modelService", "vs/platform/commands/common/commands"], "vs/nls!vs/editor/contrib/links/links": [], "vs/css!vs/editor/contrib/links/links": [], "===anonymous31===": ["vs/editor/contrib/links/links.nls", "vs/editor/contrib/links/links.nls.keys"], "vs/editor/contrib/links/links.nls": [], "vs/editor/contrib/links/links.nls.keys": [], "vs/nls!vs/editor/contrib/multicursor/multicursor": [], "===anonymous32===": ["vs/editor/contrib/multicursor/multicursor.nls", "vs/editor/contrib/multicursor/multicursor.nls.keys"], "vs/editor/contrib/multicursor/multicursor.nls": [], "vs/editor/contrib/multicursor/multicursor.nls.keys": [], "vs/editor/contrib/parameterHints/provideSignatureHelp": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/modes", "vs/editor/common/services/resolverService", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey"], "vs/nls!vs/editor/contrib/parameterHints/parameterHints": [], "vs/editor/contrib/parameterHints/parameterHintsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/types", "vs/editor/browser/core/markdownRenderer", "vs/editor/common/services/modeService", "vs/editor/contrib/parameterHints/parameterHintsModel", "vs/editor/contrib/parameterHints/provideSignatureHelp", "vs/nls!vs/editor/contrib/parameterHints/parameterHintsWidget", "vs/platform/contextkey/common/contextkey", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/theme", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/parameterHints/parameterHints"], "===anonymous33===": ["vs/editor/contrib/parameterHints/parameterHints.nls", "vs/editor/contrib/parameterHints/parameterHints.nls.keys"], "vs/editor/contrib/parameterHints/parameterHints.nls": [], "vs/editor/contrib/parameterHints/parameterHints.nls.keys": [], "vs/editor/browser/services/bulkEditService": ["require", "exports", "vs/platform/instantiation/common/instantiation", "vs/base/common/uri", "vs/base/common/types"], "vs/editor/common/services/textResourceConfigurationService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/nls!vs/editor/contrib/rename/rename": [], "vs/platform/configuration/common/configurationRegistry": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/types", "vs/nls!vs/platform/configuration/common/configurationRegistry", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "vs/platform/log/common/log": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/rename/renameInputField": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/nls!vs/editor/contrib/rename/renameInputField", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybinding", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/rename/renameInputField"], "===anonymous34===": ["vs/editor/contrib/rename/rename.nls", "vs/editor/contrib/rename/rename.nls.keys"], "vs/editor/contrib/rename/rename.nls": [], "vs/editor/contrib/rename/rename.nls.keys": [], "vs/editor/contrib/smartSelect/bracketSelections": ["require", "exports", "vs/base/common/linkedList", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/contrib/smartSelect/wordSelections": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/nls!vs/editor/contrib/smartSelect/smartSelect": [], "===anonymous35===": ["vs/editor/contrib/smartSelect/smartSelect.nls", "vs/editor/contrib/smartSelect/smartSelect.nls.keys"], "vs/editor/contrib/smartSelect/smartSelect.nls": [], "vs/editor/contrib/smartSelect/smartSelect.nls.keys": [], "vs/editor/contrib/suggest/suggest": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/common/services/resolverService", "vs/editor/contrib/snippet/snippetParser", "vs/nls!vs/editor/contrib/suggest/suggest", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey"], "vs/nls!vs/editor/contrib/snippet/snippetController2": [], "vs/editor/contrib/snippet/snippetSession": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModel", "vs/platform/label/common/label", "vs/platform/workspace/common/workspace", "vs/editor/contrib/snippet/snippetParser", "vs/editor/contrib/snippet/snippetVariables", "vs/css!vs/editor/contrib/snippet/snippetSession"], "===anonymous36===": ["vs/editor/contrib/snippet/snippetController2.nls", "vs/editor/contrib/snippet/snippetController2.nls.keys"], "vs/editor/contrib/snippet/snippetController2.nls": [], "vs/editor/contrib/snippet/snippetController2.nls.keys": [], "vs/base/common/keybindings": ["require", "exports", "vs/base/common/errors"], "vs/editor/contrib/snippet/snippetParser": ["require", "exports"], "vs/editor/contrib/suggest/suggestMemory": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/map", "vs/editor/common/modes", "vs/platform/configuration/common/configuration", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage"], "vs/editor/contrib/suggest/wordContextKey": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/nls!vs/editor/contrib/suggest/suggestController": [], "vs/editor/contrib/suggest/suggestAlternatives": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/suggest/suggestCommitCharacters": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/editor/common/core/characterClassifier"], "vs/editor/contrib/suggest/suggestModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/selection", "vs/editor/common/modes", "vs/editor/common/services/editorWorkerService", "vs/editor/contrib/snippet/snippetController2", "vs/editor/contrib/suggest/wordDistance", "vs/platform/clipboard/common/clipboardService", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/log/common/log", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/suggest/completionModel", "vs/editor/contrib/suggest/suggest"], "vs/editor/contrib/suggest/suggestOvertypingCapturer": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/contrib/suggest/suggestWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listWidget", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/strings", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/suggest/suggestWidgetStatus", "vs/nls!vs/editor/contrib/suggest/suggestWidget", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/styler", "vs/platform/theme/common/themeService", "vs/editor/contrib/suggest/resizable", "vs/editor/contrib/suggest/suggest", "vs/editor/contrib/suggest/suggestWidgetDetails", "vs/editor/contrib/suggest/suggestWidget<PERSON><PERSON><PERSON>", "vs/base/browser/ui/codicons/codiconStyles", "vs/css!vs/editor/contrib/suggest/media/suggest", "vs/editor/contrib/symbolIcons/symbolIcons"], "===anonymous37===": ["vs/editor/contrib/suggest/suggestController.nls", "vs/editor/contrib/suggest/suggestController.nls.keys"], "vs/editor/contrib/suggest/suggestController.nls": [], "vs/editor/contrib/suggest/suggestController.nls.keys": [], "vs/nls!vs/editor/contrib/tokenization/tokenization": [], "===anonymous38===": ["vs/editor/contrib/tokenization/tokenization.nls", "vs/editor/contrib/tokenization/tokenization.nls.keys"], "vs/editor/contrib/tokenization/tokenization.nls": [], "vs/editor/contrib/tokenization/tokenization.nls.keys": [], "vs/editor/common/config/commonEditorConfig": ["require", "exports", "vs/nls!vs/editor/common/config/commonEditorConfig", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/arrays", "vs/editor/common/config/editorOptions", "vs/editor/common/config/editorZoom", "vs/editor/common/config/fontInfo", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform", "vs/base/common/collections"], "vs/nls!vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode": [], "===anonymous39===": ["vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.nls", "vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.nls.keys"], "vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.nls": [], "vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode.nls.keys": [], "vs/editor/common/viewModel/viewModelDecorations": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/viewModel/viewModel", "vs/editor/common/config/editorOptions"], "vs/editor/contrib/hover/markdownHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/core/markdownRenderer", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/common/services/modeService", "vs/editor/contrib/hover/getHover", "vs/nls!vs/editor/contrib/hover/markdownHoverParticipant", "vs/platform/configuration/common/configuration", "vs/platform/opener/common/opener"], "vs/editor/contrib/unicodeHighlighter/bannerController": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/editor/browser/core/markdownRenderer", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/browser/link", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/unicodeHighlighter/bannerController"], "vs/nls!vs/editor/contrib/unicodeHighlighter/unicodeHighlighter": [], "vs/platform/workspace/common/workspaceTrust": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/contrib/unicodeHighlighter/unicodeHighlighter": [], "===anonymous40===": ["vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.nls", "vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.nls.keys"], "vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.nls": [], "vs/editor/contrib/unicodeHighlighter/unicodeHighlighter.nls.keys": [], "vs/nls!vs/editor/contrib/unusualLineTerminators/unusualLineTerminators": [], "===anonymous41===": ["vs/editor/contrib/unusualLineTerminators/unusualLineTerminators.nls", "vs/editor/contrib/unusualLineTerminators/unusualLineTerminators.nls.keys"], "vs/editor/contrib/unusualLineTerminators/unusualLineTerminators.nls": [], "vs/editor/contrib/unusualLineTerminators/unusualLineTerminators.nls.keys": [], "vs/editor/common/services/getSemanticTokens": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/uri", "vs/editor/common/modes", "vs/editor/common/services/modelService", "vs/platform/commands/common/commands", "vs/base/common/types", "vs/editor/common/services/semanticTokensDto", "vs/editor/common/core/range"], "vs/editor/common/services/modelServiceImpl": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/errors", "vs/editor/common/config/editorOptions", "vs/editor/common/model/textModel", "vs/editor/common/modes", "vs/editor/common/modes/modesRegistry", "vs/editor/common/services/modeService", "vs/editor/common/services/textResourceConfigurationService", "vs/platform/configuration/common/configuration", "vs/base/common/async", "vs/base/common/cancellation", "vs/platform/theme/common/themeService", "vs/platform/log/common/log", "vs/platform/undoRedo/common/undoRedo", "vs/base/common/hash", "vs/editor/common/model/editStack", "vs/base/common/network", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/common/services/getSemanticTokens", "vs/base/common/objects", "vs/editor/common/modes/languageConfigurationRegistry"], "vs/editor/common/services/semanticTokensProviderStyling": ["require", "exports", "vs/editor/common/modes", "vs/platform/theme/common/themeService", "vs/platform/log/common/log", "vs/editor/common/model/tokensStore", "vs/editor/common/services/modeService"], "vs/nls!vs/editor/contrib/wordHighlighter/wordHighlighter": [], "===anonymous42===": ["vs/editor/contrib/wordHighlighter/wordHighlighter.nls", "vs/editor/contrib/wordHighlighter/wordHighlighter.nls.keys"], "vs/editor/contrib/wordHighlighter/wordHighlighter.nls": [], "vs/editor/contrib/wordHighlighter/wordHighlighter.nls.keys": [], "vs/editor/common/controller/wordCharacterClassifier": ["require", "exports", "vs/editor/common/core/characterClassifier"], "vs/nls!vs/editor/contrib/wordOperations/wordOperations": [], "vs/platform/contextkey/common/contextkeys": ["require", "exports", "vs/base/common/platform", "vs/nls!vs/platform/contextkey/common/contextkeys", "vs/platform/contextkey/common/contextkey"], "===anonymous43===": ["vs/editor/contrib/wordOperations/wordOperations.nls", "vs/editor/contrib/wordOperations/wordOperations.nls.keys"], "vs/editor/contrib/wordOperations/wordOperations.nls": [], "vs/editor/contrib/wordOperations/wordOperations.nls.keys": [], "vs/nls!vs/editor/common/standaloneStrings": [], "===anonymous44===": ["vs/editor/common/standaloneStrings.nls", "vs/editor/common/standaloneStrings.nls.keys"], "vs/editor/common/standaloneStrings.nls": [], "vs/editor/common/standaloneStrings.nls.keys": [], "vs/css!vs/base/browser/ui/codicons/codicon/codicon": [], "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers": [], "vs/base/browser/canIUse": ["require", "exports", "vs/base/browser/browser", "vs/base/common/platform"], "vs/base/browser/keyboardEvent": ["require", "exports", "vs/base/browser/browser", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/base/common/platform"], "vs/base/browser/mouseEvent": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/iframe", "vs/base/common/platform"], "vs/css!vs/base/browser/ui/aria/aria": [], "vs/base/browser/touch": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/decorators", "vs/base/common/lifecycle"], "vs/nls!vs/editor/browser/editorExtensions": [], "===anonymous45===": ["vs/editor/browser/editorExtensions.nls", "vs/editor/browser/editorExtensions.nls.keys"], "vs/editor/browser/editorExtensions.nls": [], "vs/editor/browser/editorExtensions.nls.keys": [], "vs/nls!vs/editor/common/editorContextKeys": [], "===anonymous46===": ["vs/editor/common/editorContextKeys.nls", "vs/editor/common/editorContextKeys.nls.keys"], "vs/editor/common/editorContextKeys.nls": [], "vs/editor/common/editorContextKeys.nls.keys": [], "vs/editor/common/modes/tokenizationRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/nls!vs/platform/quickinput/browser/helpQuickAccess": [], "===anonymous47===": ["vs/platform/quickinput/browser/helpQuickAccess.nls", "vs/platform/quickinput/browser/helpQuickAccess.nls.keys"], "vs/platform/quickinput/browser/helpQuickAccess.nls": [], "vs/platform/quickinput/browser/helpQuickAccess.nls.keys": [], "vs/editor/contrib/quickAccess/editorNavigationQuickAccess": ["require", "exports", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/base/common/types", "vs/editor/browser/editorBrowser", "vs/editor/common/model", "vs/editor/common/view/editorColorRegistry", "vs/platform/theme/common/themeService"], "vs/nls!vs/editor/contrib/quickAccess/gotoLineQuickAccess": [], "===anonymous48===": ["vs/editor/contrib/quickAccess/gotoLineQuickAccess.nls", "vs/editor/contrib/quickAccess/gotoLineQuickAccess.nls.keys"], "vs/editor/contrib/quickAccess/gotoLineQuickAccess.nls": [], "vs/editor/contrib/quickAccess/gotoLineQuickAccess.nls.keys": [], "vs/base/parts/quickinput/common/quickInput": ["require", "exports"], "vs/base/common/iconLabels": ["require", "exports", "vs/base/common/codicons", "vs/base/common/filters", "vs/base/common/strings"], "vs/platform/quickinput/browser/commandsQuickAccess": ["require", "exports", "vs/base/common/errorMessage", "vs/base/common/errors", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/severity", "vs/base/common/types", "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/quickinput/browser/pickerQuickAccess", "vs/platform/storage/common/storage", "vs/platform/telemetry/common/telemetry"], "vs/nls!vs/platform/theme/common/colorRegistry": [], "vs/platform/jsonschemas/common/jsonContributionRegistry": ["require", "exports", "vs/base/common/event", "vs/platform/registry/common/platform"], "===anonymous49===": ["vs/platform/theme/common/colorRegistry.nls", "vs/platform/theme/common/colorRegistry.nls.keys"], "vs/platform/theme/common/colorRegistry.nls": [], "vs/platform/theme/common/colorRegistry.nls.keys": [], "vs/base/common/severity": ["require", "exports", "vs/base/common/strings"], "vs/base/parts/storage/common/storage": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types"], "vs/nls!vs/editor/contrib/symbolIcons/symbolIcons": [], "===anonymous50===": ["vs/editor/contrib/symbolIcons/symbolIcons.nls", "vs/editor/contrib/symbolIcons/symbolIcons.nls.keys"], "vs/editor/contrib/symbolIcons/symbolIcons.nls": [], "vs/editor/contrib/symbolIcons/symbolIcons.nls.keys": [], "vs/base/common/fuzzyScorer": ["require", "exports", "vs/base/common/filters", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/nls!vs/editor/contrib/quickAccess/gotoSymbolQuickAccess": [], "===anonymous51===": ["vs/editor/contrib/quickAccess/gotoSymbolQuickAccess.nls", "vs/editor/contrib/quickAccess/gotoSymbolQuickAccess.nls.keys"], "vs/editor/contrib/quickAccess/gotoSymbolQuickAccess.nls": [], "vs/editor/contrib/quickAccess/gotoSymbolQuickAccess.nls.keys": [], "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesController": [], "vs/platform/list/browser/listService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listPaging", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/table/tableWidget", "vs/base/browser/ui/tree/asyncDataTree", "vs/base/browser/ui/tree/dataTree", "vs/base/browser/ui/tree/objectTree", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/platform/list/browser/listService", "vs/platform/accessibility/common/accessibility", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationRegistry", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/registry/common/platform", "vs/platform/theme/common/styler", "vs/platform/theme/common/themeService"], "vs/editor/contrib/gotoSymbol/peek/referencesWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/splitview/splitview", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/resources", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/common/services/modeService", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/peek/referencesTree", "vs/editor/contrib/peekView/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesWidget", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/list/browser/listService", "vs/platform/theme/common/themeService", "vs/platform/undoRedo/common/undoRedo", "vs/editor/contrib/gotoSymbol/referencesModel", "vs/css!vs/editor/contrib/gotoSymbol/peek/referencesWidget"], "===anonymous52===": ["vs/editor/contrib/gotoSymbol/peek/referencesController.nls", "vs/editor/contrib/gotoSymbol/peek/referencesController.nls.keys"], "vs/editor/contrib/gotoSymbol/peek/referencesController.nls": [], "vs/editor/contrib/gotoSymbol/peek/referencesController.nls.keys": [], "vs/base/common/process": ["require", "exports", "vs/base/common/platform"], "vs/base/common/uint": ["require", "exports"], "vs/base/common/marshalling": ["require", "exports", "vs/base/common/buffer", "vs/base/common/uri"], "vs/platform/editor/common/editor": ["require", "exports"], "vs/editor/common/services/editorWorkerServiceImpl": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/worker/simpleWorker", "vs/base/worker/defaultWorkerFactory", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/common/services/editorSimpleWorker", "vs/editor/common/services/modelService", "vs/editor/common/services/textResourceConfigurationService", "vs/base/common/strings", "vs/base/common/arrays", "vs/platform/log/common/log", "vs/base/common/stopwatch", "vs/base/common/errors"], "vs/editor/common/core/lineTokens": ["require", "exports", "vs/editor/common/modes"], "vs/platform/configuration/common/configurationModels": ["require", "exports", "vs/base/common/arrays", "vs/base/common/map", "vs/base/common/objects", "vs/base/common/types", "vs/base/common/uri", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/keybinding/common/abstractKeybindingService": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/platform/keybinding/common/abstractKeybindingService"], "vs/platform/keybinding/common/keybindingResolver": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/platform/keybinding/common/resolvedKeybindingItem": ["require", "exports"], "vs/platform/keybinding/common/usLayoutResolvedKeybinding": ["require", "exports", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/platform/keybinding/common/baseResolvedKeybinding", "vs/platform/keybinding/common/resolvedKeybindingItem"], "vs/platform/workspace/common/workspace": ["require", "exports", "vs/base/common/map", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/browser/standaloneCodeServiceImpl": ["require", "exports", "vs/base/browser/dom", "vs/base/common/network", "vs/editor/browser/services/codeEditorServiceImpl", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/themeService"], "vs/base/common/mime": ["require", "exports", "vs/base/common/glob", "vs/base/common/network", "vs/base/common/path", "vs/base/common/resources", "vs/base/common/strings"], "vs/editor/common/services/modeServiceImpl": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/modes/nullMode", "vs/editor/common/services/languagesRegistry", "vs/base/common/arrays"], "vs/editor/standalone/browser/standaloneThemeServiceImpl": ["require", "exports", "vs/base/browser/dom", "vs/base/common/color", "vs/base/common/event", "vs/editor/common/modes", "vs/editor/common/modes/supports/tokenization", "vs/editor/standalone/common/themes", "vs/platform/registry/common/platform", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/base/common/lifecycle", "vs/platform/theme/common/theme", "vs/platform/theme/browser/iconsStyleSheet"], "vs/platform/contextkey/browser/contextKeyService": ["require", "exports", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/map", "vs/nls!vs/platform/contextkey/browser/contextKeyService", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingResolver"], "vs/platform/contextview/browser/contextMenuService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/telemetry/common/telemetry", "vs/platform/theme/common/themeService", "vs/platform/contextview/browser/contextMenuHandler", "vs/platform/contextview/browser/contextView"], "vs/platform/contextview/browser/contextViewService": ["require", "exports", "vs/base/browser/ui/contextview/contextview", "vs/base/common/lifecycle", "vs/platform/layout/browser/layoutService"], "vs/platform/instantiation/common/instantiationService": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/graph", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection"], "vs/platform/label/common/label": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/markers/common/markerService": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/map", "vs/base/common/network", "vs/base/common/uri", "vs/platform/markers/common/markers"], "vs/platform/markers/common/markers": ["require", "exports", "vs/base/common/severity", "vs/nls!vs/platform/markers/common/markers", "vs/platform/instantiation/common/instantiation"], "vs/platform/actions/common/menuService": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey"], "vs/editor/common/services/markersDecorationService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/services/markerDecorationsServiceImpl": ["require", "exports", "vs/platform/markers/common/markers", "vs/base/common/lifecycle", "vs/editor/common/model", "vs/platform/theme/common/themeService", "vs/editor/common/view/editorColorRegistry", "vs/editor/common/services/modelService", "vs/editor/common/core/range", "vs/base/common/network", "vs/base/common/event", "vs/platform/theme/common/colorRegistry", "vs/base/common/map"], "vs/platform/layout/browser/layoutService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/instantiation/common/extensions": ["require", "exports", "vs/platform/instantiation/common/descriptors"], "vs/platform/accessibility/browser/accessibilityService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/accessibility/common/accessibility", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey"], "vs/platform/clipboard/browser/clipboardService": ["require", "exports", "vs/base/browser/dom"], "vs/platform/undoRedo/common/undoRedo": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/undoRedo/common/undoRedoService": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/severity", "vs/nls!vs/platform/undoRedo/common/undoRedoService", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/extensions", "vs/platform/notification/common/notification", "vs/platform/undoRedo/common/undoRedo"], "vs/editor/standalone/browser/quickInput/standaloneQuickInputServiceImpl": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/platform/theme/common/themeService", "vs/base/common/cancellation", "vs/platform/instantiation/common/instantiation", "vs/platform/contextkey/common/contextkey", "vs/platform/accessibility/common/accessibility", "vs/platform/layout/browser/layoutService", "vs/editor/browser/services/codeEditorService", "vs/platform/quickinput/browser/quickInput", "vs/base/common/functional", "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput"], "vs/editor/browser/config/charWidthReader": ["require", "exports", "vs/base/browser/browser", "vs/editor/common/config/editorOptions"], "vs/editor/common/modes/languageConfiguration": ["require", "exports"], "vs/editor/common/modes/supports": ["require", "exports"], "vs/editor/common/modes/supports/characterPair": ["require", "exports", "vs/editor/common/modes/languageConfiguration"], "vs/editor/common/modes/supports/electricCharacter": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/modes/supports", "vs/editor/common/modes/supports/richEditBrackets"], "vs/editor/common/modes/supports/indentRules": ["require", "exports"], "vs/editor/common/modes/supports/onEnter": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/modes/languageConfiguration"], "vs/editor/common/modes/supports/richEditBrackets": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/stringBuilder", "vs/editor/common/core/range"], "vs/nls!vs/editor/common/modes/modesRegistry": [], "===anonymous53===": ["vs/editor/common/modes/modesRegistry.nls", "vs/editor/common/modes/modesRegistry.nls.keys"], "vs/editor/common/modes/modesRegistry.nls": [], "vs/editor/common/modes/modesRegistry.nls.keys": [], "vs/editor/standalone/common/monarch/monarchCommon": ["require", "exports"], "vs/editor/browser/core/keybindingCancellation": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/platform/contextkey/common/contextkey", "vs/base/common/cancellation", "vs/base/common/linkedList", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/extensions", "vs/nls!vs/editor/browser/core/keybindingCancellation"], "vs/editor/common/commands/surroundSelectionCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/browser/controller/pointerHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/common/platform", "vs/base/browser/touch", "vs/base/common/lifecycle", "vs/editor/browser/controller/mouseHandler", "vs/editor/browser/editorDom", "vs/base/browser/canIUse", "vs/editor/browser/controller/textAreaInput"], "vs/editor/browser/controller/textAreaHandler": ["require", "exports", "vs/nls!vs/editor/browser/controller/textAreaHandler", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/config/configuration", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/controller/textAreaState", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/viewParts/margin/margin", "vs/editor/common/config/editorOptions", "vs/editor/common/controller/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/css!vs/editor/browser/controller/textAreaHandler"], "vs/editor/browser/view/viewController": ["require", "exports", "vs/editor/browser/controller/coreCommands", "vs/editor/common/core/position", "vs/base/common/platform"], "vs/editor/browser/view/viewOverlays": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/config/configuration", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart"], "vs/editor/browser/view/viewPart": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/common/viewModel/viewEventHandler"], "vs/editor/browser/viewParts/contentWidgets/contentWidgets": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart"], "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/view/editorColorRegistry", "vs/base/common/arrays", "vs/platform/theme/common/themeService", "vs/editor/common/core/selection", "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight"], "vs/editor/browser/viewParts/decorations/decorations": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/range", "vs/editor/common/view/renderingContext", "vs/css!vs/editor/browser/viewParts/decorations/decorations"], "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/editor/browser/view/viewPart", "vs/platform/theme/common/themeService", "vs/platform/theme/common/colorRegistry"], "vs/editor/browser/viewParts/glyphMargin/glyphMargin": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin"], "vs/editor/browser/viewParts/indentGuides/indentGuides": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/view/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/core/position", "vs/editor/common/model", "vs/base/common/arrays", "vs/editor/common/model/textModel", "vs/base/common/types", "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides"], "vs/editor/browser/viewParts/lineNumbers/lineNumbers": ["require", "exports", "vs/base/common/platform", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/position", "vs/editor/common/view/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers"], "vs/editor/browser/viewParts/lines/viewLines": ["require", "exports", "vs/base/common/platform", "vs/base/common/async", "vs/editor/browser/config/configuration", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/view/renderingContext", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/css!vs/editor/browser/viewParts/lines/viewLines"], "vs/editor/browser/viewParts/linesDecorations/linesDecorations": ["require", "exports", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations"], "vs/editor/browser/viewParts/margin/margin": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart"], "vs/editor/browser/viewParts/marginDecorations/marginDecorations": ["require", "exports", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations"], "vs/editor/browser/viewParts/minimap/minimap": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/globalMouseMoveMonitor", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart", "vs/editor/common/config/editorOptions", "vs/editor/common/core/range", "vs/editor/common/core/rgba", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel/viewModel", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/core/selection", "vs/base/browser/touch", "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory", "vs/editor/common/model", "vs/base/common/functional", "vs/css!vs/editor/browser/viewParts/minimap/minimap"], "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets"], "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/color", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position", "vs/editor/common/modes", "vs/editor/common/view/editorColorRegistry", "vs/editor/common/viewModel/viewModel"], "vs/editor/browser/viewParts/overviewRuler/overviewRuler": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/common/view/overviewZoneManager", "vs/editor/common/viewModel/viewEventHandler"], "vs/editor/browser/viewParts/rulers/rulers": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/editor/common/view/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/rulers/rulers"], "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration"], "vs/editor/browser/viewParts/selections/selections": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/selections/selections"], "vs/editor/browser/viewParts/viewCursors/viewCursors": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/async", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/viewCursors/viewCursor", "vs/editor/common/config/editorOptions", "vs/editor/common/view/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors"], "vs/editor/browser/viewParts/viewZones/viewZones": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/errors", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position"], "vs/editor/common/view/renderingContext": ["require", "exports"], "vs/editor/common/view/viewContext": ["require", "exports"], "vs/editor/common/viewLayout/viewLinesViewportData": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/common/viewModel/viewEventHandler": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/browser/controller/mouseTarget": ["require", "exports", "vs/editor/browser/editorDom", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/base/browser/dom", "vs/editor/common/controller/cursorAtomicMoveOperations"], "vs/editor/common/controller/cursorCollection": ["require", "exports", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/controller/oneCursor", "vs/editor/common/core/selection"], "vs/editor/common/model/textModelEvents": ["require", "exports"], "vs/editor/common/view/viewEvents": ["require", "exports"], "vs/editor/common/viewModel/viewModelEventDispatcher": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/nls!vs/editor/common/view/editorColorRegistry": [], "===anonymous54===": ["vs/editor/common/view/editorColorRegistry.nls", "vs/editor/common/view/editorColorRegistry.nls.keys"], "vs/editor/common/view/editorColorRegistry.nls": [], "vs/editor/common/view/editorColorRegistry.nls.keys": [], "vs/editor/common/modes/textToHtmlTokenizer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/lineTokens", "vs/editor/common/modes/nullMode"], "vs/editor/common/viewModel/minimapTokensColorTracker": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/rgba", "vs/editor/common/modes"], "vs/editor/common/viewLayout/viewLayout": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/scrollable", "vs/editor/common/viewLayout/linesLayout", "vs/editor/common/viewModel/viewModel", "vs/editor/common/viewModel/viewModelEventDispatcher"], "vs/editor/common/viewModel/viewModelLines": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/model/textModelEvents", "vs/editor/common/view/viewEvents", "vs/editor/common/viewModel/modelLineProjection", "vs/editor/common/viewModel/prefixSumComputer", "vs/editor/common/viewModel/viewModel"], "vs/editor/common/viewModel/modelLineProjectionData": ["require", "exports", "vs/editor/common/core/position"], "vs/base/browser/event": ["require", "exports", "vs/base/common/event"], "vs/base/common/decorators": ["require", "exports"], "vs/css!vs/base/browser/ui/sash/sash": [], "vs/nls!vs/editor/browser/widget/diffReview": [], "vs/base/browser/ui/actionbar/actionbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/common/actions", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types", "vs/css!vs/base/browser/ui/actionbar/actionbar"], "vs/base/browser/ui/scrollbar/scrollableElement": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/horizontalScrollbar", "vs/base/browser/ui/scrollbar/verticalScrollbar", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/scrollable", "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars"], "vs/css!vs/editor/browser/widget/media/diffReview": [], "===anonymous55===": ["vs/editor/browser/widget/diffReview.nls", "vs/editor/browser/widget/diffReview.nls.keys"], "vs/editor/browser/widget/diffReview.nls": [], "vs/editor/browser/widget/diffReview.nls.keys": [], "vs/base/common/buffer": ["require", "exports"], "vs/editor/common/model/editStack": ["require", "exports", "vs/nls!vs/editor/common/model/editStack", "vs/base/common/errors", "vs/editor/common/core/selection", "vs/base/common/uri", "vs/editor/common/model/textChange", "vs/base/common/buffer", "vs/base/common/resources"], "vs/editor/common/model/indentationGuesser": ["require", "exports"], "vs/editor/common/model/intervalTree": ["require", "exports"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder": ["require", "exports", "vs/base/common/strings", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer"], "vs/editor/common/model/textModelTokens": ["require", "exports", "vs/base/common/arrays", "vs/base/common/errors", "vs/editor/common/core/lineTokens", "vs/editor/common/core/position", "vs/editor/common/modes", "vs/editor/common/modes/nullMode", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/editor/common/model/tokensStore", "vs/base/common/platform"], "vs/editor/common/model/tokensStore": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/lineTokens", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/modes"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer": ["require", "exports", "vs/base/common/event", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/tokensStore", "vs/editor/common/model/textChange", "vs/base/common/lifecycle"], "vs/editor/common/model/bracketPairs/bracketPairsImpl": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/model/bracketPairs/bracketPairsTree/bracketPairsTree", "vs/editor/common/modes/supports", "vs/editor/common/modes/supports/richEditBrackets"], "vs/editor/common/model/bracketPairs/colorizedBracketPairsDecorationProvider": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/common/view/editorColorRegistry", "vs/platform/theme/common/themeService"], "vs/nls!vs/editor/browser/widget/inlineDiffMargin": [], "===anonymous56===": ["vs/editor/browser/widget/inlineDiffMargin.nls", "vs/editor/browser/widget/inlineDiffMargin.nls.keys"], "vs/editor/browser/widget/inlineDiffMargin.nls": [], "vs/editor/browser/widget/inlineDiffMargin.nls.keys": [], "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor": [], "vs/nls!vs/platform/theme/common/iconRegistry": [], "===anonymous57===": ["vs/platform/theme/common/iconRegistry.nls", "vs/platform/theme/common/iconRegistry.nls.keys"], "vs/platform/theme/common/iconRegistry.nls": [], "vs/platform/theme/common/iconRegistry.nls.keys": [], "vs/editor/common/controller/cursorAtomicMoveOperations": ["require", "exports", "vs/editor/common/controller/cursor<PERSON>ommon"], "vs/editor/browser/controller/textAreaState": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/base/common/lazy": ["require", "exports"], "vs/editor/contrib/codeAction/codeAction": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/browser/core/editorState", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/modes", "vs/editor/common/services/modelService", "vs/platform/commands/common/commands", "vs/platform/progress/common/progress", "vs/editor/contrib/codeAction/types"], "vs/editor/contrib/codeAction/codeActionUi": ["require", "exports", "vs/base/common/errors", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/editor/contrib/message/messageController", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/codeAction/codeActionMenu", "vs/editor/contrib/codeAction/lightBulbWidget"], "vs/nls!vs/editor/contrib/codeAction/codeActionCommands": [], "vs/editor/contrib/codeAction/codeActionModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/platform/contextkey/common/contextkey", "vs/platform/progress/common/progress", "vs/editor/contrib/codeAction/codeAction"], "vs/editor/contrib/codeAction/types": ["require", "exports"], "===anonymous58===": ["vs/editor/contrib/codeAction/codeActionCommands.nls", "vs/editor/contrib/codeAction/codeActionCommands.nls.keys"], "vs/editor/contrib/codeAction/codeActionCommands.nls": [], "vs/editor/contrib/codeAction/codeActionCommands.nls.keys": [], "vs/base/common/numbers": ["require", "exports"], "vs/editor/common/modes/languageSelector": ["require", "exports", "vs/base/common/glob", "vs/base/common/path"], "vs/base/browser/ui/iconLabel/iconLabels": ["require", "exports", "vs/base/browser/dom", "vs/base/common/codicons"], "vs/css!vs/editor/contrib/codelens/codelensWidget": [], "vs/editor/contrib/colorPicker/color": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/common/services/modelService", "vs/platform/commands/common/commands"], "vs/base/browser/dnd": ["require", "exports", "vs/base/common/mime"], "vs/nls!vs/base/browser/ui/actionbar/actionViewItems": [], "vs/css!vs/base/browser/ui/actionbar/actionbar": [], "===anonymous59===": ["vs/base/browser/ui/actionbar/actionViewItems.nls", "vs/base/browser/ui/actionbar/actionViewItems.nls.keys"], "vs/base/browser/ui/actionbar/actionViewItems.nls": [], "vs/base/browser/ui/actionbar/actionViewItems.nls.keys": [], "vs/nls!vs/base/common/actions": [], "===anonymous60===": ["vs/base/common/actions.nls", "vs/base/common/actions.nls.keys"], "vs/base/common/actions.nls": [], "vs/base/common/actions.nls.keys": [], "vs/editor/contrib/find/findDecorations": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/find/replaceAllCommand": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/contrib/find/replacePattern": ["require", "exports", "vs/base/common/search"], "vs/base/browser/ui/findinput/findInputCheckboxes": ["require", "exports", "vs/base/browser/ui/checkbox/checkbox", "vs/base/common/codicons", "vs/nls!vs/base/browser/ui/findinput/findInputCheckboxes"], "vs/base/browser/ui/checkbox/checkbox": ["require", "exports", "vs/base/browser/ui/widget", "vs/base/common/codicons", "vs/base/common/color", "vs/base/common/event", "vs/css!vs/base/browser/ui/checkbox/checkbox"], "vs/nls!vs/editor/contrib/find/findWidget": [], "vs/platform/browser/contextScopedHistoryWidget": ["require", "exports", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/findinput/replaceInput", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry", "vs/editor/contrib/suggest/suggest"], "vs/platform/browser/historyWidgetKeybindingHint": ["require", "exports"], "vs/css!vs/editor/contrib/find/findWidget": [], "===anonymous61===": ["vs/editor/contrib/find/findWidget.nls", "vs/editor/contrib/find/findWidget.nls.keys"], "vs/editor/contrib/find/findWidget.nls": [], "vs/editor/contrib/find/findWidget.nls.keys": [], "vs/editor/contrib/folding/foldingRanges": ["require", "exports"], "vs/nls!vs/editor/contrib/folding/foldingDecorations": [], "===anonymous62===": ["vs/editor/contrib/folding/foldingDecorations.nls", "vs/editor/contrib/folding/foldingDecorations.nls.keys"], "vs/editor/contrib/folding/foldingDecorations.nls": [], "vs/editor/contrib/folding/foldingDecorations.nls.keys": [], "vs/editor/contrib/inlineCompletions/inlineCompletionsModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/controller/coreCommands", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/modes", "vs/editor/contrib/inlineCompletions/ghostText", "vs/platform/commands/common/commands", "vs/editor/contrib/inlineCompletions/consts", "vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText"], "vs/editor/contrib/inlineCompletions/suggestWidgetPreviewModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/common/modes", "vs/editor/contrib/inlineCompletions/ghostText", "vs/editor/contrib/inlineCompletions/inlineCompletionsModel", "vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText", "vs/editor/contrib/inlineCompletions/suggestWidgetInlineCompletionProvider"], "vs/editor/contrib/inlineCompletions/utils": ["require", "exports"], "vs/css!vs/editor/contrib/inlineCompletions/ghostText": [], "vs/base/common/idGenerator": ["require", "exports"], "vs/nls!vs/editor/contrib/gotoSymbol/referencesModel": [], "===anonymous63===": ["vs/editor/contrib/gotoSymbol/referencesModel.nls", "vs/editor/contrib/gotoSymbol/referencesModel.nls.keys"], "vs/editor/contrib/gotoSymbol/referencesModel.nls": [], "vs/editor/contrib/gotoSymbol/referencesModel.nls.keys": [], "vs/nls!vs/editor/contrib/gotoSymbol/symbolNavigation": [], "===anonymous64===": ["vs/editor/contrib/gotoSymbol/symbolNavigation.nls", "vs/editor/contrib/gotoSymbol/symbolNavigation.nls.keys"], "vs/editor/contrib/gotoSymbol/symbolNavigation.nls": [], "vs/editor/contrib/gotoSymbol/symbolNavigation.nls.keys": [], "vs/nls!vs/editor/contrib/message/messageController": [], "vs/css!vs/editor/contrib/message/messageController": [], "===anonymous65===": ["vs/editor/contrib/message/messageController.nls", "vs/editor/contrib/message/messageController.nls.keys"], "vs/editor/contrib/message/messageController.nls": [], "vs/editor/contrib/message/messageController.nls.keys": [], "vs/editor/contrib/zoneWidget/zoneWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/common/color", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/zoneWidget/zoneWidget"], "vs/nls!vs/editor/contrib/peekView/peekView": [], "vs/platform/actions/browser/menuEntryActionViewItem": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/common/actions", "vs/base/common/keybindingLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/platform/theme/common/themeService", "vs/css!vs/platform/actions/browser/menuEntryActionViewItem"], "vs/css!vs/editor/contrib/peekView/media/peekViewWidget": [], "===anonymous66===": ["vs/editor/contrib/peekView/peekView.nls", "vs/editor/contrib/peekView/peekView.nls.keys"], "vs/editor/contrib/peekView/peekView.nls": [], "vs/editor/contrib/peekView/peekView.nls.keys": [], "vs/base/common/labels": ["require", "exports", "vs/base/common/extpath", "vs/base/common/network", "vs/base/common/platform", "vs/base/common/resources", "vs/base/common/uri"], "vs/nls!vs/editor/contrib/gotoError/gotoErrorWidget": [], "vs/platform/severityIcon/common/severityIcon": ["require", "exports", "vs/base/common/codicons", "vs/base/common/severity", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/contrib/gotoError/media/gotoErrorWidget": [], "===anonymous67===": ["vs/editor/contrib/gotoError/gotoErrorWidget.nls", "vs/editor/contrib/gotoError/gotoErrorWidget.nls.keys"], "vs/editor/contrib/gotoError/gotoErrorWidget.nls": [], "vs/editor/contrib/gotoError/gotoErrorWidget.nls.keys": [], "vs/base/browser/ui/hover/hoverWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/hover/hover"], "vs/editor/contrib/hover/colorHoverParticipant": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/contrib/colorPicker/color", "vs/editor/contrib/colorPicker/colorDetector", "vs/editor/contrib/colorPicker/colorPickerModel", "vs/editor/contrib/colorPicker/colorPickerWidget", "vs/platform/theme/common/themeService"], "vs/editor/contrib/hover/hoverOperation": ["require", "exports", "vs/base/common/async", "vs/base/common/errors"], "vs/editor/contrib/hover/hoverTypes": ["require", "exports"], "vs/editor/contrib/hover/markerHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/core/range", "vs/editor/common/services/markersDecorationService", "vs/editor/contrib/codeAction/codeAction", "vs/editor/contrib/codeAction/codeActionCommands", "vs/editor/contrib/codeAction/types", "vs/editor/contrib/gotoError/gotoError", "vs/nls!vs/editor/contrib/hover/markerHoverParticipant", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/platform/progress/common/progress", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/core/markdownRenderer", "vs/editor/common/core/range", "vs/editor/common/services/modeService", "vs/editor/contrib/hover/hoverTypes", "vs/editor/contrib/inlineCompletions/ghostTextController", "vs/nls!vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant", "vs/platform/accessibility/common/accessibility", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/opener/common/opener"], "vs/editor/browser/core/markdownRenderer": ["require", "exports", "vs/base/browser/markdownRenderer", "vs/platform/opener/common/opener", "vs/editor/common/services/modeService", "vs/base/common/errors", "vs/editor/common/modes/textToHtmlTokenizer", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/modes", "vs/editor/browser/config/configuration"], "vs/base/browser/globalMouseMoveMonitor": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/iframe", "vs/base/browser/mouseEvent", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/base/common/extpath": ["require", "exports", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/editor/contrib/parameterHints/parameterHintsModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/characterClassifier", "vs/editor/common/modes", "vs/editor/contrib/parameterHints/provideSignatureHelp"], "vs/nls!vs/editor/contrib/parameterHints/parameterHintsWidget": [], "vs/css!vs/editor/contrib/parameterHints/parameterHints": [], "===anonymous68===": ["vs/editor/contrib/parameterHints/parameterHintsWidget.nls", "vs/editor/contrib/parameterHints/parameterHintsWidget.nls.keys"], "vs/editor/contrib/parameterHints/parameterHintsWidget.nls": [], "vs/editor/contrib/parameterHints/parameterHintsWidget.nls.keys": [], "vs/nls!vs/platform/configuration/common/configurationRegistry": [], "===anonymous69===": ["vs/platform/configuration/common/configurationRegistry.nls", "vs/platform/configuration/common/configurationRegistry.nls.keys"], "vs/platform/configuration/common/configurationRegistry.nls": [], "vs/platform/configuration/common/configurationRegistry.nls.keys": [], "vs/nls!vs/editor/contrib/rename/renameInputField": [], "vs/css!vs/editor/contrib/rename/renameInputField": [], "===anonymous70===": ["vs/editor/contrib/rename/renameInputField.nls", "vs/editor/contrib/rename/renameInputField.nls.keys"], "vs/editor/contrib/rename/renameInputField.nls": [], "vs/editor/contrib/rename/renameInputField.nls.keys": [], "vs/base/common/filters": ["require", "exports", "vs/base/common/map", "vs/base/common/strings"], "vs/nls!vs/editor/contrib/suggest/suggest": [], "===anonymous71===": ["vs/editor/contrib/suggest/suggest.nls", "vs/editor/contrib/suggest/suggest.nls.keys"], "vs/editor/contrib/suggest/suggest.nls": [], "vs/editor/contrib/suggest/suggest.nls.keys": [], "vs/editor/contrib/snippet/snippetVariables": ["require", "exports", "vs/base/common/labels", "vs/base/common/path", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uuid", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/contrib/snippet/snippetParser", "vs/nls!vs/editor/contrib/snippet/snippetVariables", "vs/platform/workspaces/common/workspaces"], "vs/css!vs/editor/contrib/snippet/snippetSession": [], "vs/base/browser/ui/list/listWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/list/splice", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/color", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/objects", "vs/base/common/platform", "vs/base/common/types", "vs/base/browser/ui/list/list", "vs/base/browser/ui/list/listView", "vs/css!vs/base/browser/ui/list/list"], "vs/editor/contrib/suggest/suggestWidgetStatus": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/lifecycle", "vs/editor/contrib/suggest/suggest", "vs/nls!vs/editor/contrib/suggest/suggestWidgetStatus", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/nls!vs/editor/contrib/suggest/suggestWidget": [], "vs/platform/theme/common/styler": ["require", "exports", "vs/platform/theme/common/colorRegistry"], "vs/editor/contrib/suggest/resizable": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/contrib/suggest/suggestWidgetDetails": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/browser/core/markdownRenderer", "vs/editor/common/config/editorOptions", "vs/editor/contrib/suggest/resizable", "vs/nls!vs/editor/contrib/suggest/suggestWidgetDetails", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/suggest/suggestWidgetRenderer": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/common/arrays", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/common/config/editorOptions", "vs/editor/common/modes", "vs/editor/common/services/getIconClasses", "vs/editor/common/services/modelService", "vs/editor/common/services/modeService", "vs/nls!vs/editor/contrib/suggest/suggestWidgetR<PERSON>er", "vs/platform/files/common/files", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/suggest/suggestWidgetDetails"], "vs/css!vs/editor/contrib/suggest/media/suggest": [], "===anonymous72===": ["vs/editor/contrib/suggest/suggestWidget.nls", "vs/editor/contrib/suggest/suggestWidget.nls.keys"], "vs/editor/contrib/suggest/suggestWidget.nls": [], "vs/editor/contrib/suggest/suggestWidget.nls.keys": [], "vs/editor/contrib/suggest/wordDistance": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/range", "vs/editor/contrib/smartSelect/bracketSelections"], "vs/editor/contrib/suggest/completionModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/filters", "vs/base/common/strings"], "vs/nls!vs/editor/common/config/commonEditorConfig": [], "vs/base/common/collections": ["require", "exports"], "===anonymous73===": ["vs/editor/common/config/commonEditorConfig.nls", "vs/editor/common/config/commonEditorConfig.nls.keys"], "vs/editor/common/config/commonEditorConfig.nls": [], "vs/editor/common/config/commonEditorConfig.nls.keys": [], "vs/editor/contrib/hover/getHover": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/modes"], "vs/nls!vs/editor/contrib/hover/markdownHoverParticipant": [], "===anonymous74===": ["vs/editor/contrib/hover/markdownHoverParticipant.nls", "vs/editor/contrib/hover/markdownHoverParticipant.nls.keys"], "vs/editor/contrib/hover/markdownHoverParticipant.nls": [], "vs/editor/contrib/hover/markdownHoverParticipant.nls.keys": [], "vs/platform/opener/browser/link": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/contrib/unicodeHighlighter/bannerController": [], "vs/editor/common/services/semanticTokensDto": ["require", "exports", "vs/base/common/buffer", "vs/base/common/platform"], "vs/nls!vs/platform/contextkey/common/contextkeys": [], "===anonymous75===": ["vs/platform/contextkey/common/contextkeys.nls", "vs/platform/contextkey/common/contextkeys.nls.keys"], "vs/platform/contextkey/common/contextkeys.nls": [], "vs/platform/contextkey/common/contextkeys.nls.keys": [], "vs/base/browser/iframe": ["require", "exports"], "vs/base/common/errorMessage": ["require", "exports", "vs/base/common/arrays", "vs/base/common/types", "vs/nls!vs/base/common/errorMessage"], "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess": [], "vs/platform/quickinput/browser/pickerQuickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/lifecycle"], "===anonymous76===": ["vs/platform/quickinput/browser/commandsQuickAccess.nls", "vs/platform/quickinput/browser/commandsQuickAccess.nls.keys"], "vs/platform/quickinput/browser/commandsQuickAccess.nls": [], "vs/platform/quickinput/browser/commandsQuickAccess.nls.keys": [], "vs/base/browser/ui/splitview/splitview": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/scrollable", "vs/base/common/types", "vs/css!vs/base/browser/ui/splitview/splitview"], "vs/editor/contrib/gotoSymbol/peek/referencesTree": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/common/filters", "vs/base/common/labels", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/services/resolverService", "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesTree", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/theme/common/styler", "vs/platform/theme/common/themeService", "vs/editor/contrib/gotoSymbol/referencesModel"], "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesWidget": [], "vs/css!vs/editor/contrib/gotoSymbol/peek/referencesWidget": [], "===anonymous77===": ["vs/editor/contrib/gotoSymbol/peek/referencesWidget.nls", "vs/editor/contrib/gotoSymbol/peek/referencesWidget.nls.keys"], "vs/editor/contrib/gotoSymbol/peek/referencesWidget.nls": [], "vs/editor/contrib/gotoSymbol/peek/referencesWidget.nls.keys": [], "vs/base/browser/ui/list/listPaging": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/browser/ui/list/listWidget", "vs/css!vs/base/browser/ui/list/list"], "vs/base/browser/ui/table/tableWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/splitview/splitview", "vs/base/common/event", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/table/table"], "vs/base/browser/ui/tree/asyncDataTree": ["require", "exports", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/objectTree", "vs/base/browser/ui/tree/tree", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle"], "vs/base/browser/ui/tree/dataTree": ["require", "exports", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/objectTreeModel"], "vs/base/browser/ui/tree/objectTree": ["require", "exports", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/compressedObjectTreeModel", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/common/decorators", "vs/base/common/iterator"], "vs/nls!vs/platform/list/browser/listService": [], "===anonymous78===": ["vs/platform/list/browser/listService.nls", "vs/platform/list/browser/listService.nls.keys"], "vs/platform/list/browser/listService.nls": [], "vs/platform/list/browser/listService.nls.keys": [], "vs/base/worker/defaultWorkerFactory": ["require", "exports", "vs/base/common/platform", "vs/base/common/worker/simpleWorker"], "vs/nls!vs/platform/keybinding/common/abstractKeybindingService": [], "===anonymous79===": ["vs/platform/keybinding/common/abstractKeybindingService.nls", "vs/platform/keybinding/common/abstractKeybindingService.nls.keys"], "vs/platform/keybinding/common/abstractKeybindingService.nls": [], "vs/platform/keybinding/common/abstractKeybindingService.nls.keys": [], "vs/platform/keybinding/common/baseResolvedKeybinding": ["require", "exports", "vs/base/common/errors", "vs/base/common/keybindingLabels", "vs/base/common/keybindings"], "vs/editor/browser/services/codeEditorServiceImpl": ["require", "exports", "vs/editor/browser/services/abstractCodeEditorService", "vs/platform/theme/common/themeService"], "vs/base/common/glob": ["require", "exports", "vs/base/common/async", "vs/base/common/extpath", "vs/base/common/map", "vs/base/common/path", "vs/base/common/strings"], "vs/editor/common/services/languagesRegistry": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/strings", "vs/editor/common/modes/modesRegistry", "vs/editor/common/modes/nullMode", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/editor/common/modes/supports/tokenization": ["require", "exports", "vs/base/common/color"], "vs/editor/standalone/common/themes": ["require", "exports", "vs/editor/common/view/editorColorRegistry", "vs/platform/theme/common/colorRegistry"], "vs/platform/theme/browser/iconsStyleSheet": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService"], "vs/nls!vs/platform/contextkey/browser/contextKeyService": [], "===anonymous80===": ["vs/platform/contextkey/browser/contextKeyService.nls", "vs/platform/contextkey/browser/contextKeyService.nls.keys"], "vs/platform/contextkey/browser/contextKeyService.nls": [], "vs/platform/contextkey/browser/contextKeyService.nls.keys": [], "vs/platform/contextview/browser/contextMenuHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/browser/ui/menu/menu", "vs/base/common/actions", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/platform/theme/common/styler", "vs/css!vs/platform/contextview/browser/contextMenuHandler"], "vs/base/browser/ui/contextview/contextview": ["require", "exports", "vs/base/browser/canIUse", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/range", "vs/css!vs/base/browser/ui/contextview/contextview"], "vs/platform/instantiation/common/descriptors": ["require", "exports"], "vs/platform/instantiation/common/graph": ["require", "exports"], "vs/nls!vs/platform/markers/common/markers": [], "===anonymous81===": ["vs/platform/markers/common/markers.nls", "vs/platform/markers/common/markers.nls.keys"], "vs/platform/markers/common/markers.nls": [], "vs/platform/markers/common/markers.nls.keys": [], "vs/nls!vs/platform/undoRedo/common/undoRedoService": [], "===anonymous82===": ["vs/platform/undoRedo/common/undoRedoService.nls", "vs/platform/undoRedo/common/undoRedoService.nls.keys"], "vs/platform/undoRedo/common/undoRedoService.nls": [], "vs/platform/undoRedo/common/undoRedoService.nls.keys": [], "vs/platform/quickinput/browser/quickInput": ["require", "exports", "vs/base/common/cancellation", "vs/base/parts/quickinput/browser/quickInput", "vs/platform/accessibility/common/accessibility", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/layout/browser/layoutService", "vs/platform/list/browser/listService", "vs/platform/quickinput/browser/quickAccess", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/styler", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput": [], "vs/nls!vs/editor/browser/core/keybindingCancellation": [], "===anonymous83===": ["vs/editor/browser/core/keybindingCancellation.nls", "vs/editor/browser/core/keybindingCancellation.nls.keys"], "vs/editor/browser/core/keybindingCancellation.nls": [], "vs/editor/browser/core/keybindingCancellation.nls.keys": [], "vs/editor/browser/controller/mouseHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/editorDom", "vs/editor/common/config/editorZoom", "vs/editor/common/core/position", "vs/editor/common/core/selection", "vs/editor/common/viewModel/viewEventHandler"], "vs/nls!vs/editor/browser/controller/textAreaHandler": [], "vs/css!vs/editor/browser/controller/textAreaHandler": [], "===anonymous84===": ["vs/editor/browser/controller/textAreaHandler.nls", "vs/editor/browser/controller/textAreaHandler.nls.keys"], "vs/editor/browser/controller/textAreaHandler.nls": [], "vs/editor/browser/controller/textAreaHandler.nls.keys": [], "vs/editor/browser/view/viewLayer": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/common/core/stringBuilder"], "vs/editor/browser/view/dynamicViewOverlay": ["require", "exports", "vs/editor/common/viewModel/viewEventHandler"], "vs/css!vs/editor/browser/viewParts/decorations/decorations": [], "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight": [], "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin": [], "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides": [], "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers": [], "vs/editor/browser/viewParts/lines/viewLine": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/base/common/platform", "vs/editor/browser/viewParts/lines/rangeUtil", "vs/editor/common/view/renderingContext", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/platform/theme/common/theme", "vs/editor/common/config/editorOptions"], "vs/css!vs/editor/browser/viewParts/lines/viewLines": [], "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations": [], "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations": [], "vs/editor/common/core/rgba": ["require", "exports"], "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory": ["require", "exports", "vs/editor/browser/viewParts/minimap/minimapChar<PERSON><PERSON>er", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/editor/browser/viewParts/minimap/minimapPreBaked", "vs/base/common/uint"], "vs/css!vs/editor/browser/viewParts/minimap/minimap": [], "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets": [], "vs/css!vs/editor/browser/viewParts/rulers/rulers": [], "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration": [], "vs/css!vs/editor/browser/viewParts/selections/selections": [], "vs/editor/browser/viewParts/viewCursors/viewCursor": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/common/strings", "vs/editor/browser/config/configuration", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/browser/ui/mouseCursor/mouseCursor"], "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors": [], "vs/editor/common/controller/oneCursor": ["require", "exports", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/base/common/scrollable": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/common/viewLayout/linesLayout": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/viewModel/modelLineProjection": ["require", "exports", "vs/editor/common/core/lineTokens", "vs/editor/common/core/position", "vs/editor/common/model/textModelEvents", "vs/editor/common/viewModel/viewModel"], "vs/base/browser/ui/scrollbar/horizontalScrollbar": ["require", "exports", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/codicons"], "vs/base/browser/ui/scrollbar/verticalScrollbar": ["require", "exports", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/codicons"], "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars": [], "vs/nls!vs/editor/common/model/editStack": [], "vs/editor/common/model/textChange": ["require", "exports", "vs/base/common/buffer", "vs/editor/common/core/stringBuilder"], "===anonymous85===": ["vs/editor/common/model/editStack.nls", "vs/editor/common/model/editStack.nls.keys"], "vs/editor/common/model/editStack.nls": [], "vs/editor/common/model/editStack.nls.keys": [], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase", "vs/editor/common/model/textModelSearch"], "vs/editor/common/model/bracketPairs/bracketPairsTree/bracketPairsTree": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/common/model/bracketPairs/bracketPairs", "vs/editor/common/model/bracketPairs/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairs/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairs/bracketPairsTree/length", "vs/editor/common/model/bracketPairs/bracketPairsTree/parser", "vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairs/bracketPairsTree/tokenizer"], "vs/editor/contrib/codeAction/codeActionMenu": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/errors", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/modes", "vs/editor/contrib/codeAction/codeAction", "vs/editor/contrib/codeAction/types", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding"], "vs/editor/contrib/codeAction/lightBulbWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/touch", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/model/textModel", "vs/nls!vs/editor/contrib/codeAction/lightBulbWidget", "vs/platform/keybinding/common/keybinding", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/codeAction/lightBulbWidget"], "vs/base/common/search": ["require", "exports", "vs/base/common/strings"], "vs/nls!vs/base/browser/ui/findinput/findInputCheckboxes": [], "===anonymous86===": ["vs/base/browser/ui/findinput/findInputCheckboxes.nls", "vs/base/browser/ui/findinput/findInputCheckboxes.nls.keys"], "vs/base/browser/ui/findinput/findInputCheckboxes.nls": [], "vs/base/browser/ui/findinput/findInputCheckboxes.nls.keys": [], "vs/css!vs/base/browser/ui/checkbox/checkbox": [], "vs/base/browser/ui/findinput/findInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInputCheckboxes", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/widget", "vs/base/common/event", "vs/nls!vs/base/browser/ui/findinput/findInput", "vs/css!vs/base/browser/ui/findinput/findInput"], "vs/base/browser/ui/findinput/replaceInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/checkbox/checkbox", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/widget", "vs/base/common/codicons", "vs/base/common/event", "vs/nls!vs/base/browser/ui/findinput/replaceInput", "vs/css!vs/base/browser/ui/findinput/findInput"], "vs/editor/contrib/inlineCompletions/ghostText": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range"], "vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/inlineCompletions/ghostText"], "vs/editor/contrib/inlineCompletions/suggestWidgetInlineCompletionProvider": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/contrib/snippet/snippetParser", "vs/editor/contrib/snippet/snippetSession", "vs/editor/contrib/suggest/suggestController", "vs/editor/contrib/inlineCompletions/inlineCompletionsModel", "vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText", "vs/editor/contrib/inlineCompletions/utils"], "vs/css!vs/editor/contrib/zoneWidget/zoneWidget": [], "vs/base/browser/ui/dropdown/dropdownActionViewItem": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/dropdown/dropdown", "vs/base/common/event", "vs/css!vs/base/browser/ui/dropdown/dropdown"], "vs/base/common/keybindingLabels": ["require", "exports", "vs/nls!vs/base/common/keybindingLabels"], "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem": [], "vs/css!vs/platform/actions/browser/menuEntryActionViewItem": [], "===anonymous87===": ["vs/platform/actions/browser/menuEntryActionViewItem.nls", "vs/platform/actions/browser/menuEntryActionViewItem.nls.keys"], "vs/platform/actions/browser/menuEntryActionViewItem.nls": [], "vs/platform/actions/browser/menuEntryActionViewItem.nls.keys": [], "vs/css!vs/base/browser/ui/hover/hover": [], "vs/editor/contrib/colorPicker/colorPickerModel": ["require", "exports", "vs/base/common/event"], "vs/editor/contrib/colorPicker/colorPickerWidget": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/ui/widget", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/editor/contrib/colorPicker/colorPickerWidget", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/colorPicker/colorPicker"], "vs/nls!vs/editor/contrib/hover/markerHoverParticipant": [], "===anonymous88===": ["vs/editor/contrib/hover/markerHoverParticipant.nls", "vs/editor/contrib/hover/markerHoverParticipant.nls.keys"], "vs/editor/contrib/hover/markerHoverParticipant.nls": [], "vs/editor/contrib/hover/markerHoverParticipant.nls.keys": [], "vs/nls!vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant": [], "===anonymous89===": ["vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant.nls", "vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant.nls.keys"], "vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant.nls": [], "vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant.nls.keys": [], "vs/base/browser/markdownRenderer": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/dompurify/dompurify", "vs/base/browser/event", "vs/base/browser/formattedTextRenderer", "vs/base/browser/mouseEvent", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/iconLabels", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/marked/marked", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/common/objects", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uri"], "vs/base/common/uuid": ["require", "exports"], "vs/nls!vs/editor/contrib/snippet/snippetVariables": [], "vs/platform/workspaces/common/workspaces": ["require", "exports", "vs/base/common/uri", "vs/nls!vs/platform/workspaces/common/workspaces"], "===anonymous90===": ["vs/editor/contrib/snippet/snippetVariables.nls", "vs/editor/contrib/snippet/snippetVariables.nls.keys"], "vs/editor/contrib/snippet/snippetVariables.nls": [], "vs/editor/contrib/snippet/snippetVariables.nls.keys": [], "vs/base/browser/ui/list/splice": ["require", "exports"], "vs/base/browser/ui/list/list": ["require", "exports"], "vs/base/browser/ui/list/listView": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/touch", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/range", "vs/base/common/scrollable", "vs/base/browser/ui/list/rangeMap", "vs/base/browser/ui/list/rowCache"], "vs/css!vs/base/browser/ui/list/list": [], "vs/nls!vs/editor/contrib/suggest/suggestWidgetStatus": [], "===anonymous91===": ["vs/editor/contrib/suggest/suggestWidgetStatus.nls", "vs/editor/contrib/suggest/suggestWidgetStatus.nls.keys"], "vs/editor/contrib/suggest/suggestWidgetStatus.nls": [], "vs/editor/contrib/suggest/suggestWidgetStatus.nls.keys": [], "vs/nls!vs/editor/contrib/suggest/suggestWidgetDetails": [], "===anonymous92===": ["vs/editor/contrib/suggest/suggestWidgetDetails.nls", "vs/editor/contrib/suggest/suggestWidgetDetails.nls.keys"], "vs/editor/contrib/suggest/suggestWidgetDetails.nls": [], "vs/editor/contrib/suggest/suggestWidgetDetails.nls.keys": [], "vs/base/browser/ui/iconLabel/iconLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/range", "vs/css!vs/base/browser/ui/iconLabel/iconlabel"], "vs/editor/common/services/getIconClasses": ["require", "exports", "vs/base/common/network", "vs/base/common/resources", "vs/editor/common/modes/modesRegistry", "vs/platform/files/common/files"], "vs/nls!vs/editor/contrib/suggest/suggestWidgetRenderer": [], "vs/platform/files/common/files": ["require", "exports"], "===anonymous93===": ["vs/editor/contrib/suggest/suggestWidgetRenderer.nls", "vs/editor/contrib/suggest/suggestWidgetRenderer.nls.keys"], "vs/editor/contrib/suggest/suggestWidgetRenderer.nls": [], "vs/editor/contrib/suggest/suggestWidgetRenderer.nls.keys": [], "vs/nls!vs/base/common/errorMessage": [], "===anonymous94===": ["vs/base/common/errorMessage.nls", "vs/base/common/errorMessage.nls.keys"], "vs/base/common/errorMessage.nls": [], "vs/base/common/errorMessage.nls.keys": [], "vs/css!vs/base/browser/ui/splitview/splitview": [], "vs/base/browser/ui/countBadge/countBadge": ["require", "exports", "vs/base/browser/dom", "vs/base/common/color", "vs/base/common/objects", "vs/base/common/strings", "vs/css!vs/base/browser/ui/countBadge/countBadge"], "vs/base/browser/ui/highlightedlabel/highlightedLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/objects"], "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesTree": [], "===anonymous95===": ["vs/editor/contrib/gotoSymbol/peek/referencesTree.nls", "vs/editor/contrib/gotoSymbol/peek/referencesTree.nls.keys"], "vs/editor/contrib/gotoSymbol/peek/referencesTree.nls": [], "vs/editor/contrib/gotoSymbol/peek/referencesTree.nls.keys": [], "vs/css!vs/base/browser/ui/table/table": [], "vs/base/browser/ui/tree/abstractTree": ["require", "exports", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/collections", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/platform", "vs/nls!vs/base/browser/ui/tree/abstractTree", "vs/css!vs/base/browser/ui/tree/media/tree"], "vs/base/browser/ui/tree/indexTreeModel": ["require", "exports", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/diff/diff", "vs/base/common/event", "vs/base/common/iterator"], "vs/base/browser/ui/tree/tree": ["require", "exports"], "vs/base/browser/ui/tree/objectTreeModel": ["require", "exports", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/iterator"], "vs/base/browser/ui/tree/compressedObjectTreeModel": ["require", "exports", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/event", "vs/base/common/iterator"], "vs/editor/browser/services/abstractCodeEditorService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/base/browser/ui/menu/menu": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/touch", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/codicons/codiconStyles", "vs/base/browser/ui/contextview/contextview", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/actions", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/nls!vs/base/browser/ui/menu/menu"], "vs/css!vs/platform/contextview/browser/contextMenuHandler": [], "vs/base/common/range": ["require", "exports"], "vs/css!vs/base/browser/ui/contextview/contextview": [], "vs/base/parts/quickinput/browser/quickInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/button/button", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/browser/ui/progressbar/progressbar", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/severity", "vs/base/common/types", "vs/base/parts/quickinput/browser/quickInputUtils", "vs/base/parts/quickinput/common/quickInput", "vs/nls!vs/base/parts/quickinput/browser/quickInput", "vs/base/parts/quickinput/browser/quickInputBox", "vs/base/parts/quickinput/browser/quickInputList", "vs/css!vs/base/parts/quickinput/browser/media/quickInput"], "vs/platform/quickinput/browser/quickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform"], "vs/editor/browser/viewParts/lines/rangeUtil": ["require", "exports", "vs/editor/common/view/renderingContext"], "vs/editor/browser/viewParts/minimap/minimapCharRenderer": ["require", "exports", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/base/common/uint"], "vs/editor/browser/viewParts/minimap/minimapCharSheet": ["require", "exports"], "vs/editor/browser/viewParts/minimap/minimapPreBaked": ["require", "exports", "vs/base/common/functional"], "vs/base/browser/ui/scrollbar/abstractScrollbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarVisibilityController", "vs/base/browser/ui/widget", "vs/base/common/platform"], "vs/base/browser/ui/scrollbar/scrollbarArrow": ["require", "exports", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/ui/widget", "vs/base/common/async"], "vs/base/browser/ui/scrollbar/scrollbarState": ["require", "exports"], "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase": ["require", "exports"], "vs/editor/common/model/bracketPairs/bracketPairs": ["require", "exports"], "vs/editor/common/model/bracketPairs/bracketPairsTree/beforeEditPositionMapper": ["require", "exports", "vs/editor/common/model/bracketPairs/bracketPairsTree/length"], "vs/editor/common/model/bracketPairs/bracketPairsTree/brackets": ["require", "exports", "vs/base/common/strings", "vs/editor/common/model/bracketPairs/bracketPairsTree/ast", "vs/editor/common/model/bracketPairs/bracketPairsTree/length", "vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairs/bracketPairsTree/tokenizer"], "vs/editor/common/model/bracketPairs/bracketPairsTree/length": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/editor/common/model/bracketPairs/bracketPairsTree/parser": ["require", "exports", "vs/editor/common/model/bracketPairs/bracketPairsTree/ast", "vs/editor/common/model/bracketPairs/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairs/bracketPairsTree/length", "vs/editor/common/model/bracketPairs/bracketPairsTree/concat23Trees", "vs/editor/common/model/bracketPairs/bracketPairsTree/nodeReader"], "vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet": ["require", "exports"], "vs/editor/common/model/bracketPairs/bracketPairsTree/tokenizer": ["require", "exports", "vs/base/common/errors", "vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet", "vs/editor/common/modes", "vs/editor/common/model/bracketPairs/bracketPairsTree/ast", "vs/editor/common/model/bracketPairs/bracketPairsTree/length"], "vs/nls!vs/editor/contrib/codeAction/lightBulbWidget": [], "vs/css!vs/editor/contrib/codeAction/lightBulbWidget": [], "===anonymous96===": ["vs/editor/contrib/codeAction/lightBulbWidget.nls", "vs/editor/contrib/codeAction/lightBulbWidget.nls.keys"], "vs/editor/contrib/codeAction/lightBulbWidget.nls": [], "vs/editor/contrib/codeAction/lightBulbWidget.nls.keys": [], "vs/base/browser/ui/inputbox/inputBox": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/formattedTextRenderer", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/browser/ui/widget", "vs/base/common/color", "vs/base/common/event", "vs/base/common/history", "vs/base/common/objects", "vs/nls!vs/base/browser/ui/inputbox/inputBox", "vs/css!vs/base/browser/ui/inputbox/inputBox"], "vs/nls!vs/base/browser/ui/findinput/findInput": [], "vs/css!vs/base/browser/ui/findinput/findInput": [], "===anonymous97===": ["vs/base/browser/ui/findinput/findInput.nls", "vs/base/browser/ui/findinput/findInput.nls.keys"], "vs/base/browser/ui/findinput/findInput.nls": [], "vs/base/browser/ui/findinput/findInput.nls.keys": [], "vs/nls!vs/base/browser/ui/findinput/replaceInput": [], "===anonymous98===": ["vs/base/browser/ui/findinput/replaceInput.nls", "vs/base/browser/ui/findinput/replaceInput.nls.keys"], "vs/base/browser/ui/findinput/replaceInput.nls": [], "vs/base/browser/ui/findinput/replaceInput.nls.keys": [], "vs/base/browser/ui/dropdown/dropdown": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/common/actions", "vs/base/common/event", "vs/css!vs/base/browser/ui/dropdown/dropdown"], "vs/css!vs/base/browser/ui/dropdown/dropdown": [], "vs/nls!vs/base/common/keybindingLabels": [], "===anonymous99===": ["vs/base/common/keybindingLabels.nls", "vs/base/common/keybindingLabels.nls.keys"], "vs/base/common/keybindingLabels.nls": [], "vs/base/common/keybindingLabels.nls.keys": [], "vs/nls!vs/editor/contrib/colorPicker/colorPickerWidget": [], "vs/css!vs/editor/contrib/colorPicker/colorPicker": [], "===anonymous100===": ["vs/editor/contrib/colorPicker/colorPickerWidget.nls", "vs/editor/contrib/colorPicker/colorPickerWidget.nls.keys"], "vs/editor/contrib/colorPicker/colorPickerWidget.nls": [], "vs/editor/contrib/colorPicker/colorPickerWidget.nls.keys": [], "vs/base/browser/dompurify/dompurify": ["require", "exports", "module"], "vs/base/common/marked/marked": ["require", "exports", "module"], "vs/nls!vs/platform/workspaces/common/workspaces": [], "===anonymous101===": ["vs/platform/workspaces/common/workspaces.nls", "vs/platform/workspaces/common/workspaces.nls.keys"], "vs/platform/workspaces/common/workspaces.nls": [], "vs/platform/workspaces/common/workspaces.nls.keys": [], "vs/base/browser/ui/list/rangeMap": ["require", "exports", "vs/base/common/range"], "vs/base/browser/ui/list/rowCache": ["require", "exports", "vs/base/browser/dom"], "vs/base/browser/ui/iconLabel/iconLabelHover": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/types", "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover"], "vs/css!vs/base/browser/ui/iconLabel/iconlabel": [], "vs/css!vs/base/browser/ui/countBadge/countBadge": [], "vs/nls!vs/base/browser/ui/tree/abstractTree": [], "vs/css!vs/base/browser/ui/tree/media/tree": [], "===anonymous102===": ["vs/base/browser/ui/tree/abstractTree.nls", "vs/base/browser/ui/tree/abstractTree.nls.keys"], "vs/base/browser/ui/tree/abstractTree.nls": [], "vs/base/browser/ui/tree/abstractTree.nls.keys": [], "vs/nls!vs/base/browser/ui/menu/menu": [], "===anonymous103===": ["vs/base/browser/ui/menu/menu.nls", "vs/base/browser/ui/menu/menu.nls.keys"], "vs/base/browser/ui/menu/menu.nls": [], "vs/base/browser/ui/menu/menu.nls.keys": [], "vs/base/browser/ui/button/button": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/css!vs/base/browser/ui/button/button"], "vs/base/browser/ui/progressbar/progressbar": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/color", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/css!vs/base/browser/ui/progressbar/progressbar"], "vs/base/parts/quickinput/browser/quickInputUtils": ["require", "exports", "vs/base/browser/dom", "vs/base/common/idGenerator", "vs/css!vs/base/parts/quickinput/browser/media/quickInput"], "vs/nls!vs/base/parts/quickinput/browser/quickInput": [], "vs/base/parts/quickinput/browser/quickInputBox": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/inputbox/inputBox", "vs/base/common/lifecycle", "vs/base/common/severity", "vs/css!vs/base/parts/quickinput/browser/media/quickInput"], "vs/base/parts/quickinput/browser/quickInputList": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/codicons", "vs/base/common/comparers", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/types", "vs/base/parts/quickinput/browser/quickInputUtils", "vs/nls!vs/base/parts/quickinput/browser/quickInputList", "vs/css!vs/base/parts/quickinput/browser/media/quickInput"], "vs/css!vs/base/parts/quickinput/browser/media/quickInput": [], "===anonymous104===": ["vs/base/parts/quickinput/browser/quickInput.nls", "vs/base/parts/quickinput/browser/quickInput.nls.keys"], "vs/base/parts/quickinput/browser/quickInput.nls": [], "vs/base/parts/quickinput/browser/quickInput.nls.keys": [], "vs/base/browser/ui/scrollbar/scrollbarVisibilityController": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle"], "vs/editor/common/model/bracketPairs/bracketPairsTree/ast": ["require", "exports", "vs/editor/common/controller/cursorColumns", "vs/editor/common/model/bracketPairs/bracketPairsTree/length", "vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet"], "vs/editor/common/model/bracketPairs/bracketPairsTree/concat23Trees": ["require", "exports", "vs/editor/common/model/bracketPairs/bracketPairsTree/ast"], "vs/editor/common/model/bracketPairs/bracketPairsTree/nodeReader": ["require", "exports", "vs/editor/common/model/bracketPairs/bracketPairsTree/length"], "vs/base/common/history": ["require", "exports", "vs/base/common/navigator"], "vs/nls!vs/base/browser/ui/inputbox/inputBox": [], "vs/css!vs/base/browser/ui/inputbox/inputBox": [], "===anonymous105===": ["vs/base/browser/ui/inputbox/inputBox.nls", "vs/base/browser/ui/inputbox/inputBox.nls.keys"], "vs/base/browser/ui/inputbox/inputBox.nls": [], "vs/base/browser/ui/inputbox/inputBox.nls.keys": [], "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover": [], "===anonymous106===": ["vs/base/browser/ui/iconLabel/iconLabelHover.nls", "vs/base/browser/ui/iconLabel/iconLabelHover.nls.keys"], "vs/base/browser/ui/iconLabel/iconLabelHover.nls": [], "vs/base/browser/ui/iconLabel/iconLabelHover.nls.keys": [], "vs/css!vs/base/browser/ui/button/button": [], "vs/css!vs/base/browser/ui/progressbar/progressbar": [], "vs/base/browser/ui/keybindingLabel/keybindingLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/common/keybindingLabels", "vs/base/common/objects", "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel"], "vs/base/common/comparers": ["require", "exports", "vs/base/common/async"], "vs/nls!vs/base/parts/quickinput/browser/quickInputList": [], "===anonymous107===": ["vs/base/parts/quickinput/browser/quickInputList.nls", "vs/base/parts/quickinput/browser/quickInputList.nls.keys"], "vs/base/parts/quickinput/browser/quickInputList.nls": [], "vs/base/parts/quickinput/browser/quickInputList.nls.keys": [], "vs/base/common/navigator": ["require", "exports"], "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel": [], "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel": [], "===anonymous108===": ["vs/base/browser/ui/keybindingLabel/keybindingLabel.nls", "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls.keys"], "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls": [], "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls.keys": []}, "bundles": {"vs/editor/editor.main": ["vs/base/browser/dompurify/dompurify", "vs/base/browser/fastDomNode", "vs/base/browser/iframe", "vs/base/browser/ui/list/list", "vs/base/browser/ui/list/splice", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/assert", "vs/base/common/buffer", "vs/base/common/collections", "vs/base/common/color", "vs/base/common/decorators", "vs/base/common/diff/diffChange", "vs/base/common/errors", "vs/base/common/functional", "vs/base/common/idGenerator", "vs/base/common/iterator", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/marked/marked", "vs/base/common/navigator", "vs/base/common/history", "vs/base/common/numbers", "vs/base/common/platform", "vs/base/common/process", "vs/base/common/path", "vs/base/common/range", "vs/base/browser/ui/list/rangeMap", "vs/base/common/stopwatch", "vs/base/common/event", "vs/base/browser/browser", "vs/base/browser/canIUse", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/common/cancellation", "vs/base/common/async", "vs/base/browser/ui/scrollbar/scrollbarVisibilityController", "vs/base/common/codicons", "vs/base/common/comparers", "vs/base/common/scrollable", "vs/base/common/strings", "vs/base/common/extpath", "vs/base/common/hash", "vs/base/common/diff/diff", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/browser/ui/tree/compressedObjectTreeModel", "vs/base/common/map", "vs/base/common/filters", "vs/base/common/fuzzyScorer", "vs/base/common/glob", "vs/base/common/iconLabels", "vs/base/common/htmlContent", "vs/base/common/search", "vs/base/common/severity", "vs/base/common/types", "vs/base/common/objects", "vs/base/common/uint", "vs/base/common/uri", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/browser/dom", "vs/base/browser/formattedTextRenderer", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/touch", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/list/rowCache", "vs/base/browser/ui/widget", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/horizontalScrollbar", "vs/base/browser/ui/scrollbar/verticalScrollbar", "vs/base/common/resources", "vs/base/browser/markdownRenderer", "vs/base/common/labels", "vs/base/common/mime", "vs/base/browser/dnd", "vs/base/common/uuid", "vs/base/common/worker/simpleWorker", "vs/base/parts/quickinput/common/quickInput", "vs/base/parts/storage/common/storage", "vs/base/worker/defaultWorkerFactory", "vs/css!vs/base/browser/ui/actionbar/actionbar", "vs/css!vs/base/browser/ui/aria/aria", "vs/base/browser/ui/aria/aria", "vs/css!vs/base/browser/ui/button/button", "vs/base/browser/ui/button/button", "vs/css!vs/base/browser/ui/checkbox/checkbox", "vs/base/browser/ui/checkbox/checkbox", "vs/css!vs/base/browser/ui/codicons/codicon/codicon", "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers", "vs/base/browser/ui/codicons/codiconStyles", "vs/css!vs/base/browser/ui/contextview/contextview", "vs/base/browser/ui/contextview/contextview", "vs/css!vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/countBadge/countBadge", "vs/css!vs/base/browser/ui/dropdown/dropdown", "vs/css!vs/base/browser/ui/findinput/findInput", "vs/css!vs/base/browser/ui/hover/hover", "vs/css!vs/base/browser/ui/iconLabel/iconlabel", "vs/css!vs/base/browser/ui/inputbox/inputBox", "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/css!vs/base/browser/ui/list/list", "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/css!vs/base/browser/ui/progressbar/progressbar", "vs/base/browser/ui/progressbar/progressbar", "vs/css!vs/base/browser/ui/sash/sash", "vs/base/browser/ui/sash/sash", "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/browser/ui/hover/hoverWidget", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/list/listPaging", "vs/css!vs/base/browser/ui/splitview/splitview", "vs/base/browser/ui/splitview/splitview", "vs/css!vs/base/browser/ui/table/table", "vs/base/browser/ui/table/tableWidget", "vs/css!vs/base/browser/ui/tree/media/tree", "vs/css!vs/base/parts/quickinput/browser/media/quickInput", "vs/base/parts/quickinput/browser/quickInputUtils", "vs/css!vs/editor/browser/controller/textAreaHandler", "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/css!vs/editor/browser/viewParts/decorations/decorations", "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/css!vs/editor/browser/viewParts/lines/viewLines", "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/css!vs/editor/browser/viewParts/minimap/minimap", "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/css!vs/editor/browser/viewParts/rulers/rulers", "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/css!vs/editor/browser/viewParts/selections/selections", "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/css!vs/editor/browser/widget/media/diffEditor", "vs/css!vs/editor/browser/widget/media/diffReview", "vs/css!vs/editor/browser/widget/media/editor", "vs/css!vs/editor/contrib/anchorSelect/anchorSelect", "vs/css!vs/editor/contrib/bracketMatching/bracketMatching", "vs/css!vs/editor/contrib/codeAction/lightBulbWidget", "vs/css!vs/editor/contrib/codelens/codelensWidget", "vs/css!vs/editor/contrib/colorPicker/colorPicker", "vs/css!vs/editor/contrib/dnd/dnd", "vs/css!vs/editor/contrib/find/findWidget", "vs/css!vs/editor/contrib/folding/folding", "vs/css!vs/editor/contrib/gotoError/media/gotoErrorWidget", "vs/css!vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition", "vs/css!vs/editor/contrib/gotoSymbol/peek/referencesWidget", "vs/css!vs/editor/contrib/inlineCompletions/ghostText", "vs/css!vs/editor/contrib/links/links", "vs/css!vs/editor/contrib/message/messageController", "vs/css!vs/editor/contrib/parameterHints/parameterHints", "vs/css!vs/editor/contrib/peekView/media/peekViewWidget", "vs/css!vs/editor/contrib/rename/renameInputField", "vs/css!vs/editor/contrib/snippet/snippetSession", "vs/css!vs/editor/contrib/suggest/media/suggest", "vs/css!vs/editor/contrib/unicodeHighlighter/bannerController", "vs/css!vs/editor/contrib/unicodeHighlighter/unicodeHighlighter", "vs/css!vs/editor/contrib/zoneWidget/zoneWidget", "vs/css!vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp", "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput", "vs/css!vs/editor/standalone/browser/standalone-tokens", "vs/css!vs/platform/actions/browser/menuEntryActionViewItem", "vs/css!vs/platform/contextview/browser/contextMenuHandler", "vs/editor/browser/config/elementSizeObserver", "vs/editor/browser/services/abstractCodeEditorService", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/editor/browser/viewParts/minimap/minimapChar<PERSON><PERSON>er", "vs/editor/browser/viewParts/minimap/minimapPreBaked", "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory", "vs/editor/common/config/editorZoom", "vs/editor/common/config/fontInfo", "vs/editor/common/controller/cursorColumns", "vs/editor/common/core/characterClassifier", "vs/editor/common/controller/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/browser/controller/textAreaState", "vs/editor/browser/widget/diffNavigator", "vs/editor/common/core/editOperation", "vs/editor/common/commands/trimTrailingWhitespaceCommand", "vs/editor/common/core/rgba", "vs/editor/common/core/selection", "vs/editor/browser/controller/textAreaInput", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/core/stringBuilder", "vs/editor/browser/view/viewLayer", "vs/editor/common/core/token", "vs/editor/common/diff/diffComputer", "vs/editor/common/editorAction", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/browser/editorBrowser", "vs/editor/common/model", "vs/editor/common/model/bracketPairs/bracketPairs", "vs/editor/common/model/bracketPairs/bracketPairsTree/length", "vs/editor/common/model/bracketPairs/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairs/bracketPairsTree/nodeReader", "vs/editor/common/model/bracketPairs/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairs/bracketPairsTree/ast", "vs/editor/common/model/bracketPairs/bracketPairsTree/concat23Trees", "vs/editor/common/model/bracketPairs/bracketPairsTree/parser", "vs/editor/common/model/indentationG<PERSON>ser", "vs/editor/common/model/intervalTree", "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase", "vs/editor/common/model/textChange", "vs/editor/common/model/textModelEvents", "vs/editor/common/model/textModelSearch", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/wordHelper", "vs/editor/common/modes/languageConfiguration", "vs/editor/common/modes/languageSelector", "vs/editor/common/modes/linkComputer", "vs/editor/common/modes/nullMode", "vs/editor/common/modes/supports", "vs/editor/common/modes/supports/characterPair", "vs/editor/common/modes/supports/indentRules", "vs/editor/common/modes/supports/inplaceReplaceSupport", "vs/editor/common/modes/supports/onEnter", "vs/editor/common/modes/supports/richEditBrackets", "vs/editor/common/modes/supports/electricCharacter", "vs/editor/common/modes/supports/tokenization", "vs/editor/common/modes/tokenizationRegistry", "vs/editor/common/modes/unicodeTextModelHighlighter", "vs/editor/common/services/semanticTokensDto", "vs/editor/common/standalone/standaloneEnums", "vs/editor/common/standalone/standaloneBase", "vs/editor/common/view/overviewZoneManager", "vs/editor/common/view/renderingContext", "vs/editor/browser/viewParts/lines/rangeUtil", "vs/editor/common/view/viewContext", "vs/editor/common/view/viewEvents", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/linesLayout", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewLayout/viewLinesViewportData", "vs/editor/common/viewModel/modelLineProjectionData", "vs/editor/common/viewModel/monospaceLineBreaksComputer", "vs/editor/common/viewModel/prefixSumComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/services/editorSimpleWorker", "vs/editor/common/viewModel/viewEventHandler", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/contentWidgets/contentWidgets", "vs/editor/browser/viewParts/decorations/decorations", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/editor/browser/viewParts/margin/margin", "vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/editor/browser/viewParts/overviewRuler/overviewRuler", "vs/editor/browser/viewParts/viewZones/viewZones", "vs/editor/common/viewModel/viewModel", "vs/editor/common/viewModel/viewModelEventDispatcher", "vs/editor/common/viewLayout/viewLayout", "vs/editor/contrib/caretOperations/moveCaretCommand", "vs/editor/contrib/codeAction/types", "vs/editor/contrib/colorPicker/colorPickerModel", "vs/editor/contrib/dnd/dragAndDropCommand", "vs/editor/contrib/find/replaceAllCommand", "vs/editor/contrib/find/replacePattern", "vs/editor/contrib/folding/foldingRanges", "vs/editor/contrib/folding/foldingModel", "vs/editor/contrib/folding/syntaxRangeProvider", "vs/editor/contrib/folding/intializingRangeProvider", "vs/editor/contrib/format/formattingEdit", "vs/editor/contrib/gotoSymbol/link/clickLinkGesture", "vs/editor/contrib/hover/hoverOperation", "vs/editor/contrib/hover/hoverTypes", "vs/editor/contrib/inPlaceReplace/inPlaceReplaceCommand", "vs/editor/contrib/indentation/indentUtils", "vs/editor/contrib/inlineCompletions/consts", "vs/editor/contrib/inlineCompletions/ghostText", "vs/editor/contrib/inlineCompletions/inlineCompletionToGhostText", "vs/editor/contrib/inlineCompletions/utils", "vs/editor/contrib/linesOperations/copyLinesCommand", "vs/editor/contrib/linesOperations/sortLinesCommand", "vs/editor/contrib/smartSelect/bracketSelections", "vs/editor/contrib/smartSelect/wordSelections", "vs/editor/contrib/snippet/snippetParser", "vs/editor/contrib/suggest/completionModel", "vs/editor/contrib/suggest/resizable", "vs/editor/contrib/suggest/suggestCommitCharacters", "vs/editor/contrib/suggest/suggestOvertypingCapturer", "vs/editor/contrib/suggest/wordDistance", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon", "vs/editor/standalone/common/monarch/monarchCompile", "vs/nls!vs/base/browser/ui/actionbar/actionViewItems", "vs/nls!vs/base/browser/ui/findinput/findInput", "vs/nls!vs/base/browser/ui/findinput/findInputCheckboxes", "vs/base/browser/ui/findinput/findInputCheckboxes", "vs/nls!vs/base/browser/ui/findinput/replaceInput", "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/browser/ui/iconLabel/iconLabel", "vs/nls!vs/base/browser/ui/inputbox/inputBox", "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/nls!vs/base/browser/ui/menu/menu", "vs/nls!vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/dataTree", "vs/base/browser/ui/tree/objectTree", "vs/base/browser/ui/tree/asyncDataTree", "vs/nls!vs/base/common/actions", "vs/base/common/actions", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/dropdown/dropdown", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/findinput/replaceInput", "vs/base/browser/ui/menu/menu", "vs/base/parts/quickinput/browser/quickInputBox", "vs/nls!vs/base/common/errorMessage", "vs/base/common/errorMessage", "vs/nls!vs/base/common/keybindingLabels", "vs/base/common/keybindingLabels", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/nls!vs/base/parts/quickinput/browser/quickInput", "vs/nls!vs/base/parts/quickinput/browser/quickInputList", "vs/base/parts/quickinput/browser/quickInputList", "vs/base/parts/quickinput/browser/quickInput", "vs/nls!vs/editor/browser/controller/coreCommands", "vs/nls!vs/editor/browser/controller/textAreaHandler", "vs/nls!vs/editor/browser/core/keybindingCancellation", "vs/nls!vs/editor/browser/editorExtensions", "vs/nls!vs/editor/browser/widget/codeEditorWidget", "vs/nls!vs/editor/browser/widget/diffEditorWidget", "vs/nls!vs/editor/browser/widget/diffReview", "vs/nls!vs/editor/browser/widget/inlineDiffMargin", "vs/editor/browser/widget/inlineDiffMargin", "vs/nls!vs/editor/common/config/commonEditorConfig", "vs/nls!vs/editor/common/config/editorOptions", "vs/editor/common/config/editorOptions", "vs/editor/browser/config/charWidthReader", "vs/editor/common/viewModel/viewModelDecorations", "vs/nls!vs/editor/common/editorContextKeys", "vs/nls!vs/editor/common/model/editStack", "vs/editor/common/model/editStack", "vs/nls!vs/editor/common/modes/modesRegistry", "vs/nls!vs/editor/common/standaloneStrings", "vs/editor/common/standaloneStrings", "vs/nls!vs/editor/common/view/editorColorRegistry", "vs/nls!vs/editor/contrib/anchorSelect/anchorSelect", "vs/nls!vs/editor/contrib/bracketMatching/bracketMatching", "vs/nls!vs/editor/contrib/caretOperations/caretOperations", "vs/nls!vs/editor/contrib/caretOperations/transpose", "vs/nls!vs/editor/contrib/clipboard/clipboard", "vs/nls!vs/editor/contrib/codeAction/codeActionCommands", "vs/nls!vs/editor/contrib/codeAction/lightBulbWidget", "vs/nls!vs/editor/contrib/codelens/codelensController", "vs/nls!vs/editor/contrib/colorPicker/colorPickerWidget", "vs/nls!vs/editor/contrib/comment/comment", "vs/nls!vs/editor/contrib/contextmenu/contextmenu", "vs/nls!vs/editor/contrib/cursorUndo/cursorUndo", "vs/nls!vs/editor/contrib/find/findController", "vs/nls!vs/editor/contrib/find/findWidget", "vs/nls!vs/editor/contrib/folding/folding", "vs/nls!vs/editor/contrib/folding/foldingDecorations", "vs/nls!vs/editor/contrib/fontZoom/fontZoom", "vs/nls!vs/editor/contrib/format/format", "vs/nls!vs/editor/contrib/format/formatActions", "vs/nls!vs/editor/contrib/gotoError/gotoError", "vs/nls!vs/editor/contrib/gotoError/gotoErrorWidget", "vs/nls!vs/editor/contrib/gotoSymbol/goToCommands", "vs/nls!vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition", "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesController", "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesTree", "vs/nls!vs/editor/contrib/gotoSymbol/peek/referencesWidget", "vs/nls!vs/editor/contrib/gotoSymbol/referencesModel", "vs/editor/contrib/gotoSymbol/referencesModel", "vs/nls!vs/editor/contrib/gotoSymbol/symbolNavigation", "vs/nls!vs/editor/contrib/hover/hover", "vs/nls!vs/editor/contrib/hover/markdownHoverParticipant", "vs/nls!vs/editor/contrib/hover/markerHoverParticipant", "vs/nls!vs/editor/contrib/inPlaceReplace/inPlaceReplace", "vs/nls!vs/editor/contrib/indentation/indentation", "vs/nls!vs/editor/contrib/inlineCompletions/ghostTextController", "vs/nls!vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant", "vs/nls!vs/editor/contrib/lineSelection/lineSelection", "vs/nls!vs/editor/contrib/linesOperations/linesOperations", "vs/nls!vs/editor/contrib/linkedEditing/linkedEditing", "vs/nls!vs/editor/contrib/links/links", "vs/nls!vs/editor/contrib/message/messageController", "vs/nls!vs/editor/contrib/multicursor/multicursor", "vs/nls!vs/editor/contrib/parameterHints/parameterHints", "vs/nls!vs/editor/contrib/parameterHints/parameterHintsWidget", "vs/nls!vs/editor/contrib/peekView/peekView", "vs/nls!vs/editor/contrib/quickAccess/gotoLineQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/gotoSymbolQuickAccess", "vs/nls!vs/editor/contrib/rename/rename", "vs/nls!vs/editor/contrib/rename/renameInputField", "vs/nls!vs/editor/contrib/smartSelect/smartSelect", "vs/nls!vs/editor/contrib/snippet/snippetController2", "vs/nls!vs/editor/contrib/snippet/snippetVariables", "vs/nls!vs/editor/contrib/suggest/suggest", "vs/nls!vs/editor/contrib/suggest/suggestController", "vs/nls!vs/editor/contrib/suggest/suggestWidget", "vs/nls!vs/editor/contrib/suggest/suggestWidgetDetails", "vs/nls!vs/editor/contrib/suggest/suggestWidgetR<PERSON>er", "vs/nls!vs/editor/contrib/suggest/suggestWidgetStatus", "vs/nls!vs/editor/contrib/symbolIcons/symbolIcons", "vs/nls!vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode", "vs/nls!vs/editor/contrib/tokenization/tokenization", "vs/nls!vs/editor/contrib/unicodeHighlighter/unicodeHighlighter", "vs/nls!vs/editor/contrib/unusualLineTerminators/unusualLineTerminators", "vs/nls!vs/editor/contrib/wordHighlighter/wordHighlighter", "vs/nls!vs/editor/contrib/wordOperations/wordOperations", "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem", "vs/nls!vs/platform/configuration/common/configurationRegistry", "vs/nls!vs/platform/contextkey/browser/contextKeyService", "vs/nls!vs/platform/contextkey/common/contextkeys", "vs/nls!vs/platform/keybinding/common/abstractKeybindingService", "vs/nls!vs/platform/list/browser/listService", "vs/nls!vs/platform/markers/common/markers", "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess", "vs/nls!vs/platform/quickinput/browser/helpQuickAccess", "vs/nls!vs/platform/theme/common/colorRegistry", "vs/nls!vs/platform/theme/common/iconRegistry", "vs/nls!vs/platform/undoRedo/common/undoRedoService", "vs/nls!vs/platform/workspaces/common/workspaces", "vs/platform/browser/historyWidgetKeybindingHint", "vs/platform/clipboard/browser/clipboardService", "vs/platform/editor/common/editor", "vs/platform/extensions/common/extensions", "vs/platform/files/common/files", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/graph", "vs/platform/instantiation/common/instantiation", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/common/services/editorWorkerService", "vs/editor/common/services/markersDecorationService", "vs/editor/common/services/modeService", "vs/editor/common/services/modelService", "vs/editor/common/modes/languageFeatureRegistry", "vs/editor/common/modes", "vs/editor/common/core/lineTokens", "vs/editor/common/model/bracketPairs/bracketPairsTree/tokenizer", "vs/editor/common/model/bracketPairs/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairs/bracketPairsTree/bracketPairsTree", "vs/editor/common/model/bracketPairs/bracketPairsImpl", "vs/editor/common/model/tokensStore", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder", "vs/editor/common/model/textModelTokens", "vs/editor/common/modes/textToHtmlTokenizer", "vs/editor/common/services/resolverService", "vs/editor/common/services/textResourceConfigurationService", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel/modelLineProjection", "vs/editor/contrib/documentSymbols/outlineModel", "vs/editor/contrib/folding/hiddenRangeModel", "vs/editor/standalone/common/monarch/monarchLexer", "vs/editor/standalone/browser/colorizer", "vs/editor/standalone/common/standaloneThemeService", "vs/platform/clipboard/common/clipboardService", "vs/platform/commands/common/commands", "vs/editor/common/services/getSemanticTokens", "vs/editor/contrib/codelens/codelens", "vs/editor/contrib/colorPicker/color", "vs/editor/contrib/documentSymbols/documentSymbols", "vs/editor/contrib/links/getLinks", "vs/platform/configuration/common/configuration", "vs/editor/common/modes/languageConfigurationRegistry", "vs/editor/contrib/comment/blockCommentCommand", "vs/editor/contrib/comment/lineCommentCommand", "vs/platform/contextkey/common/contextkey", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/parameterHints/provideSignatureHelp", "vs/editor/contrib/parameterHints/parameterHintsModel", "vs/editor/contrib/suggest/suggestAlternatives", "vs/editor/contrib/suggest/wordContextKey", "vs/platform/accessibility/common/accessibility", "vs/platform/accessibility/browser/accessibilityService", "vs/platform/contextkey/common/contextkeys", "vs/platform/contextview/browser/contextView", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/serviceCollection", "vs/platform/instantiation/common/instantiationService", "vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/baseResolvedKeybinding", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingResolver", "vs/platform/contextkey/browser/contextKeyService", "vs/platform/keybinding/common/resolvedKeybindingItem", "vs/platform/keybinding/common/usLayoutResolvedKeybinding", "vs/platform/label/common/label", "vs/platform/layout/browser/layoutService", "vs/platform/contextview/browser/contextViewService", "vs/platform/log/common/log", "vs/editor/common/services/editorWorkerServiceImpl", "vs/editor/common/services/webWorker", "vs/platform/markers/common/markers", "vs/editor/contrib/gotoError/markerNavigationService", "vs/platform/markers/common/markerService", "vs/platform/notification/common/notification", "vs/platform/opener/common/opener", "vs/editor/browser/services/openerService", "vs/platform/progress/common/progress", "vs/platform/quickinput/browser/pickerQuickAccess", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/configuration/common/configurationRegistry", "vs/editor/common/config/commonEditorConfig", "vs/editor/browser/config/configuration", "vs/editor/browser/core/markdownRenderer", "vs/editor/browser/view/domLineBreaksComputer", "vs/editor/browser/view/viewOverlays", "vs/editor/browser/viewParts/viewCursors/viewCursor", "vs/editor/common/modes/modesRegistry", "vs/editor/common/services/getIconClasses", "vs/editor/common/services/languagesRegistry", "vs/editor/common/services/modeServiceImpl", "vs/editor/contrib/hover/modesGlyphHover", "vs/editor/contrib/suggest/suggestWidgetDetails", "vs/platform/configuration/common/configurationModels", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/browser/helpQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess", "vs/platform/quickinput/browser/quickAccess", "vs/platform/storage/common/storage", "vs/editor/contrib/codelens/codeLensCache", "vs/editor/contrib/suggest/suggestMemory", "vs/platform/telemetry/common/telemetry", "vs/platform/quickinput/browser/commandsQuickAccess", "vs/editor/contrib/quickAccess/commandsQuickAccess", "vs/platform/theme/common/colorRegistry", "vs/editor/browser/editorDom", "vs/platform/theme/common/styler", "vs/platform/contextview/browser/contextMenuHandler", "vs/platform/theme/common/theme", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/browser/viewParts/lines/viewLines", "vs/platform/theme/common/themeService", "vs/editor/browser/services/codeEditorServiceImpl", "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar", "vs/editor/browser/viewParts/minimap/minimap", "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/editor/browser/viewParts/selections/selections", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/common/view/editorColorRegistry", "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/controller/text<PERSON><PERSON>Hand<PERSON>", "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler", "vs/editor/browser/viewParts/rulers/rulers", "vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/editor/common/model/bracketPairs/colorizedBracketPairsDecorationProvider", "vs/editor/common/services/markerDecorationsServiceImpl", "vs/editor/contrib/colorPicker/colorPickerWidget", "vs/editor/contrib/gotoSymbol/peek/referencesTree", "vs/editor/contrib/inlineCompletions/ghostTextWidget", "vs/editor/contrib/quickAccess/editorNavigationQuickAccess", "vs/editor/contrib/quickAccess/gotoLineQuickAccess", "vs/editor/contrib/quickAccess/gotoSymbolQuickAccess", "vs/editor/contrib/rename/renameInputField", "vs/editor/contrib/symbolIcons/symbolIcons", "vs/editor/standalone/browser/standaloneCodeServiceImpl", "vs/editor/standalone/common/themes", "vs/platform/actions/common/actions", "vs/editor/browser/editorExtensions", "vs/editor/browser/core/keybindingCancellation", "vs/editor/browser/core/editorState", "vs/editor/browser/services/markerDecorations", "vs/editor/contrib/anchorSelect/anchorSelect", "vs/editor/contrib/caretOperations/caretOperations", "vs/editor/contrib/clipboard/clipboard", "vs/editor/contrib/codeAction/codeAction", "vs/editor/contrib/codeAction/codeActionMenu", "vs/editor/contrib/codeAction/codeActionModel", "vs/editor/contrib/comment/comment", "vs/editor/contrib/contextmenu/contextmenu", "vs/editor/contrib/cursorUndo/cursorUndo", "vs/editor/contrib/fontZoom/fontZoom", "vs/editor/contrib/format/format", "vs/editor/contrib/format/formatActions", "vs/editor/contrib/gotoSymbol/goToSymbol", "vs/editor/contrib/gotoSymbol/symbolNavigation", "vs/editor/contrib/hover/getHover", "vs/editor/contrib/hover/markdownHoverParticipant", "vs/editor/contrib/inlayHints/inlayHintsController", "vs/editor/contrib/message/messageController", "vs/editor/contrib/rename/rename", "vs/editor/contrib/smartSelect/smartSelect", "vs/editor/contrib/suggest/suggest", "vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode", "vs/editor/contrib/tokenization/tokenization", "vs/editor/contrib/unusualLineTerminators/unusualLineTerminators", "vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp", "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess", "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/editor/contrib/suggest/suggestWidgetStatus", "vs/platform/actions/common/menuService", "vs/platform/browser/contextScopedHistoryWidget", "vs/platform/contextview/browser/contextMenuService", "vs/platform/list/browser/listService", "vs/platform/opener/browser/link", "vs/platform/quickinput/browser/quickInput", "vs/editor/standalone/browser/quickInput/standaloneQuickInputServiceImpl", "vs/platform/severityIcon/common/severityIcon", "vs/platform/theme/common/iconRegistry", "vs/editor/browser/widget/diffReview", "vs/editor/contrib/parameterHints/parameterHintsWidget", "vs/editor/contrib/parameterHints/parameterHints", "vs/editor/contrib/suggest/suggestWidget<PERSON><PERSON><PERSON>", "vs/editor/contrib/unicodeHighlighter/bannerController", "vs/platform/theme/browser/iconsStyleSheet", "vs/editor/standalone/browser/standaloneThemeServiceImpl", "vs/platform/undoRedo/common/undoRedo", "vs/editor/common/model/textModel", "vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/editor/common/controller/cursor<PERSON>ommon", "vs/editor/common/commands/shiftCommand", "vs/editor/common/controller/cursorAtomicMoveOperations", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/controller/mouseHandler", "vs/editor/browser/controller/pointerHandler", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/common/controller/cursorColumnSelection", "vs/editor/common/controller/cursorMoveOperations", "vs/editor/common/controller/cursorDeleteOperations", "vs/editor/common/controller/cursorTypeOperations", "vs/editor/common/controller/cursorWordOperations", "vs/editor/common/controller/cursorMoveCommands", "vs/editor/browser/controller/coreCommands", "vs/editor/browser/view/viewController", "vs/editor/browser/view/viewImpl", "vs/editor/common/controller/oneCursor", "vs/editor/common/controller/cursorCollection", "vs/editor/common/controller/cursor", "vs/editor/common/services/modelServiceImpl", "vs/editor/common/viewModel/viewModelLines", "vs/editor/common/viewModel/viewModelImpl", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditorWidget", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/bracketMatching/bracketMatching", "vs/editor/contrib/caretOperations/transpose", "vs/editor/contrib/codeAction/lightBulbWidget", "vs/editor/contrib/codeAction/codeActionUi", "vs/editor/contrib/codeAction/codeActionCommands", "vs/editor/contrib/codeAction/codeActionContributions", "vs/editor/contrib/codelens/codelensWidget", "vs/editor/contrib/codelens/codelensController", "vs/editor/contrib/colorPicker/colorDetector", "vs/editor/contrib/dnd/dnd", "vs/editor/contrib/find/findDecorations", "vs/editor/contrib/find/findModel", "vs/editor/contrib/find/findOptionsWidget", "vs/editor/contrib/find/findState", "vs/editor/contrib/find/findWidget", "vs/editor/contrib/find/findController", "vs/editor/contrib/folding/foldingDecorations", "vs/editor/contrib/folding/indentRange<PERSON>rovider", "vs/editor/contrib/folding/folding", "vs/editor/contrib/hover/colorHoverParticipant", "vs/editor/contrib/inPlaceReplace/inPlaceReplace", "vs/editor/contrib/indentation/indentation", "vs/editor/contrib/inlineCompletions/inlineCompletionsModel", "vs/editor/contrib/lineSelection/lineSelection", "vs/editor/contrib/linesOperations/moveLinesCommand", "vs/editor/contrib/linesOperations/linesOperations", "vs/editor/contrib/linkedEditing/linkedEditing", "vs/editor/contrib/links/links", "vs/editor/contrib/multicursor/multicursor", "vs/editor/contrib/suggest/suggestWidget", "vs/editor/contrib/viewportSemanticTokens/viewportSemanticTokens", "vs/editor/contrib/wordHighlighter/wordHighlighter", "vs/editor/contrib/wordOperations/wordOperations", "vs/editor/contrib/wordPartOperations/wordPartOperations", "vs/editor/contrib/zoneWidget/zoneWidget", "vs/editor/contrib/peekView/peekView", "vs/editor/contrib/gotoError/gotoErrorWidget", "vs/editor/contrib/gotoError/gotoError", "vs/editor/contrib/gotoSymbol/peek/referencesWidget", "vs/editor/contrib/gotoSymbol/peek/referencesController", "vs/editor/contrib/gotoSymbol/goToCommands", "vs/editor/contrib/gotoSymbol/link/goToDefinitionAtPosition", "vs/editor/contrib/hover/markerHoverParticipant", "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch", "vs/platform/undoRedo/common/undoRedoService", "vs/platform/workspace/common/workspace", "vs/editor/standalone/browser/simpleServices", "vs/editor/standalone/browser/standaloneCodeEditor", "vs/platform/workspace/common/workspaceTrust", "vs/editor/contrib/unicodeHighlighter/unicodeHighlighter", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/browser/standaloneEditor", "vs/editor/standalone/browser/standaloneLanguages", "vs/editor/editor.api", "vs/platform/workspaces/common/workspaces", "vs/editor/contrib/snippet/snippetVariables", "vs/editor/contrib/snippet/snippetSession", "vs/editor/contrib/snippet/snippetController2", "vs/editor/contrib/suggest/suggestModel", "vs/editor/contrib/suggest/suggestController", "vs/editor/contrib/inlineCompletions/suggestWidgetInlineCompletionProvider", "vs/editor/contrib/inlineCompletions/suggestWidgetPreviewModel", "vs/editor/contrib/inlineCompletions/ghostTextModel", "vs/editor/contrib/inlineCompletions/ghostTextController", "vs/editor/contrib/inlineCompletions/inlineCompletionsHoverParticipant", "vs/editor/contrib/hover/modesContentHover", "vs/editor/contrib/hover/hover", "vs/editor/contrib/colorPicker/colorContributions", "vs/editor/editor.all", "vs/editor/editor.main"], "vs/base/common/worker/simpleWorker": ["vs/base/common/arrays", "vs/base/common/diff/diffChange", "vs/base/common/errors", "vs/base/common/functional", "vs/base/common/iterator", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/platform", "vs/base/common/process", "vs/base/common/path", "vs/base/common/stopwatch", "vs/base/common/event", "vs/base/common/cancellation", "vs/base/common/strings", "vs/base/common/hash", "vs/base/common/diff/diff", "vs/base/common/types", "vs/base/common/objects", "vs/base/common/uint", "vs/base/common/uri", "vs/base/common/worker/simpleWorker", "vs/editor/common/core/characterClassifier", "vs/editor/common/controller/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/core/token", "vs/editor/common/diff/diffComputer", "vs/editor/common/model", "vs/editor/common/model/textModelSearch", "vs/editor/common/model/wordHelper", "vs/editor/common/modes/linkComputer", "vs/editor/common/modes/supports/inplaceReplaceSupport", "vs/editor/common/modes/unicodeTextModelHighlighter", "vs/editor/common/standalone/standaloneEnums", "vs/editor/common/standalone/standaloneBase", "vs/editor/common/viewModel/prefixSumComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/services/editorSimpleWorker"]}}